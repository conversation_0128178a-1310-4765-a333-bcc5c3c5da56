# Rich Text Editor

A modern, extensible rich text editor built with vanilla JavaScript, CSS, and HTML.

## Features

- Clean, modern interface with light/dark theme support
- Basic text formatting (bold, italic)
- Undo/redo functionality with history management
- Modern clipboard integration
- Plugin architecture using Web Components
- Responsive design that adapts to different devices

## Project Structure

```
rich-text-editor/
├── src/
│   ├── clipboard/
│   │   └── clipboard-manager.js      # Handles copy/paste operations
│   ├── editor/
│   │   ├── core.js                   # Core editor initialization
│   │   └── formatting.js             # Text formatting functions
│   ├── history/
│   │   └── history-manager.js        # Undo/redo functionality
│   ├── plugins/
│   │   ├── basic-formatting.js       # Basic formatting buttons
│   │   ├── history-controls.js       # Undo/redo buttons
│   │   ├── plugin-manager.js         # Plugin system
│   │   └── toolbar-button.js         # Custom toolbar button element
│   ├── styles/
│   │   ├── base.css                  # Base styles
│   │   ├── index.css                 # CSS entry point
│   │   ├── plugins.css               # Plugin-specific styles
│   │   └── theme.css                 # Theming system
│   ├── themes/
│   │   └── theme-manager.js          # Theme switching functionality
│   └── main.js                       # Application entry point
└── index.html                        # Main HTML file
```

## Creating Plugins

Plugins can be created to extend the functionality of the editor. Each plugin should have:

1. An `init(editor)` method that sets up the plugin
2. An optional `destroy()` method for cleanup

Example plugin:

```javascript
const MyPlugin = {
  init(editor) {
    // Initialize plugin
    console.log('My plugin initialized');
    
    // Create UI elements, register event handlers, etc.
    const button = document.createElement('toolbar-button');
    button.setAttribute('data-command', 'myCommand');
    button.setAttribute('data-icon', '✓');
    
    document.getElementById('toolbar').appendChild(button);
    
    // Handle custom commands
    document.addEventListener('toolbar-action', (event) => {
      if (event.detail.command === 'myCommand') {
        // Do something
      }
    });
  },
  
  destroy() {
    // Clean up
    console.log('My plugin destroyed');
    // Remove event listeners, UI elements, etc.
  }
};

// Register the plugin
pluginManager.register('my-plugin', MyPlugin);
```

## Theming

The editor supports light and dark themes out of the box. Themes are implemented using CSS custom properties, making it easy to create new themes or modify existing ones.

To create a custom theme:

1. Add a new class to `theme.css` (e.g., `.theme-custom`)
2. Override the CSS custom properties
3. Add the theme to the available themes in `ThemeManager`

## Development

To get started with Bun:

```bash
bun install           # install dependencies
bun run dev           # start development server
bun run build         # build for production
bun run test          # run tests