---
trigger: always_on
---

Use bun as package manager
Ensure typescript is used
Ensure eslint is used
Ensure prettier is used
Ensure vitest is used
Ensure vite is used
Write unit and integration tests
Test happy and sad paths, including edge cases
Ensure all implementations are SOLID (Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion)
Ensure all code is type-safe
Ensure all code is performant
Use OOP principles
When fixing unit tests, ensure the feature to be tested is actually working as expected, hence do not just blindly force tests to pass
Ensure to use browser-native APIs where possible
If any file is going to be more than 500 lines of code, maybe they need to be split into multiple files, or refactored to be more maintainable else ensure to implement it in chunks
