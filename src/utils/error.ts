/**
 * FeatherJS Error Handling System
 * A comprehensive error handling system for FeatherJS editor and plugins.
 * Uses a hierarchical error class structure following SOLID principles.
 */

/**
 * Base error class for all FeatherJS errors
 * Extends the native Error class with additional functionality
 */
export class FeatherError extends Error {
  /** Error code for categorization */
  public code: string;
  /** Timestamp when the error occurred */
  public timestamp: Date;
  /** Optional data related to the error */
  public data?: unknown;
  /** Stack trace of the error */
  public override stack: string;

  constructor(message: string, code = 'FEATHER_ERROR', data?: unknown) {
    super(message);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, FeatherError.prototype);
    
    // Maintain proper stack traces for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
    
    this.name = this.constructor.name;
    this.code = code;
    this.timestamp = new Date();
    this.data = data;
    this.stack = new Error().stack || '';
  }

  /**
   * Returns a formatted error message including timestamp and code
   */
  public formattedMessage(): string {
    return `[${this.timestamp.toISOString()}] ${this.code}: ${this.message}`;
  }

  /**
   * Converts the error to a plain object for serialization
   */
  public toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
      data: this.data
    };
  }
}

/**
 * Error class for validation errors
 */
export class ValidationError extends FeatherError {
  /** The field that failed validation */
  public field?: string;
  /** The validation constraints that failed */
  public constraints?: string[];

  constructor(message: string, field?: string, constraints?: string[], data?: unknown) {
    super(message, 'VALIDATION_ERROR', data);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, ValidationError.prototype);
    
    this.field = field;
    this.constraints = constraints;
  }

  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      field: this.field,
      constraints: this.constraints
    };
  }
}

/**
 * Error class for plugin-related errors
 */
export class PluginError extends FeatherError {
  /** The ID of the plugin that threw the error */
  public pluginId: string;
  
  constructor(message: string, pluginId: string, data?: unknown) {
    super(message, 'PLUGIN_ERROR', data);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, PluginError.prototype);
    
    this.pluginId = pluginId;
  }

  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      pluginId: this.pluginId
    };
  }
}

/**
 * Error class for editor state errors
 */
export class StateError extends FeatherError {
  constructor(message: string, data?: unknown) {
    super(message, 'STATE_ERROR', data);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, StateError.prototype);
  }
}

/**
 * Error class for network or IO-related errors
 */
export class NetworkError extends FeatherError {
  /** HTTP status code (if applicable) */
  public statusCode?: number;
  /** Request URL (if applicable) */
  public url?: string;

  constructor(message: string, statusCode?: number, url?: string, data?: unknown) {
    super(message, 'NETWORK_ERROR', data);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, NetworkError.prototype);
    
    this.statusCode = statusCode;
    this.url = url;
  }

  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      statusCode: this.statusCode,
      url: this.url
    };
  }
}

/**
 * Error class for DOM manipulation errors
 */
export class DOMError extends FeatherError {
  /** The DOM element that caused the error */
  public element?: HTMLElement | null;
  /** The selector that was used (if applicable) */
  public selector?: string;

  constructor(message: string, element?: HTMLElement | null, selector?: string, data?: unknown) {
    super(message, 'DOM_ERROR', data);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, DOMError.prototype);
    
    this.element = element;
    this.selector = selector;
  }

  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      selector: this.selector,
      elementId: this.element?.id,
      elementClassName: this.element?.className
    };
  }
}

/**
 * Error class for initialization errors
 */
export class InitializationError extends FeatherError {
  /** The component that failed to initialize */
  public component: string;

  constructor(message: string, component: string, data?: unknown) {
    super(message, 'INIT_ERROR', data);
    
    // Set the prototype explicitly.
    Object.setPrototypeOf(this, InitializationError.prototype);
    
    this.component = component;
  }

  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      component: this.component
    };
  }
}

/**
 * Error handler utility functions
 */
export const ErrorHandler = {
  /**
   * Safely tries to execute a function and handle any errors
   * @param fn The function to execute
   * @param errorHandler The error handler callback
   * @returns The result of the function or void
   */
  try<T>(fn: () => T, errorHandler?: (error: unknown) => void): T | void {
    try {
      return fn();
    } catch (error) {
      if (errorHandler) {
        errorHandler(error);
      }
    }
  },

  /**
   * Wraps an async function with error handling
   * @param fn The async function to wrap
   * @param errorHandler The error handler callback
   * @returns A promise of the result or void
   */
  async tryAsync<T>(fn: () => Promise<T>, errorHandler?: (error: unknown) => void): Promise<T | void> {
    try {
      return await fn();
    } catch (error) {
      if (errorHandler) {
        errorHandler(error);
      }
    }
  },

  /**
   * Converts an unknown error to a FeatherError
   * @param error The error to convert
   * @returns A FeatherError
   */
  normalizeError(error: unknown): FeatherError {
    if (error instanceof FeatherError) {
      return error;
    }
    
    if (error instanceof Error) {
      const featherError = new FeatherError(error.message);
      featherError.stack = error.stack || featherError.stack;
      return featherError;
    }
    
    return new FeatherError(
      typeof error === 'string' 
        ? error 
        : 'An unknown error occurred'
    );
  }
};
