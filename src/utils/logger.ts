/**
 * FeatherJS Logging System
 * A lightweight, configurable logging system for FeatherJS that works in conjunction with the error handling system.
 * Follows SOLID principles with a modular design.
 */

import { FeatherError, <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from './error';

/**
 * Log levels in order of severity
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
  SILENT = 5,
}

/**
 * String representation of log levels for display
 */
export const LogLevelName: Record<LogLevel, string> = {
  [LogLevel.DEBUG]: 'DEBUG',
  [LogLevel.INFO]: 'INFO',
  [LogLevel.WARN]: 'WARN',
  [LogLevel.ERROR]: 'ERROR',
  [LogLevel.FATAL]: 'FATAL',
  [LogLevel.SILENT]: 'SILENT',
};

/**
 * Interface for log entries
 */
export interface LogEntry {
  /** Log message */
  message: string;
  /** Log level */
  level: LogLevel;
  /** Timestamp when the log was created */
  timestamp: Date;
  /** Source of the log (component, plugin ID, etc.) */
  source?: string;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
  /** Associated error object, if any */
  error?: Error | FeatherError;
}

/**
 * Interface for log transports (destinations for log output)
 */
export interface LogTransport {
  /** Processes a log entry */
  log(entry: LogEntry): void;
  /** Minimum log level to process */
  minLevel: LogLevel;
}

/**
 * Console transport implementation
 */
export class ConsoleTransport implements LogTransport {
  /** Minimum log level to process */
  public minLevel: LogLevel;
  /** Whether to include timestamps in console output */
  private includeTimestamp: boolean;
  /** Whether to include source in console output */
  private includeSource: boolean;
  /** Whether to use colors in console output */
  private useColors: boolean;

  constructor(options: {
    minLevel?: LogLevel;
    includeTimestamp?: boolean;
    includeSource?: boolean;
    useColors?: boolean;
  } = {}) {
    this.minLevel = options.minLevel ?? LogLevel.INFO;
    this.includeTimestamp = options.includeTimestamp ?? true;
    this.includeSource = options.includeSource ?? true;
    this.useColors = options.useColors ?? true;
  }

  /**
   * Log color map for console output
   */
  private readonly colors: Record<LogLevel, string> = {
    [LogLevel.DEBUG]: '\x1b[37m', // White
    [LogLevel.INFO]: '\x1b[32m',  // Green
    [LogLevel.WARN]: '\x1b[33m',  // Yellow
    [LogLevel.ERROR]: '\x1b[31m', // Red
    [LogLevel.FATAL]: '\x1b[35m', // Magenta
    [LogLevel.SILENT]: '',        // No color
  };

  /**
   * Reset color code
   */
  private readonly resetColor = '\x1b[0m';

  /**
   * Process a log entry for console output
   */
  public log(entry: LogEntry): void {
    if (entry.level < this.minLevel) {
      return;
    }

    const { message, level, timestamp, source, metadata, error } = entry;
    const color = this.useColors ? this.colors[level] : '';
    const reset = this.useColors ? this.resetColor : '';
    
    let logMessage = '';
    
    // Build the log prefix
    if (this.includeTimestamp) {
      logMessage += `[${timestamp.toISOString()}] `;
    }
    
    logMessage += `${color}${LogLevelName[level]}${reset}: `;
    
    if (this.includeSource && source) {
      logMessage += `[${source}] `;
    }
    
    // Add the message
    logMessage += message;
    
    // Choose the appropriate console method
    let consoleMethod: (message?: string, ...optionalParams: unknown[]) => void;
    
    switch (level) {
      case LogLevel.DEBUG:
        consoleMethod = console.debug;
        break;
      case LogLevel.INFO:
        consoleMethod = console.info;
        break;
      case LogLevel.WARN:
        consoleMethod = console.warn;
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        consoleMethod = console.error;
        break;
      default:
        consoleMethod = console.log;
    }
    
    // Log the message
    if (metadata && Object.keys(metadata).length > 0) {
      consoleMethod(logMessage, metadata);
    } else {
      consoleMethod(logMessage);
    }
    
    // Log the error stack if present
    if (error?.stack) {
      console.error(error.stack);
    }
  }
}

/**
 * Memory transport that keeps logs in memory for retrieval
 */
export class MemoryTransport implements LogTransport {
  /** Minimum log level to process */
  public minLevel: LogLevel;
  /** Maximum number of entries to keep */
  private maxEntries: number;
  /** Stored log entries */
  private entries: LogEntry[] = [];

  constructor(options: { minLevel?: LogLevel; maxEntries?: number } = {}) {
    this.minLevel = options.minLevel ?? LogLevel.DEBUG;
    this.maxEntries = options.maxEntries ?? 1000;
  }

  /**
   * Store a log entry
   */
  public log(entry: LogEntry): void {
    if (entry.level < this.minLevel) {
      return;
    }
    
    this.entries.push(entry);
    
    // Trim if we exceed the maximum number of entries
    if (this.entries.length > this.maxEntries) {
      this.entries = this.entries.slice(-this.maxEntries);
    }
  }

  /**
   * Get all stored log entries
   */
  public getEntries(): ReadonlyArray<LogEntry> {
    return this.entries;
  }

  /**
   * Clear all stored log entries
   */
  public clear(): void {
    this.entries = [];
  }

  /**
   * Filter entries by criteria
   */
  public filter(
    criteria: Partial<{
      level: LogLevel;
      minLevel: LogLevel;
      source: string;
      search: string;
      from: Date;
      to: Date;
    }>
  ): ReadonlyArray<LogEntry> {
    return this.entries.filter((entry) => {
      if (criteria.level !== undefined && entry.level !== criteria.level) {
        return false;
      }
      
      if (criteria.minLevel !== undefined && entry.level < criteria.minLevel) {
        return false;
      }
      
      if (criteria.source && entry.source !== criteria.source) {
        return false;
      }
      
      if (criteria.search && !entry.message.includes(criteria.search)) {
        return false;
      }
      
      if (criteria.from && entry.timestamp < criteria.from) {
        return false;
      }
      
      if (criteria.to && entry.timestamp > criteria.to) {
        return false;
      }
      
      return true;
    });
  }
}

/**
 * Main Logger class
 */
export class Logger {
  /** Log transports */
  private transports: LogTransport[] = [];
  /** Default log level */
  private level: LogLevel;
  /** Source identifier for this logger */
  private source: string;
  /** Global metadata to include with all logs */
  private globalMetadata: Record<string, unknown> = {};

  constructor(options: { 
    level?: LogLevel; 
    source?: string; 
    transports?: LogTransport[];
    metadata?: Record<string, unknown>;
  } = {}) {
    this.level = options.level ?? LogLevel.INFO;
    this.source = options.source ?? 'FeatherJS';
    this.transports = options.transports ?? [new ConsoleTransport()];
    this.globalMetadata = options.metadata ?? {};
  }

  /**
   * Creates a log entry and sends it to all transports
   */
  private createLog(
    level: LogLevel, 
    message: string, 
    metadata?: Record<string, unknown>, 
    error?: Error | FeatherError
  ): void {
    if (level < this.level) {
      return;
    }

    const entry: LogEntry = {
      message,
      level,
      timestamp: new Date(),
      source: this.source,
      metadata: {
        ...this.globalMetadata,
        ...metadata
      },
      error
    };

    this.transports.forEach(transport => {
      ErrorHandler.try(() => transport.log(entry));
    });
  }

  /**
   * Logs a debug message
   */
  public debug(message: string, metadata?: Record<string, unknown>): void {
    this.createLog(LogLevel.DEBUG, message, metadata);
  }

  /**
   * Logs an info message
   */
  public info(message: string, metadata?: Record<string, unknown>): void {
    this.createLog(LogLevel.INFO, message, metadata);
  }

  /**
   * Logs a warning message
   */
  public warn(message: string, metadata?: Record<string, unknown>, error?: Error | FeatherError): void {
    this.createLog(LogLevel.WARN, message, metadata, error);
  }

  /**
   * Logs an error message
   */
  public error(message: string, error?: unknown, metadata?: Record<string, unknown>): void {
    const normalizedError = error instanceof Error ? error : ErrorHandler.normalizeError(error);
    this.createLog(LogLevel.ERROR, message, metadata, normalizedError);
  }

  /**
   * Logs a fatal error message
   */
  public fatal(message: string, error?: unknown, metadata?: Record<string, unknown>): void {
    const normalizedError = error instanceof Error ? error : ErrorHandler.normalizeError(error);
    this.createLog(LogLevel.FATAL, message, metadata, normalizedError);
  }

  /**
   * Logs an entry for an error object
   */
  public logError(error: unknown, level = LogLevel.ERROR, metadata?: Record<string, unknown>): void {
    const normalizedError = error instanceof Error ? error : ErrorHandler.normalizeError(error);
    const message = normalizedError.message || 'An error occurred';
    this.createLog(level, message, metadata, normalizedError);
  }

  /**
   * Creates a child logger with a different source
   */
  public createChild(source: string, additionalMetadata?: Record<string, unknown>): Logger {
    return new Logger({
      level: this.level,
      source: `${this.source}:${source}`,
      transports: this.transports,
      metadata: {
        ...this.globalMetadata,
        ...additionalMetadata
      }
    });
  }

  /**
   * Adds a transport to the logger
   */
  public addTransport(transport: LogTransport): void {
    this.transports.push(transport);
  }

  /**
   * Removes a transport from the logger
   */
  public removeTransport(transport: LogTransport): void {
    this.transports = this.transports.filter(t => t !== transport);
  }

  /**
   * Changes the minimum log level for this logger
   */
  public setLevel(level: LogLevel): void {
    this.level = level;
  }

  /**
   * Gets the current log level
   */
  public getLevel(): LogLevel {
    return this.level;
  }

  /**
   * Adds metadata to all future log entries
   */
  public addGlobalMetadata(metadata: Record<string, unknown>): void {
    this.globalMetadata = {
      ...this.globalMetadata,
      ...metadata
    };
  }
}

/**
 * Default logger instance
 */
export const logger = new Logger();

/**
 * Utility function to measure function execution time and log it
 */
export function measureTime<T>(
  fn: () => T,
  name: string,
  logger?: Logger
): T {
  const start = performance.now();
  const result = fn();
  const duration = performance.now() - start;
  
  if (logger) {
    logger.debug(`${name} execution time: ${duration.toFixed(2)}ms`);
  } else {
    console.debug(`${name} execution time: ${duration.toFixed(2)}ms`);
  }
  
  return result;
}

/**
 * Async utility function to measure function execution time and log it
 */
export async function measureTimeAsync<T>(
  fn: () => Promise<T>,
  name: string,
  logger?: Logger
): Promise<T> {
  const start = performance.now();
  const result = await fn();
  const duration = performance.now() - start;
  
  if (logger) {
    logger.debug(`${name} execution time: ${duration.toFixed(2)}ms`);
  } else {
    console.debug(`${name} execution time: ${duration.toFixed(2)}ms`);
  }
  
  return result;
}
