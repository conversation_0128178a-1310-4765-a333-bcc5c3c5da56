/**
 * Unit tests for the FeatherJS Logging System
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  LogLevel, 
  LogLevelName, 
  ConsoleTransport, 
  MemoryTransport, 
  Logger,
  measureTime,
  measureTimeAsync
} from '../logger';
import { FeatherError } from '../error';

describe('LogLevel', () => {
  it('should define log levels in order of increasing severity', () => {
    expect(LogLevel.DEBUG).toBeLessThan(LogLevel.INFO);
    expect(LogLevel.INFO).toBeLessThan(LogLevel.WARN);
    expect(LogLevel.WARN).toBeLessThan(LogLevel.ERROR);
    expect(LogLevel.ERROR).toBeLessThan(LogLevel.FATAL);
    expect(LogLevel.FATAL).toBeLessThan(LogLevel.SILENT);
  });
  
  it('should have matching name entries for all log levels', () => {
    expect(Object.keys(LogLevelName).length).toBe(Object.keys(LogLevel).length / 2); // Enum creates reverse mapping
    
    expect(LogLevelName[LogLevel.DEBUG]).toBe('DEBUG');
    expect(LogLevelName[LogLevel.INFO]).toBe('INFO');
    expect(LogLevelName[LogLevel.WARN]).toBe('WARN');
    expect(LogLevelName[LogLevel.ERROR]).toBe('ERROR');
    expect(LogLevelName[LogLevel.FATAL]).toBe('FATAL');
    expect(LogLevelName[LogLevel.SILENT]).toBe('SILENT');
  });
});

describe('ConsoleTransport', () => {
  let originalConsole: typeof console;
  
  beforeEach(() => {
    // Store original console methods
    originalConsole = {
      log: console.log,
      debug: console.debug,
      info: console.info,
      warn: console.warn,
      error: console.error
    } as typeof console;
    
    // Mock console methods
    console.log = vi.fn();
    console.debug = vi.fn();
    console.info = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
  });
  
  afterEach(() => {
    // Restore original console methods
    console.log = originalConsole.log;
    console.debug = originalConsole.debug;
    console.info = originalConsole.info;
    console.warn = originalConsole.warn;
    console.error = originalConsole.error;
  });
  
  it('should use constructor defaults when not provided', () => {
    const transport = new ConsoleTransport();
    
    expect(transport.minLevel).toBe(LogLevel.INFO);
  });
  
  it('should respect custom minimum log level', () => {
    const transport = new ConsoleTransport({ minLevel: LogLevel.ERROR });
    
    expect(transport.minLevel).toBe(LogLevel.ERROR);
  });
  
  it('should not log messages below minimum level', () => {
    const transport = new ConsoleTransport({ minLevel: LogLevel.WARN });
    
    transport.log({
      message: 'Debug message',
      level: LogLevel.DEBUG,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Info message',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    expect(console.debug).not.toHaveBeenCalled();
    expect(console.info).not.toHaveBeenCalled();
  });
  
  it('should log messages at or above minimum level', () => {
    const transport = new ConsoleTransport({ minLevel: LogLevel.WARN, useColors: false });
    
    transport.log({
      message: 'Warning message',
      level: LogLevel.WARN,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Error message',
      level: LogLevel.ERROR,
      timestamp: new Date()
    });
    
    expect(console.warn).toHaveBeenCalled();
    expect(console.error).toHaveBeenCalled();
  });
  
  it('should include source when provided', () => {
    const transport = new ConsoleTransport({ useColors: false });
    
    transport.log({
      message: 'Test message',
      level: LogLevel.INFO,
      timestamp: new Date(),
      source: 'TestComponent'
    });
    
    expect(console.info).toHaveBeenCalledWith(expect.stringContaining('[TestComponent]'));
  });
  
  it('should log metadata when provided', () => {
    const transport = new ConsoleTransport();
    const metadata = { userId: '123', action: 'test' };
    
    transport.log({
      message: 'Message with metadata',
      level: LogLevel.INFO,
      timestamp: new Date(),
      metadata
    });
    
    expect(console.info).toHaveBeenCalledWith(expect.any(String), metadata);
  });
  
  it('should log error stack when provided', () => {
    const transport = new ConsoleTransport();
    const error = new Error('Test error');
    
    transport.log({
      message: 'Error occurred',
      level: LogLevel.ERROR,
      timestamp: new Date(),
      error
    });
    
    expect(console.error).toHaveBeenCalledWith(expect.stringContaining('ERROR'));
    expect(console.error).toHaveBeenCalledWith(error.stack);
  });
});

describe('MemoryTransport', () => {
  it('should use constructor defaults when not provided', () => {
    const transport = new MemoryTransport();
    
    expect(transport.minLevel).toBe(LogLevel.DEBUG);
    expect(transport.getEntries()).toHaveLength(0);
  });
  
  it('should store log entries above minimum level', () => {
    const transport = new MemoryTransport({ minLevel: LogLevel.WARN });
    
    transport.log({
      message: 'Debug message',
      level: LogLevel.DEBUG,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Error message',
      level: LogLevel.ERROR,
      timestamp: new Date()
    });
    
    const entries = transport.getEntries();
    expect(entries).toHaveLength(1);
    expect(entries[0].level).toBe(LogLevel.ERROR);
  });
  
  it('should respect maximum entries limit', () => {
    const transport = new MemoryTransport({ maxEntries: 2 });
    
    transport.log({
      message: 'First',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Second',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Third',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    const entries = transport.getEntries();
    expect(entries).toHaveLength(2);
    expect(entries[0].message).toBe('Second');
    expect(entries[1].message).toBe('Third');
  });
  
  it('should clear all entries', () => {
    const transport = new MemoryTransport();
    
    transport.log({
      message: 'Test',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    expect(transport.getEntries()).toHaveLength(1);
    
    transport.clear();
    
    expect(transport.getEntries()).toHaveLength(0);
  });
  
  it('should filter entries by level', () => {
    const transport = new MemoryTransport();
    
    transport.log({
      message: 'Debug',
      level: LogLevel.DEBUG,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Info',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Error',
      level: LogLevel.ERROR,
      timestamp: new Date()
    });
    
    const infoEntries = transport.filter({ level: LogLevel.INFO });
    expect(infoEntries).toHaveLength(1);
    expect(infoEntries[0].message).toBe('Info');
    
    const highSeverityEntries = transport.filter({ minLevel: LogLevel.WARN });
    expect(highSeverityEntries).toHaveLength(1);
    expect(highSeverityEntries[0].message).toBe('Error');
  });
  
  it('should filter entries by source', () => {
    const transport = new MemoryTransport();
    
    transport.log({
      message: 'Component A',
      level: LogLevel.INFO,
      timestamp: new Date(),
      source: 'ComponentA'
    });
    
    transport.log({
      message: 'Component B',
      level: LogLevel.INFO,
      timestamp: new Date(),
      source: 'ComponentB'
    });
    
    const componentAEntries = transport.filter({ source: 'ComponentA' });
    expect(componentAEntries).toHaveLength(1);
    expect(componentAEntries[0].message).toBe('Component A');
  });
  
  it('should filter entries by search term', () => {
    const transport = new MemoryTransport();
    
    transport.log({
      message: 'Success message',
      level: LogLevel.INFO,
      timestamp: new Date()
    });
    
    transport.log({
      message: 'Error occurred',
      level: LogLevel.ERROR,
      timestamp: new Date()
    });
    
    const errorEntries = transport.filter({ search: 'Error' });
    expect(errorEntries).toHaveLength(1);
    expect(errorEntries[0].message).toBe('Error occurred');
  });
  
  it('should filter entries by date range', () => {
    const transport = new MemoryTransport();
    const now = new Date();
    const past = new Date(now.getTime() - 10000);
    const future = new Date(now.getTime() + 10000);
    
    transport.log({
      message: 'Past',
      level: LogLevel.INFO,
      timestamp: past
    });
    
    transport.log({
      message: 'Future',
      level: LogLevel.INFO,
      timestamp: future
    });
    
    const fromNowEntries = transport.filter({ from: now });
    expect(fromNowEntries).toHaveLength(1);
    expect(fromNowEntries[0].message).toBe('Future');
    
    const untilNowEntries = transport.filter({ to: now });
    expect(untilNowEntries).toHaveLength(1);
    expect(untilNowEntries[0].message).toBe('Past');
  });
});

describe('Logger', () => {
  let memoryTransport: MemoryTransport;
  let consoleTransport: ConsoleTransport;
  let logger: Logger;
  
  beforeEach(() => {
    // Mock console methods
    console.log = vi.fn();
    console.debug = vi.fn();
    console.info = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
    
    memoryTransport = new MemoryTransport();
    consoleTransport = new ConsoleTransport({ useColors: false });
    
    logger = new Logger({
      level: LogLevel.DEBUG,
      source: 'TestLogger',
      transports: [memoryTransport, consoleTransport]
    });
  });
  
  it('should create a logger with default values', () => {
    const defaultLogger = new Logger();
    
    expect(defaultLogger.getLevel()).toBe(LogLevel.INFO);
  });
  
  it('should log messages at different levels', () => {
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    logger.error('Error message');
    logger.fatal('Fatal message');
    
    const entries = memoryTransport.getEntries();
    expect(entries).toHaveLength(5);
    
    expect(entries[0].level).toBe(LogLevel.DEBUG);
    expect(entries[1].level).toBe(LogLevel.INFO);
    expect(entries[2].level).toBe(LogLevel.WARN);
    expect(entries[3].level).toBe(LogLevel.ERROR);
    expect(entries[4].level).toBe(LogLevel.FATAL);
  });
  
  it('should not log messages below minimum level', () => {
    logger.setLevel(LogLevel.WARN);
    
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    
    const entries = memoryTransport.getEntries();
    expect(entries).toHaveLength(1);
    expect(entries[0].message).toBe('Warning message');
  });
  
  it('should include source in log entries', () => {
    logger.info('Test message');
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].source).toBe('TestLogger');
  });
  
  it('should include metadata in log entries', () => {
    const metadata = { userId: '123', action: 'test' };
    
    logger.info('Message with metadata', metadata);
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].metadata).toEqual(metadata);
  });
  
  it('should normalize errors when logging', () => {
    const error = new Error('Test error');
    
    logger.error('Error occurred', error);
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].error).toBeDefined();
    expect(entries[0].error?.message).toBe('Test error');
  });
  
  it('should log an error directly', () => {
    const error = new FeatherError('Direct error');
    
    logger.logError(error);
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].level).toBe(LogLevel.ERROR);
    expect(entries[0].message).toBe('Direct error');
    expect(entries[0].error).toBe(error);
  });
  
  it('should create a child logger with inherited settings', () => {
    const childLogger = logger.createChild('Child');
    
    childLogger.info('Child message');
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].source).toBe('TestLogger:Child');
  });
  
  it('should add global metadata to all log entries', () => {
    logger.addGlobalMetadata({ app: 'FeatherJS', version: '1.0.0' });
    
    logger.info('Message with global metadata');
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].metadata).toEqual({
      app: 'FeatherJS',
      version: '1.0.0'
    });
  });
  
  it('should merge global metadata with entry-specific metadata', () => {
    logger.addGlobalMetadata({ app: 'FeatherJS', version: '1.0.0' });
    
    logger.info('Message with merged metadata', { userId: '123' });
    
    const entries = memoryTransport.getEntries();
    expect(entries[0].metadata).toEqual({
      app: 'FeatherJS',
      version: '1.0.0',
      userId: '123'
    });
  });
  
  it('should add and remove transports', () => {
    const newTransport = new MemoryTransport();
    
    logger.addTransport(newTransport);
    logger.info('Message to all transports');
    
    expect(newTransport.getEntries()).toHaveLength(1);
    
    logger.removeTransport(newTransport);
    logger.info('Message to remaining transports');
    
    expect(newTransport.getEntries()).toHaveLength(1); // No change
  });
});

describe('Performance measurement utilities', () => {
  beforeEach(() => {
    console.debug = vi.fn();
  });
  
  it('should measure sync function execution time', () => {
    const result = measureTime(() => {
      // Simulate work
      let sum = 0;
      for (let i = 0; i < 1000; i++) {
        sum += i;
      }
      return sum;
    }, 'TestFunction');
    
    expect(result).toBe(499500);
    expect(console.debug).toHaveBeenCalledWith(expect.stringContaining('TestFunction execution time:'));
  });
  
  it('should measure async function execution time', async () => {
    const result = await measureTimeAsync(async () => {
      // Simulate async work
      await new Promise(resolve => setTimeout(resolve, 5));
      return 'done';
    }, 'AsyncFunction');
    
    expect(result).toBe('done');
    expect(console.debug).toHaveBeenCalledWith(expect.stringContaining('AsyncFunction execution time:'));
  });
  
  it('should use provided logger if available', () => {
    const mockLogger = {
      debug: vi.fn()
    } as unknown as Logger;
    
    measureTime(() => 'result', 'TestWithLogger', mockLogger as Logger);
    
    expect(mockLogger.debug).toHaveBeenCalledWith(expect.stringContaining('TestWithLogger execution time:'));
    expect(console.debug).not.toHaveBeenCalled();
  });
});
