/**
 * Unit tests for the FeatherJS Error Handling System
 */
import { describe, it, expect, vi } from 'vitest';
import { 
  FeatherError, 
  ValidationError, 
  PluginError, 
  StateError, 
  NetworkError, 
  DOMError, 
  InitializationError,
  ErrorHandler 
} from '../error';

describe('FeatherError', () => {
  it('should create a basic error with default values', () => {
    const error = new FeatherError('Test error');
    
    expect(error).toBeInstanceOf(Error);
    expect(error).toBeInstanceOf(FeatherError);
    expect(error.message).toBe('Test error');
    expect(error.code).toBe('FEATHER_ERROR');
    expect(error.timestamp).toBeInstanceOf(Date);
    expect(error.data).toBeUndefined();
    expect(error.stack).toBeDefined();
    expect(error.name).toBe('FeatherError');
  });
  
  it('should create an error with custom code and data', () => {
    const data = { foo: 'bar' };
    const error = new FeatherError('Custom error', 'CUSTOM_CODE', data);
    
    expect(error.message).toBe('Custom error');
    expect(error.code).toBe('CUSTOM_CODE');
    expect(error.data).toBe(data);
  });
  
  it('should generate a formatted message', () => {
    const error = new FeatherError('Formatted error');
    const formatted = error.formattedMessage();
    
    expect(formatted).toContain(error.timestamp.toISOString());
    expect(formatted).toContain('FEATHER_ERROR');
    expect(formatted).toContain('Formatted error');
  });
  
  it('should convert to JSON correctly', () => {
    const error = new FeatherError('JSON error');
    const json = error.toJSON();
    
    expect(json.name).toBe('FeatherError');
    expect(json.code).toBe('FEATHER_ERROR');
    expect(json.message).toBe('JSON error');
    expect(json.timestamp).toBe(error.timestamp.toISOString());
    expect(json.stack).toBeDefined();
  });
});

describe('ValidationError', () => {
  it('should create a validation error with field and constraints', () => {
    const error = new ValidationError('Invalid value', 'username', ['required', 'minLength']);
    
    expect(error).toBeInstanceOf(FeatherError);
    expect(error).toBeInstanceOf(ValidationError);
    expect(error.code).toBe('VALIDATION_ERROR');
    expect(error.field).toBe('username');
    expect(error.constraints).toEqual(['required', 'minLength']);
  });
  
  it('should include field and constraints in JSON output', () => {
    const error = new ValidationError('Invalid email', 'email', ['email']);
    const json = error.toJSON();
    
    expect(json.field).toBe('email');
    expect(json.constraints).toEqual(['email']);
  });
});

describe('PluginError', () => {
  it('should create a plugin error with pluginId', () => {
    const error = new PluginError('Plugin failed', 'bold-plugin');
    
    expect(error).toBeInstanceOf(FeatherError);
    expect(error).toBeInstanceOf(PluginError);
    expect(error.code).toBe('PLUGIN_ERROR');
    expect(error.pluginId).toBe('bold-plugin');
  });
  
  it('should include pluginId in JSON output', () => {
    const error = new PluginError('Plugin crashed', 'table-plugin');
    const json = error.toJSON();
    
    expect(json.pluginId).toBe('table-plugin');
  });
});

describe('StateError', () => {
  it('should create a state error', () => {
    const error = new StateError('Invalid state');
    
    expect(error).toBeInstanceOf(FeatherError);
    expect(error).toBeInstanceOf(StateError);
    expect(error.code).toBe('STATE_ERROR');
  });
});

describe('NetworkError', () => {
  it('should create a network error with status code and URL', () => {
    const error = new NetworkError('Request failed', 404, 'https://example.com/api');
    
    expect(error).toBeInstanceOf(FeatherError);
    expect(error).toBeInstanceOf(NetworkError);
    expect(error.code).toBe('NETWORK_ERROR');
    expect(error.statusCode).toBe(404);
    expect(error.url).toBe('https://example.com/api');
  });
  
  it('should include status code and URL in JSON output', () => {
    const error = new NetworkError('Timeout', 408, 'https://example.com/data');
    const json = error.toJSON();
    
    expect(json.statusCode).toBe(408);
    expect(json.url).toBe('https://example.com/data');
  });
});

describe('DOMError', () => {
  it('should create a DOM error with element and selector', () => {
    const element = document.createElement('div');
    const error = new DOMError('Element not found', element, '#editor');
    
    expect(error).toBeInstanceOf(FeatherError);
    expect(error).toBeInstanceOf(DOMError);
    expect(error.code).toBe('DOM_ERROR');
    expect(error.element).toBe(element);
    expect(error.selector).toBe('#editor');
  });
  
  it('should include element properties in JSON output', () => {
    const element = document.createElement('div');
    element.id = 'test-id';
    element.className = 'test-class';
    
    const error = new DOMError('Invalid operation', element);
    const json = error.toJSON();
    
    expect(json.elementId).toBe('test-id');
    expect(json.elementClassName).toBe('test-class');
  });
});

describe('InitializationError', () => {
  it('should create an initialization error with component name', () => {
    const error = new InitializationError('Failed to initialize', 'EditorCore');
    
    expect(error).toBeInstanceOf(FeatherError);
    expect(error).toBeInstanceOf(InitializationError);
    expect(error.code).toBe('INIT_ERROR');
    expect(error.component).toBe('EditorCore');
  });
  
  it('should include component in JSON output', () => {
    const error = new InitializationError('Config missing', 'ThemeManager');
    const json = error.toJSON();
    
    expect(json.component).toBe('ThemeManager');
  });
});

describe('ErrorHandler', () => {
  it('should try a function and return its result', () => {
    const result = ErrorHandler.try(() => 'success');
    expect(result).toBe('success');
  });
  
  it('should catch errors and call the error handler', () => {
    const errorHandler = vi.fn();
    const error = new Error('Test error');
    
    const result = ErrorHandler.try(() => {
      throw error;
    }, errorHandler);
    
    expect(result).toBeUndefined();
    expect(errorHandler).toHaveBeenCalledWith(error);
  });
  
  it('should try an async function and return its resolved value', async () => {
    const result = await ErrorHandler.tryAsync(async () => 'async success');
    expect(result).toBe('async success');
  });
  
  it('should catch async errors and call the error handler', async () => {
    const errorHandler = vi.fn();
    const error = new Error('Async error');
    
    const result = await ErrorHandler.tryAsync(async () => {
      throw error;
    }, errorHandler);
    
    expect(result).toBeUndefined();
    expect(errorHandler).toHaveBeenCalledWith(error);
  });
  
  it('should normalize a FeatherError to itself', () => {
    const original = new FeatherError('Original error');
    const normalized = ErrorHandler.normalizeError(original);
    
    expect(normalized).toBe(original);
  });
  
  it('should normalize a standard Error to a FeatherError', () => {
    const original = new Error('Standard error');
    const normalized = ErrorHandler.normalizeError(original);
    
    expect(normalized).toBeInstanceOf(FeatherError);
    expect(normalized.message).toBe('Standard error');
    expect(normalized.stack).toBe(original.stack);
  });
  
  it('should normalize a string to a FeatherError', () => {
    const normalized = ErrorHandler.normalizeError('String error');
    
    expect(normalized).toBeInstanceOf(FeatherError);
    expect(normalized.message).toBe('String error');
  });
  
  it('should normalize an unknown value to a FeatherError with generic message', () => {
    const normalized = ErrorHandler.normalizeError(null);
    
    expect(normalized).toBeInstanceOf(FeatherError);
    expect(normalized.message).toBe('An unknown error occurred');
  });
});
