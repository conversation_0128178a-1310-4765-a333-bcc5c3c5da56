import type { FeatherEventBus } from "../types";

/** next-frame throttle */
export function rafThrottle<T extends (...a: any[]) => void>(fn: T): T {
	let run = false;
	return function (this: unknown, ...a: any[]) {
	  if (run) return;
	  run = true;
	  requestAnimationFrame(() => {
		fn.apply(this, a);
		run = false;
	  });
	} as T;
}
  
/** BroadcastChannel fallback */
export function makeBus(channel: string): FeatherEventBus {
	const bc = new BroadcastChannel(channel);
	return {
	  publish: (e: string, d?: unknown) => bc.postMessage({ e, d }),
	  subscribe: (e: string, cb: (data: unknown) => void) => {
		const handler = ({ data }: MessageEvent) => {
			if (data?.e === e) cb(data.d);
		};
		bc.addEventListener('message', handler);
		return { unsubscribe: () => bc.removeEventListener('message', handler) };
	  }
	};
}