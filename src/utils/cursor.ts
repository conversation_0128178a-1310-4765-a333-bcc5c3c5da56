/**
 * Places the cursor at the end of the content within the given element.
 * @param element The container element.
 */
export function placeCursorAtEnd(element: HTMLElement): void {
  const range = document.createRange();
  const selection = window.getSelection();

  if (!selection) return;

  // Find the last valid node for cursor placement
  let lastNode = element.lastChild;
  if (!lastNode) {
    // Element might be empty, ensure a baseline paragraph/br if needed
    // Or simply set cursor at the start if truly empty
    range.setStart(element, 0);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
    return;
  }

  // Traverse down to the last actual content node
  while (lastNode.nodeType === Node.ELEMENT_NODE && lastNode.lastChild) {
    // Handle potential <br> tags as last child - place cursor before them?
    // Or ignore them and go to the last text/element node?
    // Current logic goes to the deepest last child.
    lastNode = lastNode.lastChild;
  }

  // Place cursor at the end of the text node or after the element node
  if (lastNode.nodeType === Node.TEXT_NODE) {
    range.setStart(lastNode, lastNode.textContent?.length ?? 0);
  } else {
    range.setStartAfter(lastNode);
  }

  range.collapse(true);
  selection.removeAllRanges();
  selection.addRange(range);
}
