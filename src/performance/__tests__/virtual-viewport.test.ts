import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
} from 'vitest';
import { VirtualViewport } from '../virtual-viewport';
import type { ThemeDefinition } from '../../themes/theme-types';

// Mock theme manager interface
interface MockThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

// Mock theme definitions
const mockLightTheme: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  description: 'Light theme for testing',
  version: '1.0.0',
  author: 'Test',
  isBuiltIn: true,
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  },
  stateColors: {
    hover: { background: '#f5f5f5', border: '#e0e0e0', text: '#333333' },
    active: { background: '#e0e0e0', border: '#d0d0d0', text: '#333333' },
    focus: { background: '#ffffff', border: '#3b82f6', ring: '#3b82f6' },
    disabled: { background: '#f5f5f5', border: '#e0e0e0', text: '#999999' }
  },
  pluginColors: {
    palette: {
      background: '#ffffff',
      border: '#e0e0e0',
      shadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      tabActiveBorder: '#4a86e8',
      swatchBorder: 'rgba(0, 0, 0, 0.1)'
    },
    chart: {
      background: '#ffffff',
      gridLines: '#e0e0e0',
      dataColors: ['#1565c0', '#28a745', '#ffc107', '#dc3545', '#17a2b8']
    },
    code: {
      background: '#f8f9fa',
      border: '#e0e0e0',
      text: '#333333',
      keyword: '#1565c0',
      string: '#28a745',
      comment: '#6c757d'
    },
    table: {
      background: '#ffffff',
      border: '#e0e0e0',
      headerBackground: '#f8f9fa',
      alternateRowBackground: '#f8f9fa'
    },
    comments: {
      background: '#ffffff',
      border: '#e0e0e0',
      highlightBackground: '#fff3cd',
      avatarBackground: '#f8f9fa'
    }
  },
  animation: {
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    respectReducedMotion: true
  },
  cssVariablePrefix: 'theme'
};

const mockDarkTheme: ThemeDefinition = {
  ...mockLightTheme,
  id: 'dark',
  name: 'Dark Theme',
  colors: {
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d',
    border: '#444444',
    primary: '#4fc3f7',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  }
};

describe('VirtualViewport Theme Integration', () => {
  let virtualViewport: VirtualViewport;
  let mockContainer: HTMLElement;
  let mockThemeManager: MockThemeManager;
  let themeChangeCallback: (theme: ThemeDefinition) => void;

  beforeEach(() => {
    // Create mock container element
    mockContainer = document.createElement('div');
    mockContainer.style.height = '400px';
    mockContainer.style.overflow = 'auto';
    document.body.appendChild(mockContainer);

    // Create mock theme manager
    mockThemeManager = {
      getCurrentTheme: vi.fn().mockReturnValue(mockLightTheme),
      watch: vi.fn().mockImplementation((callback) => {
        themeChangeCallback = callback;
        return vi.fn(); // Return unsubscribe function
      }),
    };

    // Mock requestAnimationFrame
    global.requestAnimationFrame = vi.fn().mockImplementation((cb) => {
      setTimeout(cb, 16);
      return 1;
    });

    // Mock IntersectionObserver
    global.IntersectionObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));

    // Mock MutationObserver
    global.MutationObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      disconnect: vi.fn(),
    }));
  });

  afterEach(() => {
    if (virtualViewport) {
      virtualViewport.destroy();
    }
    document.body.removeChild(mockContainer);
    vi.restoreAllMocks();
  });

  describe('Theme Manager Integration', () => {
    it('should create virtual viewport without theme manager', () => {
      virtualViewport = new VirtualViewport(mockContainer);

      expect(virtualViewport).toBeDefined();
      expect(virtualViewport.getCurrentTheme()).toBeNull();
    });

    it('should initialize with theme manager and current theme', () => {
      virtualViewport = new VirtualViewport(mockContainer, 50, mockThemeManager);

      expect(mockThemeManager.getCurrentTheme).toHaveBeenCalled();
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
      expect(virtualViewport.getCurrentTheme()).toEqual(mockLightTheme);
    });

    it('should handle theme changes', () => {
      virtualViewport = new VirtualViewport(mockContainer, 50, mockThemeManager);

      // Simulate theme change
      themeChangeCallback(mockDarkTheme);

      expect(virtualViewport.getCurrentTheme()).toEqual(mockDarkTheme);
    });

    it('should clean up theme integration on destroy', () => {
      const unsubscribeMock = vi.fn();
      mockThemeManager.watch = vi.fn().mockReturnValue(unsubscribeMock);

      virtualViewport = new VirtualViewport(mockContainer, 50, mockThemeManager);
      virtualViewport.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
      expect(virtualViewport.getCurrentTheme()).toBeNull();
    });

    it('should handle theme change errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      virtualViewport = new VirtualViewport(mockContainer, 50, mockThemeManager);

      // Mock _scheduleUpdate to throw an error
      const originalScheduleUpdate = virtualViewport['_scheduleUpdate'];
      virtualViewport['_scheduleUpdate'] = vi.fn().mockImplementation(() => {
        throw new Error('Mock update error');
      });

      // Simulate theme change that will trigger an error
      themeChangeCallback(mockDarkTheme);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[VirtualViewport] Error handling theme change:'),
        expect.any(Error)
      );

      // Restore original method
      virtualViewport['_scheduleUpdate'] = originalScheduleUpdate;
      consoleSpy.mockRestore();
    });
  });

  describe('Virtual Viewport Functionality with Theme Integration', () => {
    beforeEach(() => {
      virtualViewport = new VirtualViewport(mockContainer, 50, mockThemeManager);
    });

    it('should maintain virtual viewport functionality with theme manager', async () => {
      const testLines = ['Line 1', 'Line 2', 'Line 3', 'Line 4', 'Line 5'];

      virtualViewport.setContent(testLines);

      // Wait for requestAnimationFrame to complete DOM update
      await new Promise(resolve => setTimeout(resolve, 20));

      // Should have set up the content
      expect(mockContainer.children.length).toBeGreaterThan(0);
    });

    it('should apply theme classes to virtual lines', async () => {
      const testLines = ['<p>Line 1</p>', '<p>Line 2</p>'];

      virtualViewport.setContent(testLines);

      // Wait for requestAnimationFrame
      await new Promise(resolve => setTimeout(resolve, 20));

      // Check if theme classes are applied to line elements
      const lineElements = mockContainer.querySelectorAll('.virtual-viewport-line');
      expect(lineElements.length).toBeGreaterThan(0);

      lineElements.forEach(line => {
        expect(line.classList.contains('theme-light')).toBe(true);
      });
    });

    it('should update theme classes when theme changes', async () => {
      const testLines = ['<p>Line 1</p>', '<p>Line 2</p>'];

      virtualViewport.setContent(testLines);
      await new Promise(resolve => setTimeout(resolve, 20));

      // Change theme
      themeChangeCallback(mockDarkTheme);
      await new Promise(resolve => setTimeout(resolve, 20));

      // Check if theme classes are updated
      const lineElements = mockContainer.querySelectorAll('.virtual-viewport-line');
      lineElements.forEach(line => {
        expect(line.classList.contains('theme-dark')).toBe(true);
      });
    });

    it('should apply CSS variables for theme-aware styling', async () => {
      const testLines = ['<p>Test line</p>'];

      virtualViewport.setContent(testLines);
      await new Promise(resolve => setTimeout(resolve, 20));

      const lineElements = mockContainer.querySelectorAll('.virtual-viewport-line');
      expect(lineElements.length).toBeGreaterThan(0);

      const firstLine = lineElements[0] as HTMLElement;
      expect(firstLine.style.backgroundColor).toBe('var(--viewport-line-bg)');
      expect(firstLine.style.color).toBe('var(--viewport-line-text)');
    });
  });
});
