/**
 * Tests for Performance Integration Layer
 * Validates cross-directory integration between src/performance/ and src/themes/performance/
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { UnifiedPerformanceManager } from '../performance-integration-layer';
import { PerformanceOperation, PerformanceMetrics } from '../shared-performance-types';

// Mock implementations
class MockThemePerformanceMonitor {
  public measureOperation = vi.fn().mockResolvedValue('theme-result');
  public measureOperationSync = vi.fn().mockReturnValue('theme-sync-result');
  public setPerformanceThreshold = vi.fn();
  public getPerformanceStats = vi.fn().mockReturnValue({
    operationCounts: new Map([['theme_switch', 5]]),
    averageDurations: new Map([['theme_switch', 50]]),
    benchmarkViolations: new Map([['theme_switch', 0]]),
    memoryTrend: 'stable' as const
  });
}

class MockThemeBenchmarkRunner {
  public runBenchmark = vi.fn();
  public compareWithBaseline = vi.fn();
  public getHistoricalData = vi.fn().mockReturnValue([]);
}

class MockDOMOptimizer {
  public createElementsBatch = vi.fn().mockReturnValue([]);
  public monitorElementChanges = vi.fn().mockReturnValue(() => {});
  public registerElement = vi.fn();
  public destroy = vi.fn();
}

class MockCSSOptimizer {
  public optimize = vi.fn();
}

describe('UnifiedPerformanceManager', () => {
  let manager: UnifiedPerformanceManager;
  let mockThemePerformanceMonitor: MockThemePerformanceMonitor;
  let mockThemeBenchmarkRunner: MockThemeBenchmarkRunner;
  let mockDOMOptimizer: MockDOMOptimizer;
  let mockCSSOptimizer: MockCSSOptimizer;

  beforeEach(() => {
    mockThemePerformanceMonitor = new MockThemePerformanceMonitor();
    mockThemeBenchmarkRunner = new MockThemeBenchmarkRunner();
    mockDOMOptimizer = new MockDOMOptimizer();
    mockCSSOptimizer = new MockCSSOptimizer();

    manager = new UnifiedPerformanceManager(
      mockThemePerformanceMonitor as any,
      mockThemeBenchmarkRunner as any,
      mockDOMOptimizer as any,
      mockCSSOptimizer as any,
      {
        enabled: true,
        collectMemoryData: true,
        maxRecords: 100,
        logViolations: true
      }
    );
  });

  afterEach(() => {
    manager.destroy();
    vi.clearAllMocks();
  });

  describe('measureOperation', () => {
    it('should delegate theme operations to theme performance monitor', async () => {
      const testFn = vi.fn().mockResolvedValue('test-result');
      const context = { elementCount: 5 };

      const result = await manager.measureOperation(
        PerformanceOperation.THEME_SWITCH,
        testFn,
        context
      );

      expect(mockThemePerformanceMonitor.measureOperation).toHaveBeenCalledWith(
        'theme_switch',
        testFn,
        context
      );
      expect(result).toBe('theme-result');
    });

    it('should handle non-theme operations directly', async () => {
      const testFn = vi.fn().mockResolvedValue('test-result');
      const context = { elementCount: 3 };

      const result = await manager.measureOperation(
        PerformanceOperation.DOM_READ,
        testFn,
        context
      );

      expect(mockThemePerformanceMonitor.measureOperation).not.toHaveBeenCalled();
      expect(result).toBe('test-result');
    });

    it('should record metrics for non-theme operations', async () => {
      const testFn = vi.fn().mockResolvedValue('test-result');

      await manager.measureOperation(
        PerformanceOperation.DOM_READ,
        testFn,
        { elementCount: 2 }
      );

      const stats = manager.getPerformanceStats();
      expect(stats.operationCounts.has('dom_read')).toBe(true);
    });

    it('should handle operation failures gracefully', async () => {
      const testFn = vi.fn().mockRejectedValue(new Error('Test error'));

      await expect(manager.measureOperation(
        PerformanceOperation.DOM_READ,
        testFn
      )).rejects.toThrow('Test error');

      const stats = manager.getPerformanceStats();
      expect(stats.operationCounts.has('dom_read')).toBe(true);
    });
  });

  describe('measureOperationSync', () => {
    it('should delegate theme operations to theme performance monitor', () => {
      const testFn = vi.fn().mockReturnValue('sync-result');
      const context = { elementCount: 1 };

      const result = manager.measureOperationSync(
        PerformanceOperation.CSS_INJECT,
        testFn,
        context
      );

      expect(mockThemePerformanceMonitor.measureOperationSync).toHaveBeenCalledWith(
        'css_inject',
        testFn,
        context
      );
      expect(result).toBe('theme-sync-result');
    });

    it('should handle non-theme operations directly', () => {
      const testFn = vi.fn().mockReturnValue('sync-result');

      const result = manager.measureOperationSync(
        PerformanceOperation.DOM_WRITE,
        testFn
      );

      expect(mockThemePerformanceMonitor.measureOperationSync).not.toHaveBeenCalled();
      expect(result).toBe('sync-result');
    });
  });

  describe('setPerformanceThreshold', () => {
    it('should update threshold for theme operations in both systems', () => {
      manager.setPerformanceThreshold(PerformanceOperation.THEME_SWITCH, 150);

      expect(mockThemePerformanceMonitor.setPerformanceThreshold).toHaveBeenCalledWith(
        'theme_switch',
        150
      );
    });

    it('should update threshold for non-theme operations', () => {
      manager.setPerformanceThreshold(PerformanceOperation.DOM_BATCH, 20);

      expect(mockThemePerformanceMonitor.setPerformanceThreshold).not.toHaveBeenCalled();
      // Threshold should be stored internally for non-theme operations
    });
  });

  describe('getPerformanceStats', () => {
    it('should merge stats from both performance systems', () => {
      const stats = manager.getPerformanceStats();

      expect(mockThemePerformanceMonitor.getPerformanceStats).toHaveBeenCalled();
      expect(stats.operationCounts).toBeInstanceOf(Map);
      expect(stats.averageDurations).toBeInstanceOf(Map);
      expect(stats.benchmarkViolations).toBeInstanceOf(Map);
      expect(['increasing', 'decreasing', 'stable']).toContain(stats.memoryTrend);
    });
  });

  describe('runBenchmark', () => {
    it('should run benchmark with specified iterations', async () => {
      const testFn = vi.fn().mockResolvedValue(undefined);
      const iterations = 5;

      const result = await manager.runBenchmark(
        PerformanceOperation.ELEMENT_CREATE,
        testFn,
        iterations
      );

      expect(testFn).toHaveBeenCalledTimes(iterations);
      expect(result.operation).toBe(PerformanceOperation.ELEMENT_CREATE);
      expect(result.metrics).toBeDefined();
      expect(result.id).toContain('element_create');
    });

    it('should calculate average metrics correctly', async () => {
      const testFn = vi.fn().mockResolvedValue(undefined);

      const result = await manager.runBenchmark(
        PerformanceOperation.ELEMENT_CREATE,
        testFn,
        3
      );

      expect(result.metrics.duration).toBeGreaterThanOrEqual(0);
      expect(result.metrics.meetsBenchmark).toBeDefined();
      expect(result.context?.iterations).toBe(3);
    });
  });

  describe('compareWithBaseline', () => {
    it('should return no regression for first measurement', () => {
      const metrics: PerformanceMetrics = {
        startTime: 0,
        endTime: 10,
        duration: 10,
        meetsBenchmark: true,
        benchmark: 100
      };

      const comparison = manager.compareWithBaseline(
        PerformanceOperation.DOM_READ,
        metrics
      );

      expect(comparison.isRegression).toBe(false);
      expect(comparison.changeFromBaseline).toBe(0);
      expect(comparison.recommendation).toContain('No baseline data');
    });
  });

  describe('batchDOMOperations', () => {
    it('should batch operations using batch manager', async () => {
      const operations = [
        vi.fn(),
        vi.fn(),
        vi.fn()
      ];

      await manager.batchDOMOperations(operations);

      // Operations should be scheduled (exact verification depends on BatchManager implementation)
      expect(operations.length).toBe(3);
    });
  });

  describe('optimizeElementCreation', () => {
    it('should use DOM optimizer for large batches', () => {
      const factory = vi.fn().mockReturnValue(document.createElement('div'));
      const count = 20;

      const elements = manager.optimizeElementCreation(count, factory);

      // For large batches, it should register elements with DOM optimizer
      expect(mockDOMOptimizer.registerElement).toHaveBeenCalledTimes(count);
      expect(factory).toHaveBeenCalledTimes(count);
      expect(elements).toHaveLength(count);
    });

    it('should use regular creation for small batches', () => {
      const factory = vi.fn().mockReturnValue(document.createElement('div'));
      const count = 5;

      const elements = manager.optimizeElementCreation(count, factory);

      // For small batches, it should not register with DOM optimizer
      expect(mockDOMOptimizer.registerElement).not.toHaveBeenCalled();
      expect(factory).toHaveBeenCalledTimes(count);
      expect(elements).toHaveLength(count);
    });
  });

  describe('monitorDOMMutations', () => {
    it('should create mutation observer and return cleanup function', () => {
      const target = document.createElement('div');
      const callback = vi.fn();

      const unsubscribe = manager.monitorDOMMutations(target, callback);

      // Should return a cleanup function
      expect(typeof unsubscribe).toBe('function');

      // Should be able to call cleanup without errors
      expect(() => unsubscribe()).not.toThrow();
    });
  });

  describe('resource management', () => {
    it('should provide access to batch manager', () => {
      const batchManager = manager.getBatchManager();
      expect(batchManager).toBeDefined();
    });

    it('should provide access to CSS optimizer', () => {
      const cssOptimizer = manager.getCSSOptimizer();
      expect(cssOptimizer).toBe(mockCSSOptimizer);
    });

    it('should clean up resources on destroy', () => {
      manager.destroy();

      // The destroy method should clean up the batch manager and clear history
      // DOMOptimizer doesn't have a destroy method in the actual implementation
      expect(() => manager.destroy()).not.toThrow();
    });
  });
});
