/**
 * Tests for Enhanced Benchmark Runner
 * Validates performance benchmarking functionality and integration
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { EnhancedBenchmarkRunner } from '../benchmark';
import { PerformanceOperation } from '../shared-performance-types';

describe('EnhancedBenchmarkRunner', () => {
  let runner: EnhancedBenchmarkRunner;

  beforeEach(() => {
    runner = new EnhancedBenchmarkRunner();
  });

  describe('runBenchmark', () => {
    it('should run benchmark with specified iterations', async () => {
      const testFunction = vi.fn().mockResolvedValue(undefined);
      const iterations = 5;

      const result = await runner.runBenchmark(
        PerformanceOperation.ELEMENT_CREATE,
        testFunction,
        iterations
      );

      expect(testFunction).toHaveBeenCalledTimes(iterations);
      expect(result.operation).toBe(PerformanceOperation.ELEMENT_CREATE);
      expect(result.id).toContain('element_create');
      expect(result.metrics).toBeDefined();
      expect(result.metrics.duration).toBeGreaterThanOrEqual(0);
    });

    it('should use default iterations when not specified', async () => {
      const testFunction = vi.fn().mockResolvedValue(undefined);

      const result = await runner.runBenchmark(
        PerformanceOperation.DOM_READ,
        testFunction
      );

      expect(testFunction).toHaveBeenCalledTimes(10); // Default iterations
      expect(result.context?.iterations).toBe(10);
    });

    it('should calculate average metrics correctly', async () => {
      const testFunction = vi.fn().mockImplementation(() => {
        // Simulate some work
        const start = performance.now();
        while (performance.now() - start < 1) {
          // Busy wait for 1ms
        }
      });

      const result = await runner.runBenchmark(
        PerformanceOperation.DOM_WRITE,
        testFunction,
        3
      );

      expect(result.metrics.duration).toBeGreaterThan(0);
      expect(result.metrics.startTime).toBeGreaterThan(0);
      expect(result.metrics.endTime).toBeGreaterThan(result.metrics.startTime);
      expect(result.context?.results).toHaveLength(3);
    });

    it('should handle benchmark failures gracefully', async () => {
      const testFunction = vi.fn()
        .mockResolvedValueOnce(undefined) // First call succeeds
        .mockRejectedValueOnce(new Error('Test error')) // Second call fails
        .mockResolvedValueOnce(undefined); // Third call succeeds

      const result = await runner.runBenchmark(
        PerformanceOperation.THEME_SWITCH,
        testFunction,
        3
      );

      expect(testFunction).toHaveBeenCalledTimes(3);
      expect(result.context?.results).toHaveLength(2); // Only successful iterations
      expect(result.metrics.duration).toBeGreaterThanOrEqual(0);
    });

    it('should determine benchmark compliance correctly', async () => {
      const fastFunction = vi.fn().mockResolvedValue(undefined);
      const slowFunction = vi.fn().mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 150)); // 150ms delay
      });

      const fastResult = await runner.runBenchmark(
        PerformanceOperation.DOM_READ, // 1ms threshold
        fastFunction,
        1
      );

      const slowResult = await runner.runBenchmark(
        PerformanceOperation.THEME_SWITCH, // 100ms threshold
        slowFunction,
        1
      );

      expect(fastResult.metrics.meetsBenchmark).toBe(true);
      expect(slowResult.metrics.meetsBenchmark).toBe(false);
    });
  });

  describe('compareWithBaseline', () => {
    it('should return no regression for first measurement', () => {
      const metrics = {
        startTime: 100,
        endTime: 150,
        duration: 50,
        meetsBenchmark: true,
        benchmark: 100
      };

      const comparison = runner.compareWithBaseline(
        PerformanceOperation.THEME_LOAD,
        metrics
      );

      expect(comparison.isRegression).toBe(false);
      expect(comparison.changeFromBaseline).toBe(0);
      expect(comparison.recommendation).toContain('No baseline data');
    });

    it('should detect performance regression', () => {
      // First, establish baseline by getting historical data
      const baselineMetrics = {
        startTime: 100,
        endTime: 150,
        duration: 50,
        meetsBenchmark: true,
        benchmark: 100
      };

      // Simulate having historical data
      const historicalData = [
        {
          id: 'baseline_1',
          operation: PerformanceOperation.THEME_VALIDATE,
          metrics: baselineMetrics,
          isRegression: false,
          changeFromBaseline: 0
        }
      ];

      // Mock the historical data by setting it directly
      (runner as any).historicalData.set(PerformanceOperation.THEME_VALIDATE, historicalData);

      const currentMetrics = {
        startTime: 200,
        endTime: 280,
        duration: 80, // 60% slower than baseline
        meetsBenchmark: false,
        benchmark: 100
      };

      const comparison = runner.compareWithBaseline(
        PerformanceOperation.THEME_VALIDATE,
        currentMetrics
      );

      expect(comparison.isRegression).toBe(true);
      expect(comparison.changeFromBaseline).toBeGreaterThan(15);
      expect(comparison.recommendation).toContain('regression');
    });

    it('should detect performance improvement', () => {
      const historicalData = [
        {
          id: 'baseline_1',
          operation: PerformanceOperation.CSS_INJECT,
          metrics: {
            startTime: 100,
            endTime: 200,
            duration: 100,
            meetsBenchmark: false,
            benchmark: 50
          },
          isRegression: false,
          changeFromBaseline: 0
        }
      ];

      // Mock the historical data by setting it directly
      (runner as any).historicalData.set(PerformanceOperation.CSS_INJECT, historicalData);

      const currentMetrics = {
        startTime: 200,
        endTime: 230,
        duration: 30, // 70% faster than baseline
        meetsBenchmark: true,
        benchmark: 50
      };

      const comparison = runner.compareWithBaseline(
        PerformanceOperation.CSS_INJECT,
        currentMetrics
      );

      expect(comparison.isRegression).toBe(false);
      expect(comparison.changeFromBaseline).toBeLessThan(-10);
      expect(comparison.recommendation).toContain('improvement');
    });
  });

  describe('getHistoricalData', () => {
    it('should return empty array for operations with no history', () => {
      const data = runner.getHistoricalData(PerformanceOperation.PLUGIN_APPLY);
      expect(data).toEqual([]);
    });
  });

  describe('threshold management', () => {
    it('should use correct thresholds for different operations', async () => {
      const testFunction = vi.fn().mockResolvedValue(undefined);

      // Test DOM operations (low thresholds)
      const domResult = await runner.runBenchmark(
        PerformanceOperation.DOM_READ,
        testFunction,
        1
      );
      expect(domResult.metrics.benchmark).toBe(1);

      // Test theme operations (higher thresholds)
      const themeResult = await runner.runBenchmark(
        PerformanceOperation.THEME_SWITCH,
        testFunction,
        1
      );
      expect(themeResult.metrics.benchmark).toBe(100);

      // Test benchmark operations (highest thresholds)
      const benchmarkResult = await runner.runBenchmark(
        PerformanceOperation.BENCHMARK_RUN,
        testFunction,
        1
      );
      expect(benchmarkResult.metrics.benchmark).toBe(1000);
    });
  });

  describe('memory tracking', () => {
    it('should include memory usage when available', async () => {
      // Mock performance.memory
      const mockMemory = { usedJSHeapSize: 1000000 };
      Object.defineProperty(performance, 'memory', {
        value: mockMemory,
        configurable: true
      });

      const testFunction = vi.fn().mockResolvedValue(undefined);

      const result = await runner.runBenchmark(
        PerformanceOperation.ELEMENT_CREATE,
        testFunction,
        1
      );

      expect(result.metrics.memoryBefore).toBeDefined();
      expect(result.metrics.memoryAfter).toBeDefined();
    });

    it('should handle missing memory API gracefully', async () => {
      // Remove memory property
      const originalMemory = (performance as any).memory;
      delete (performance as any).memory;

      const testFunction = vi.fn().mockResolvedValue(undefined);

      const result = await runner.runBenchmark(
        PerformanceOperation.VIEWPORT_UPDATE,
        testFunction,
        1
      );

      expect(result.metrics.memoryBefore).toBe(0);
      expect(result.metrics.memoryAfter).toBe(0);

      // Restore memory property
      if (originalMemory) {
        (performance as any).memory = originalMemory;
      }
    });
  });
});
