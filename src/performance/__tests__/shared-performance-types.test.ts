/**
 * Tests for Shared Performance Types
 * Validates type definitions and utility functions for cross-directory integration
 */

import { describe, it, expect } from 'vitest';
import {
  PerformanceOperation,
  PerformanceIntegration,
  PerformanceMetrics
} from '../shared-performance-types';

describe('PerformanceOperation', () => {
  it('should define all required operation types', () => {
    expect(PerformanceOperation.DOM_READ).toBe('dom_read');
    expect(PerformanceOperation.DOM_WRITE).toBe('dom_write');
    expect(PerformanceOperation.DOM_BATCH).toBe('dom_batch');
    expect(PerformanceOperation.THEME_SWITCH).toBe('theme_switch');
    expect(PerformanceOperation.THEME_LOAD).toBe('theme_load');
    expect(PerformanceOperation.THEME_SAVE).toBe('theme_save');
    expect(PerformanceOperation.THEME_VALIDATE).toBe('theme_validate');
    expect(PerformanceOperation.ELEMENT_CREATE).toBe('element_create');
    expect(PerformanceOperation.ELEMENT_UPDATE).toBe('element_update');
    expect(PerformanceOperation.ELEMENT_REMOVE).toBe('element_remove');
    expect(PerformanceOperation.CSS_INJECT).toBe('css_inject');
    expect(PerformanceOperation.CSS_OPTIMIZE).toBe('css_optimize');
    expect(PerformanceOperation.PLUGIN_APPLY).toBe('plugin_apply');
    expect(PerformanceOperation.PLUGIN_RENDER).toBe('plugin_render');
    expect(PerformanceOperation.VIEWPORT_UPDATE).toBe('viewport_update');
    expect(PerformanceOperation.VIEWPORT_SCROLL).toBe('viewport_scroll');
    expect(PerformanceOperation.BENCHMARK_RUN).toBe('benchmark_run');
    expect(PerformanceOperation.BENCHMARK_COMPARE).toBe('benchmark_compare');
  });
});

describe('PerformanceIntegration', () => {
  describe('mapThemeToPerformanceOperation', () => {
    it('should map theme operations to performance operations', () => {
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('theme_switch'))
        .toBe(PerformanceOperation.THEME_SWITCH);
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('theme_load'))
        .toBe(PerformanceOperation.THEME_LOAD);
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('theme_save'))
        .toBe(PerformanceOperation.THEME_SAVE);
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('theme_validate'))
        .toBe(PerformanceOperation.THEME_VALIDATE);
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('element_create'))
        .toBe(PerformanceOperation.ELEMENT_CREATE);
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('css_inject'))
        .toBe(PerformanceOperation.CSS_INJECT);
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('plugin_apply'))
        .toBe(PerformanceOperation.PLUGIN_APPLY);
    });

    it('should return default operation for unknown theme operations', () => {
      expect(PerformanceIntegration.mapThemeToPerformanceOperation('unknown_operation'))
        .toBe(PerformanceOperation.DOM_BATCH);
    });
  });

  describe('createDefaultConfig', () => {
    it('should create valid default configuration', () => {
      const config = PerformanceIntegration.createDefaultConfig();

      expect(config.enabled).toBe(true);
      expect(config.thresholds).toBeInstanceOf(Map);
      expect(config.collectMemoryData).toBe(true);
      expect(config.maxRecords).toBe(1000);
      expect(config.logViolations).toBe(true);
    });

    it('should include thresholds for all operation types', () => {
      const config = PerformanceIntegration.createDefaultConfig();

      expect(config.thresholds.get(PerformanceOperation.DOM_READ)).toBe(1);
      expect(config.thresholds.get(PerformanceOperation.DOM_WRITE)).toBe(2);
      expect(config.thresholds.get(PerformanceOperation.DOM_BATCH)).toBe(16);
      expect(config.thresholds.get(PerformanceOperation.THEME_SWITCH)).toBe(100);
      expect(config.thresholds.get(PerformanceOperation.THEME_LOAD)).toBe(50);
      expect(config.thresholds.get(PerformanceOperation.THEME_SAVE)).toBe(200);
      expect(config.thresholds.get(PerformanceOperation.THEME_VALIDATE)).toBe(10);
      expect(config.thresholds.get(PerformanceOperation.ELEMENT_CREATE)).toBe(1);
      expect(config.thresholds.get(PerformanceOperation.CSS_INJECT)).toBe(10);
      expect(config.thresholds.get(PerformanceOperation.PLUGIN_APPLY)).toBe(50);
      expect(config.thresholds.get(PerformanceOperation.VIEWPORT_UPDATE)).toBe(16);
      expect(config.thresholds.get(PerformanceOperation.BENCHMARK_RUN)).toBe(1000);
    });
  });

  describe('mergeMetrics', () => {
    it('should merge two performance metrics correctly', () => {
      const metrics1: PerformanceMetrics = {
        startTime: 100,
        endTime: 150,
        duration: 50,
        memoryBefore: 1000,
        memoryAfter: 1100,
        elementCount: 5,
        meetsBenchmark: true,
        benchmark: 100
      };

      const metrics2: PerformanceMetrics = {
        startTime: 120,
        endTime: 180,
        duration: 60,
        memoryBefore: 1050,
        memoryAfter: 1200,
        elementCount: 3,
        meetsBenchmark: false,
        benchmark: 80
      };

      const merged = PerformanceIntegration.mergeMetrics(metrics1, metrics2);

      expect(merged.startTime).toBe(100); // Min of start times
      expect(merged.endTime).toBe(180); // Max of end times
      expect(merged.duration).toBe(110); // Sum of durations
      expect(merged.memoryBefore).toBe(1000); // First memory before
      expect(merged.memoryAfter).toBe(1200); // Last memory after
      expect(merged.elementCount).toBe(8); // Sum of element counts
      expect(merged.meetsBenchmark).toBe(false); // AND of benchmarks
      expect(merged.benchmark).toBe(100); // Max of benchmarks
    });

    it('should handle missing optional properties', () => {
      const metrics1: PerformanceMetrics = {
        startTime: 100,
        endTime: 150,
        duration: 50,
        meetsBenchmark: true,
        benchmark: 100
      };

      const metrics2: PerformanceMetrics = {
        startTime: 120,
        endTime: 180,
        duration: 60,
        memoryAfter: 1200,
        elementCount: 3,
        meetsBenchmark: false,
        benchmark: 80
      };

      const merged = PerformanceIntegration.mergeMetrics(metrics1, metrics2);

      expect(merged.memoryBefore).toBeUndefined();
      expect(merged.memoryAfter).toBe(1200);
      expect(merged.elementCount).toBe(3); // 0 + 3
    });
  });
});

describe('Performance Interfaces', () => {
  describe('PerformanceMetrics', () => {
    it('should define required properties', () => {
      const metrics: PerformanceMetrics = {
        startTime: 100,
        endTime: 150,
        duration: 50,
        meetsBenchmark: true,
        benchmark: 100
      };

      expect(metrics.startTime).toBe(100);
      expect(metrics.endTime).toBe(150);
      expect(metrics.duration).toBe(50);
      expect(metrics.meetsBenchmark).toBe(true);
      expect(metrics.benchmark).toBe(100);
    });

    it('should allow optional properties', () => {
      const metrics: PerformanceMetrics = {
        startTime: 100,
        endTime: 150,
        duration: 50,
        memoryBefore: 1000,
        memoryAfter: 1100,
        elementCount: 5,
        meetsBenchmark: true,
        benchmark: 100
      };

      expect(metrics.memoryBefore).toBe(1000);
      expect(metrics.memoryAfter).toBe(1100);
      expect(metrics.elementCount).toBe(5);
    });
  });

  describe('BenchmarkResult', () => {
    it('should include all required properties', () => {
      const result = {
        id: 'test_benchmark_123',
        operation: PerformanceOperation.THEME_SWITCH,
        metrics: {
          startTime: 100,
          endTime: 150,
          duration: 50,
          meetsBenchmark: true,
          benchmark: 100
        },
        isRegression: false,
        changeFromBaseline: -5.2,
        context: { iterations: 10 }
      };

      expect(result.id).toBe('test_benchmark_123');
      expect(result.operation).toBe(PerformanceOperation.THEME_SWITCH);
      expect(result.metrics).toBeDefined();
      expect(result.isRegression).toBe(false);
      expect(result.changeFromBaseline).toBe(-5.2);
      expect(result.context).toEqual({ iterations: 10 });
    });
  });

  describe('PerformanceConfig', () => {
    it('should define configuration structure', () => {
      const config = {
        enabled: true,
        thresholds: new Map([[PerformanceOperation.THEME_SWITCH, 100]]),
        collectMemoryData: true,
        maxRecords: 1000,
        logViolations: true
      };

      expect(config.enabled).toBe(true);
      expect(config.thresholds).toBeInstanceOf(Map);
      expect(config.collectMemoryData).toBe(true);
      expect(config.maxRecords).toBe(1000);
      expect(config.logViolations).toBe(true);
    });
  });
});
