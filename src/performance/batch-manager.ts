/**
 * Manages batched DOM update operations using requestAnimationFrame for performance optimization.
 *
 * This manager follows the SOLID principles:
 * - Single Responsibility: Focuses only on batching read & write DOM operations
 * - Open/Closed: Extensible through operation priority system
 * - Liskov Substitution: Properly typed interfaces for operation callbacks
 * - Interface Segregation: Separates read vs write operation types
 * - Dependency Inversion: Depends on abstractions rather than concrete implementations
 *
 * Integrates with the unified performance monitoring system for cross-directory metrics.
 */

import { PerformanceOperation, IPerformanceMonitor } from './shared-performance-types';

/**
 * Operation priority levels for batch processing
 */
enum OperationPriority {
  CRITICAL = 0,   // Process immediately, can't be batched (synchronous)
  HIGH = 1,       // Process in the next animation frame
  NORMAL = 2,     // Default priority
  LOW = 3,        // Lower priority, can be delayed if needed
}

/**
 * Operation result type
 */
type OperationResult<T = void> = T | Error | undefined;

/**
 * Callback operation type with error handling
 */
type Callback<T = void> = () => T | Promise<T>;

/**
 * Operation metadata for tracking and debugging
 */
interface Operation {
  /** The operation callback function */
  callback: Callback<any>;
  /** When the operation was scheduled */
  timestamp: number;
  /** Priority level of this operation */
  priority: OperationPriority;
  /** Optional timeout in ms after which this operation is skipped */
  timeout?: number;
}

/**
 * Configuration options for the batch manager
 */
interface BatchManagerOptions {
  /** Whether to enable detailed performance logging */
  enablePerfLogging?: boolean;
  /** Maximum time (ms) to spend on batch operations before yielding */
  maxBatchTime?: number;
  /** Whether to process operations in priority order */
  usePriorityQueue?: boolean;
  /** Optional performance monitor for cross-directory integration */
  performanceMonitor?: IPerformanceMonitor;
}

/**
 * BatchManager handles efficient DOM read/write operation scheduling
 * to minimize layout thrashing and optimize rendering performance.
 */
export class BatchManager {
  /** Operations that read from the DOM */
  private readonly readQueue: Map<string, Operation> = new Map();

  /** Operations that write to the DOM */
  private readonly writeQueue: Map<string, Operation> = new Map();

  /** Tracks whether a flush operation is already scheduled */
  private isScheduled: boolean = false;

  /** Animation frame request ID for cancellation */
  private frameId: number | null = null;

  /** Configuration options */
  private readonly options: Required<Omit<BatchManagerOptions, 'performanceMonitor'>>;

  /** Optional performance monitor for cross-directory integration */
  private readonly performanceMonitor?: IPerformanceMonitor;

  /** Performance tracking */
  private metrics = {
    totalReads: 0,
    totalWrites: 0,
    lastFlushTime: 0,
    errorCount: 0,
    skippedOperations: 0,
  };

  /**
   * Creates a new batch manager for DOM operations
   * @param options - Configuration options
   */
  constructor(options: BatchManagerOptions = {}) {
    // Apply defaults for options
    this.options = {
      enablePerfLogging: options.enablePerfLogging ?? false,
      maxBatchTime: options.maxBatchTime ?? 16, // Default to 16ms (60fps)
      usePriorityQueue: options.usePriorityQueue ?? true,
    };

    // Store performance monitor for cross-directory integration
    this.performanceMonitor = options.performanceMonitor;
  }

  /**
   * Schedule a DOM read operation
   * @param key - Unique identifier for the operation
   * @param callback - Read operation callback
   * @param options - Optional operation settings
   * @returns This instance for chaining
   */
  read<T = void>(
    key: string,
    callback: Callback<T>,
    options: { priority?: OperationPriority; timeout?: number } = {}
  ): BatchManager {
    try {
      if (!key) {
        console.error('[BatchManager] Invalid key for read operation');
        return this;
      }

      if (typeof callback !== 'function') {
        console.error('[BatchManager] Invalid callback for read operation:', key);
        return this;
      }

      // Store the operation in the read queue
      this.readQueue.set(key, {
        callback,
        timestamp: performance.now(),
        priority: options.priority ?? OperationPriority.NORMAL,
        timeout: options.timeout,
      });

      // Critical operations execute immediately
      if (options.priority === OperationPriority.CRITICAL) {
        this._executeOperation('read', key);
        return this;
      }

      // Schedule a flush for non-critical operations
      this._scheduleFlush();
      return this;
    } catch (error) {
      console.error('[BatchManager] Error scheduling read operation:', error);
      this.metrics.errorCount++;
      return this;
    }
  }

  /**
   * Schedule a DOM write operation
   * @param key - Unique identifier for the operation
   * @param callback - Write operation callback
   * @param options - Optional operation settings
   * @returns This instance for chaining
   */
  write<T = void>(
    key: string,
    callback: Callback<T>,
    options: { priority?: OperationPriority; timeout?: number } = {}
  ): BatchManager {
    try {
      if (!key) {
        console.error('[BatchManager] Invalid key for write operation');
        return this;
      }

      if (typeof callback !== 'function') {
        console.error('[BatchManager] Invalid callback for write operation:', key);
        return this;
      }

      // Store the operation in the write queue
      this.writeQueue.set(key, {
        callback,
        timestamp: performance.now(),
        priority: options.priority ?? OperationPriority.NORMAL,
        timeout: options.timeout,
      });

      // Critical operations execute immediately
      if (options.priority === OperationPriority.CRITICAL) {
        this._executeOperation('write', key);
        return this;
      }

      // Schedule a flush for non-critical operations
      this._scheduleFlush();
      return this;
    } catch (error) {
      console.error('[BatchManager] Error scheduling write operation:', error);
      this.metrics.errorCount++;
      return this;
    }
  }

  /**
   * Cancel a scheduled operation by key
   * @param key - Operation identifier
   * @param type - Optional operation type (read or write)
   * @returns True if operation was found and canceled
   */
  cancel(key: string, type?: 'read' | 'write'): boolean {
    try {
      let canceled = false;

      if (!type || type === 'read') {
        canceled = this.readQueue.delete(key) || canceled;
      }

      if (!type || type === 'write') {
        canceled = this.writeQueue.delete(key) || canceled;
      }

      return canceled;
    } catch (error) {
      console.error('[BatchManager] Error canceling operation:', error);
      return false;
    }
  }

  /**
   * Get performance metrics for the batch manager
   * @returns Object with performance statistics
   */
  getMetrics(): Record<string, number> {
    return {
      ...this.metrics,
      pendingReads: this.readQueue.size,
      pendingWrites: this.writeQueue.size,
    };
  }

  /**
   * Execute a specific operation immediately
   * @param type - Operation type (read or write)
   * @param key - Operation identifier
   * @returns Result of the operation or undefined
   * @private
   */
  private _executeOperation<T = void>(type: 'read' | 'write', key: string): OperationResult<T> {
    const queue = type === 'read' ? this.readQueue : this.writeQueue;
    const operation = queue.get(key);

    if (!operation) return undefined;

    try {
      // Remove the operation from the queue
      queue.delete(key);

      // Check if the operation has timed out
      if (operation.timeout) {
        const now = performance.now();
        const elapsed = now - operation.timestamp;

        if (elapsed > operation.timeout) {
          this.metrics.skippedOperations++;
          return undefined;
        }
      }

      // Execute the operation callback
      const result = operation.callback();

      // Update metrics
      if (type === 'read') {
        this.metrics.totalReads++;
      } else {
        this.metrics.totalWrites++;
      }

      return result as T;
    } catch (error) {
      this.metrics.errorCount++;
      console.error(`[BatchManager] Error executing ${type} operation:`, error);
      return error as Error;
    }
  }

  /**
   * Schedule a flush of queued operations
   * @private
   */
  private _scheduleFlush(): void {
    // Don't schedule if already scheduled
    if (this.isScheduled) return;

    this.isScheduled = true;

    // Use requestAnimationFrame for best performance
    this.frameId = requestAnimationFrame(() => {
      try {
        this._flush();
      } catch (error) {
        console.error('[BatchManager] Error in flush operation:', error);
        this.metrics.errorCount++;
      } finally {
        this.isScheduled = false;
        this.frameId = null;
      }
    });
  }

  /**
   * Cancel any pending scheduled flush
   * @returns True if a flush was canceled
   */
  cancelScheduledFlush(): boolean {
    if (this.frameId !== null) {
      cancelAnimationFrame(this.frameId);
      this.frameId = null;
      this.isScheduled = false;
      return true;
    }
    return false;
  }

  /**
   * Flush queued operations in optimal order
   * @private
   */
  private _flush(): void {
    const startTime = this.options.enablePerfLogging ? performance.now() : 0;

    try {
      // Use performance monitor if available
      if (this.performanceMonitor) {
        this.performanceMonitor.measureOperationSync(
          PerformanceOperation.DOM_BATCH,
          () => {
            // Process all reads first (in priority order if enabled)
            this._processQueue('read');
            // Then process all writes (in priority order if enabled)
            this._processQueue('write');
          },
          {
            elementCount: this.readQueue.size + this.writeQueue.size,
            operationType: 'batch_flush'
          }
        );
      } else {
        // Process all reads first (in priority order if enabled)
        this._processQueue('read');
        // Then process all writes (in priority order if enabled)
        this._processQueue('write');
      }

      if (this.options.enablePerfLogging) {
        this.metrics.lastFlushTime = performance.now() - startTime;
        if (this.metrics.lastFlushTime > 5) {
          console.debug(`[BatchManager] Flush took ${this.metrics.lastFlushTime.toFixed(2)}ms`);
        }
      }
    } catch (error) {
      console.error('[BatchManager] Error during flush:', error);
      this.metrics.errorCount++;
    }
  }

  /**
   * Process a specific operation queue
   * @param type - Queue type to process
   * @private
   */
  private _processQueue(type: 'read' | 'write'): void {
    const queue = type === 'read' ? this.readQueue : this.writeQueue;
    const startTime = performance.now();

    // Use sorted entries if priority queue is enabled
    const entries = this.options.usePriorityQueue
      ? Array.from(queue.entries()).sort((a, b) => a[1].priority - b[1].priority)
      : Array.from(queue.entries());

    // Clear the queue first to avoid re-entrancy issues
    queue.clear();

    for (const [key, operation] of entries) {
      try {
        // Check if we've exceeded the max batch time
        const elapsed = performance.now() - startTime;
        if (elapsed > this.options.maxBatchTime) {
          // Re-queue remaining operations
          const remainingEntries = entries.slice(entries.indexOf([key, operation]));
          for (const [k, op] of remainingEntries) {
            queue.set(k, op);
          }

          // Schedule another flush for remaining operations
          this._scheduleFlush();
          break;
        }

        // Execute the operation
        if (operation.timeout) {
          const now = performance.now();
          if (now - operation.timestamp > operation.timeout) {
            this.metrics.skippedOperations++;
            continue;
          }
        }

        operation.callback();

        // Update metrics
        if (type === 'read') {
          this.metrics.totalReads++;
        } else {
          this.metrics.totalWrites++;
        }
      } catch (error) {
        console.error(`[BatchManager] Error in ${type} operation:`, error);
        this.metrics.errorCount++;
      }
    }
  }

  /**
   * Cleans up resources and cancels any pending operations
   */
  destroy(): void {
    try {
      this.cancelScheduledFlush();
      this.readQueue.clear();
      this.writeQueue.clear();
    } catch (error) {
      console.error('[BatchManager] Error during cleanup:', error);
    }
  }
}