/**
 * Performance Integration Layer
 * Provides integration between src/performance/ and src/themes/performance/ directories
 * Follows Single Responsibility Principle by focusing solely on cross-directory integration.
 */

import { BatchManager } from './batch-manager';
import {
  PerformanceMetrics,
  PerformanceOperation,
  PerformanceConfig,
  BenchmarkResult,
  IPerformanceMonitor,
  IBenchmarkRunner,
  IDOMOptimizer,
  PerformanceIntegration
} from './shared-performance-types';

// Import theme performance utilities
import { ThemePerformanceMonitor } from '../themes/theme-performance-monitor';
import { ThemeBenchmarkRunner } from '../themes/performance/benchmark-utilities';
import { DOMOptimizer } from '../themes/performance/dom-optimizer';
import { CSSOptimizer } from '../themes/performance/css-optimization';
import { ThemeOperation } from '../themes/theme-types';

/**
 * Unified performance manager that integrates both performance directories
 */
export class UnifiedPerformanceManager implements IPerformanceMonitor, IBenchmarkRunner, IDOMOptimizer {
  private batchManager: BatchManager;
  private themePerformanceMonitor: ThemePerformanceMonitor;
  private domOptimizer: DOMOptimizer;
  private cssOptimizer: CSSOptimizer;
  private config: PerformanceConfig;
  private performanceHistory: Map<PerformanceOperation, PerformanceMetrics[]>;

  constructor(
    themePerformanceMonitor: ThemePerformanceMonitor,
    _themeBenchmarkRunner: ThemeBenchmarkRunner,
    domOptimizer: DOMOptimizer,
    cssOptimizer: CSSOptimizer,
    config?: Partial<PerformanceConfig>
  ) {
    this.batchManager = new BatchManager({
      enablePerfLogging: true,
      maxBatchTime: 16,
      usePriorityQueue: true
    });

    this.themePerformanceMonitor = themePerformanceMonitor;
    this.domOptimizer = domOptimizer;
    this.cssOptimizer = cssOptimizer;
    this.config = { ...PerformanceIntegration.createDefaultConfig(), ...config };
    this.performanceHistory = new Map();
  }

  /**
   * Measure an asynchronous operation using both performance systems
   */
  async measureOperation<T>(
    operation: PerformanceOperation,
    fn: () => Promise<T>,
    context?: Record<string, unknown>
  ): Promise<T> {
    const startTime = performance.now();

    try {
      // Use theme performance monitor for theme-related operations
      if (this.isThemeOperation(operation)) {
        const themeOp = this.mapToThemeOperation(operation);
        return await this.themePerformanceMonitor.measureOperation(themeOp, fn, context);
      }

      // Use general performance monitoring for other operations
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const metrics: PerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: typeof context?.elementCount === 'number' ? context.elementCount : undefined,
        meetsBenchmark: duration <= (this.config.thresholds.get(operation) || 100),
        benchmark: this.config.thresholds.get(operation) || 100
      };

      this.recordMetrics(operation, metrics);
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Record failed operation metrics
      const metrics: PerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: typeof context?.elementCount === 'number' ? context.elementCount : undefined,
        meetsBenchmark: false,
        benchmark: this.config.thresholds.get(operation) || 100
      };

      this.recordMetrics(operation, metrics);
      throw error;
    }
  }

  /**
   * Measure a synchronous operation using both performance systems
   */
  measureOperationSync<T>(
    operation: PerformanceOperation,
    fn: () => T,
    context?: Record<string, unknown>
  ): T {
    const startTime = performance.now();

    try {
      // Use theme performance monitor for theme-related operations
      if (this.isThemeOperation(operation)) {
        const themeOp = this.mapToThemeOperation(operation);
        return this.themePerformanceMonitor.measureOperationSync(themeOp, fn, context);
      }

      // Use general performance monitoring for other operations
      const result = fn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const metrics: PerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: typeof context?.elementCount === 'number' ? context.elementCount : undefined,
        meetsBenchmark: duration <= (this.config.thresholds.get(operation) || 100),
        benchmark: this.config.thresholds.get(operation) || 100
      };

      this.recordMetrics(operation, metrics);
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Record failed operation metrics
      const metrics: PerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: typeof context?.elementCount === 'number' ? context.elementCount : undefined,
        meetsBenchmark: false,
        benchmark: this.config.thresholds.get(operation) || 100
      };

      this.recordMetrics(operation, metrics);
      throw error;
    }
  }

  /**
   * Set performance threshold for an operation
   */
  setPerformanceThreshold(operation: PerformanceOperation, thresholdMs: number): void {
    this.config.thresholds.set(operation, thresholdMs);

    // Also update theme performance monitor if it's a theme operation
    if (this.isThemeOperation(operation)) {
      const themeOp = this.mapToThemeOperation(operation);
      this.themePerformanceMonitor.setPerformanceThreshold(themeOp, thresholdMs);
    }
  }

  /**
   * Get performance statistics from both systems
   */
  getPerformanceStats() {
    const themeStats = this.themePerformanceMonitor.getPerformanceStats();
    const generalStats = this.calculateGeneralStats();

    // Merge the maps properly
    const mergedOperationCounts = new Map<string, number>();
    const mergedAverageDurations = new Map<string, number>();
    const mergedBenchmarkViolations = new Map<string, number>();

    // Add theme stats (convert from Record to Map)
    for (const [key, value] of Object.entries(themeStats.operationCounts)) {
      mergedOperationCounts.set(key, value);
    }
    for (const [key, value] of Object.entries(themeStats.averageDurations)) {
      mergedAverageDurations.set(key, value);
    }
    for (const [key, value] of Object.entries(themeStats.benchmarkViolations)) {
      mergedBenchmarkViolations.set(key, value);
    }

    // Add general stats
    for (const [key, value] of generalStats.operationCounts) {
      mergedOperationCounts.set(key, (mergedOperationCounts.get(key) || 0) + value);
    }
    for (const [key, value] of generalStats.averageDurations) {
      const existing = mergedAverageDurations.get(key) || 0;
      mergedAverageDurations.set(key, (existing + value) / 2); // Average the averages
    }
    for (const [key, value] of generalStats.benchmarkViolations) {
      mergedBenchmarkViolations.set(key, (mergedBenchmarkViolations.get(key) || 0) + value);
    }

    return {
      operationCounts: mergedOperationCounts,
      averageDurations: mergedAverageDurations,
      benchmarkViolations: mergedBenchmarkViolations,
      memoryTrend: this.calculateMemoryTrend()
    };
  }

  /**
   * Run a performance benchmark using integrated systems
   */
  async runBenchmark(
    operation: PerformanceOperation,
    testFunction: () => Promise<void> | void,
    iterations: number = 10
  ): Promise<BenchmarkResult> {
    const benchmarkId = `${operation}_${Date.now()}`;
    const results: PerformanceMetrics[] = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      try {
        await testFunction();
        const endTime = performance.now();
        const duration = endTime - startTime;

        const metrics: PerformanceMetrics = {
          startTime,
          endTime,
          duration,
          memoryBefore: this.getMemoryUsage(),
          memoryAfter: this.getMemoryUsage(),
          meetsBenchmark: duration <= (this.config.thresholds.get(operation) || 100),
          benchmark: this.config.thresholds.get(operation) || 100
        };

        results.push(metrics);
      } catch (error) {
        console.error(`Benchmark iteration ${i} failed:`, error);
      }
    }

    // Calculate average metrics
    const avgDuration = results.reduce((sum, m) => sum + m.duration, 0) / results.length;
    const avgMetrics: PerformanceMetrics = {
      startTime: results[0].startTime,
      endTime: results[results.length - 1].endTime,
      duration: avgDuration,
      memoryBefore: results[0].memoryBefore,
      memoryAfter: results[results.length - 1].memoryAfter,
      elementCount: results[0].elementCount,
      meetsBenchmark: avgDuration <= (this.config.thresholds.get(operation) || 100),
      benchmark: this.config.thresholds.get(operation) || 100
    };

    const comparison = this.compareWithBaseline(operation, avgMetrics);

    return {
      id: benchmarkId,
      operation,
      metrics: avgMetrics,
      isRegression: comparison.isRegression,
      changeFromBaseline: comparison.changeFromBaseline,
      context: { iterations, results }
    };
  }

  /**
   * Compare current performance against historical data
   */
  compareWithBaseline(
    operation: PerformanceOperation,
    currentMetrics: PerformanceMetrics
  ): {
    isRegression: boolean;
    changeFromBaseline: number;
    recommendation?: string;
  } {
    const history = this.performanceHistory.get(operation) || [];
    if (history.length === 0) {
      return {
        isRegression: false,
        changeFromBaseline: 0,
        recommendation: 'No baseline data available'
      };
    }

    const baseline = history.reduce((sum, m) => sum + m.duration, 0) / history.length;
    const changeFromBaseline = ((currentMetrics.duration - baseline) / baseline) * 100;
    const isRegression = changeFromBaseline > 15; // 15% slower is considered a regression

    let recommendation: string | undefined;
    if (isRegression) {
      recommendation = 'Performance regression detected. Consider optimization.';
    } else if (changeFromBaseline < -10) {
      recommendation = 'Performance improvement detected. Good work!';
    }

    return {
      isRegression,
      changeFromBaseline,
      recommendation
    };
  }

  /**
   * Get historical benchmark data
   */
  getHistoricalData(_operation: PerformanceOperation): BenchmarkResult[] {
    // This would typically come from persistent storage
    // For now, return empty array as placeholder
    return [];
  }

  /**
   * Batch DOM operations for performance using integrated systems
   */
  async batchDOMOperations(operations: Array<() => void>): Promise<void> {
    // Use batch manager to schedule operations
    operations.forEach((op, index) => {
      this.batchManager.write(`batch_op_${Date.now()}_${index}`, op);
    });

    // Operations will be automatically flushed on next animation frame
    // Return a promise that resolves after a frame to ensure operations complete
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        resolve();
      });
    });
  }

  /**
   * Optimize element creation for performance
   */
  optimizeElementCreation(count: number, factory: () => HTMLElement): HTMLElement[] {
    return this.measureOperationSync(PerformanceOperation.ELEMENT_CREATE, () => {
      const elements: HTMLElement[] = [];

      // For large batches, create elements in chunks to avoid blocking
      if (count > 10) {
        const chunkSize = 5;
        for (let i = 0; i < count; i += chunkSize) {
          const chunkEnd = Math.min(i + chunkSize, count);
          for (let j = i; j < chunkEnd; j++) {
            const element = factory();
            // Register element with DOM optimizer for tracking
            this.domOptimizer.registerElement(element, 'dynamic');
            elements.push(element);
          }
        }
      } else {
        // Use regular creation for small batches
        for (let i = 0; i < count; i++) {
          elements.push(factory());
        }
      }

      return elements;
    }, { elementCount: count });
  }

  /**
   * Monitor DOM mutation performance
   */
  monitorDOMMutations(target: Element, callback: (metrics: PerformanceMetrics) => void): () => void {
    // Create a MutationObserver to monitor changes
    const observer = new MutationObserver((mutations) => {
      const startTime = performance.now();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const metrics: PerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: mutations.length,
        meetsBenchmark: duration <= 1, // 1ms threshold for mutations
        benchmark: 1
      };

      callback(metrics);
    });

    observer.observe(target, {
      childList: true,
      attributes: true,
      subtree: true
    });

    // Return cleanup function
    return () => {
      observer.disconnect();
    };
  }

  /**
   * Get batch manager for direct access
   */
  getBatchManager(): BatchManager {
    return this.batchManager;
  }

  /**
   * Get CSS optimizer for direct access
   */
  getCSSOptimizer(): CSSOptimizer {
    return this.cssOptimizer;
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.batchManager.destroy();
    // DOMOptimizer doesn't have a destroy method, so we'll just clear our references
    this.performanceHistory.clear();
  }

  // Private helper methods
  private isThemeOperation(operation: PerformanceOperation): boolean {
    return [
      PerformanceOperation.THEME_SWITCH,
      PerformanceOperation.THEME_LOAD,
      PerformanceOperation.THEME_SAVE,
      PerformanceOperation.THEME_VALIDATE,
      PerformanceOperation.CSS_INJECT
    ].includes(operation);
  }

  private mapToThemeOperation(operation: PerformanceOperation): ThemeOperation {
    // Map to theme operation enum
    const mapping: Partial<Record<PerformanceOperation, ThemeOperation>> = {
      [PerformanceOperation.THEME_SWITCH]: ThemeOperation.THEME_SWITCH,
      [PerformanceOperation.THEME_LOAD]: ThemeOperation.THEME_LOAD,
      [PerformanceOperation.THEME_SAVE]: ThemeOperation.THEME_SAVE,
      [PerformanceOperation.THEME_VALIDATE]: ThemeOperation.THEME_VALIDATE,
      [PerformanceOperation.CSS_INJECT]: ThemeOperation.CSS_INJECT
    };

    return mapping[operation] || ThemeOperation.THEME_SWITCH; // Default fallback
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const perfWithMemory = performance as typeof performance & {
        memory: { usedJSHeapSize: number };
      };
      return perfWithMemory.memory.usedJSHeapSize;
    }
    return 0;
  }

  private recordMetrics(operation: PerformanceOperation, metrics: PerformanceMetrics): void {
    if (!this.performanceHistory.has(operation)) {
      this.performanceHistory.set(operation, []);
    }

    const history = this.performanceHistory.get(operation)!;
    history.push(metrics);

    // Keep only the last N records to prevent memory leaks
    if (history.length > this.config.maxRecords) {
      history.shift();
    }
  }

  private calculateGeneralStats() {
    const operationCounts = new Map<string, number>();
    const averageDurations = new Map<string, number>();
    const benchmarkViolations = new Map<string, number>();

    for (const [operation, metrics] of this.performanceHistory) {
      const opName = operation.toString();
      operationCounts.set(opName, metrics.length);

      const avgDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length;
      averageDurations.set(opName, avgDuration);

      const violations = metrics.filter(m => !m.meetsBenchmark).length;
      benchmarkViolations.set(opName, violations);
    }

    return { operationCounts, averageDurations, benchmarkViolations };
  }

  private calculateMemoryTrend(): 'increasing' | 'decreasing' | 'stable' {
    // Simple memory trend calculation based on recent metrics
    const recentMetrics: PerformanceMetrics[] = [];
    for (const metrics of this.performanceHistory.values()) {
      recentMetrics.push(...metrics.slice(-5)); // Last 5 metrics per operation
    }

    if (recentMetrics.length < 2) return 'stable';

    const memoryValues = recentMetrics
      .map(m => m.memoryAfter || 0)
      .filter(m => m > 0);

    if (memoryValues.length < 2) return 'stable';

    const trend = memoryValues[memoryValues.length - 1] - memoryValues[0];
    const threshold = memoryValues[0] * 0.05; // 5% change threshold

    if (trend > threshold) return 'increasing';
    if (trend < -threshold) return 'decreasing';
    return 'stable';
  }
}
