import puppeteer from 'puppeteer';
import { PerformanceMetrics, PerformanceOperation, IBenchmarkRunner, BenchmarkResult } from './shared-performance-types';

/**
 * Enhanced benchmark runner that integrates with the unified performance system
 */
export class EnhancedBenchmarkRunner implements IBenchmarkRunner {
  private historicalData: Map<PerformanceOperation, BenchmarkResult[]> = new Map();

  /**
   * Run a performance benchmark
   */
  async runBenchmark(
    operation: PerformanceOperation,
    testFunction: () => Promise<void> | void,
    iterations: number = 10
  ) {
    const benchmarkId = `${operation}_${Date.now()}`;
    const results: PerformanceMetrics[] = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();

      try {
        await testFunction();
        const endTime = performance.now();
        const duration = endTime - startTime;

        const metrics: PerformanceMetrics = {
          startTime,
          endTime,
          duration,
          memoryBefore: this.getMemoryUsage(),
          memoryAfter: this.getMemoryUsage(),
          meetsBenchmark: duration <= this.getThreshold(operation),
          benchmark: this.getThreshold(operation)
        };

        results.push(metrics);
      } catch (error) {
        console.error(`Benchmark iteration ${i} failed:`, error);
      }
    }

    // Calculate average metrics
    const avgDuration = results.reduce((sum, m) => sum + m.duration, 0) / results.length;
    const avgMetrics: PerformanceMetrics = {
      startTime: results[0]?.startTime || 0,
      endTime: results[results.length - 1]?.endTime || 0,
      duration: avgDuration,
      memoryBefore: results[0]?.memoryBefore,
      memoryAfter: results[results.length - 1]?.memoryAfter,
      meetsBenchmark: avgDuration <= this.getThreshold(operation),
      benchmark: this.getThreshold(operation)
    };

    const comparison = this.compareWithBaseline(operation, avgMetrics);

    return {
      id: benchmarkId,
      operation,
      metrics: avgMetrics,
      isRegression: comparison.isRegression,
      changeFromBaseline: comparison.changeFromBaseline,
      context: { iterations, results }
    };
  }

  /**
   * Compare current performance against historical data
   */
  compareWithBaseline(operation: PerformanceOperation, currentMetrics: PerformanceMetrics) {
    const history = this.historicalData.get(operation) || [];
    if (history.length === 0) {
      return {
        isRegression: false,
        changeFromBaseline: 0,
        recommendation: 'No baseline data available'
      };
    }

    const baseline = history.reduce((sum, m) => sum + m.metrics.duration, 0) / history.length;
    const changeFromBaseline = ((currentMetrics.duration - baseline) / baseline) * 100;
    const isRegression = changeFromBaseline > 15; // 15% slower is considered a regression

    let recommendation: string | undefined;
    if (isRegression) {
      recommendation = 'Performance regression detected. Consider optimization.';
    } else if (changeFromBaseline < -10) {
      recommendation = 'Performance improvement detected. Good work!';
    }

    return {
      isRegression,
      changeFromBaseline,
      recommendation
    };
  }

  /**
   * Get historical benchmark data
   */
  getHistoricalData(operation: PerformanceOperation) {
    return this.historicalData.get(operation) || [];
  }



  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const perfWithMemory = performance as typeof performance & {
        memory: { usedJSHeapSize: number };
      };
      return perfWithMemory.memory.usedJSHeapSize;
    }
    return 0;
  }

  private getThreshold(operation: PerformanceOperation): number {
    const thresholds: Record<PerformanceOperation, number> = {
      [PerformanceOperation.DOM_READ]: 1,
      [PerformanceOperation.DOM_WRITE]: 2,
      [PerformanceOperation.DOM_BATCH]: 16,
      [PerformanceOperation.THEME_SWITCH]: 100,
      [PerformanceOperation.THEME_LOAD]: 50,
      [PerformanceOperation.THEME_SAVE]: 200,
      [PerformanceOperation.THEME_VALIDATE]: 10,
      [PerformanceOperation.ELEMENT_CREATE]: 1,
      [PerformanceOperation.CSS_INJECT]: 10,
      [PerformanceOperation.PLUGIN_APPLY]: 50,
      [PerformanceOperation.VIEWPORT_UPDATE]: 16,
      [PerformanceOperation.BENCHMARK_RUN]: 1000,
      [PerformanceOperation.ELEMENT_UPDATE]: 2,
      [PerformanceOperation.ELEMENT_REMOVE]: 1,
      [PerformanceOperation.CSS_OPTIMIZE]: 10,
      [PerformanceOperation.PLUGIN_RENDER]: 50,
      [PerformanceOperation.VIEWPORT_SCROLL]: 16,
      [PerformanceOperation.BENCHMARK_COMPARE]: 100
    };

    return thresholds[operation] || 100;
  }
}

/**
 * Legacy benchmark function for backward compatibility
 * Run performance benchmarks using Puppeteer
 */
export async function runBenchmarks() {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  // Enable performance metrics
  await page.emulateCPUThrottling(4);
  await page.coverage.startJSCoverage();

  const metrics = {
    initialRender: 0,
    editLatency: 0,
    scrollFrameDrops: 0
  };

  // Measure initial render time
  const start: number = performance.now();
  await page.goto('http://localhost:3000');
  metrics.initialRender = performance.now() - start;

  // Measure edit latency
  const editLatencies: number[] = [];
  for (let i = 0; i < 100; i++) {
    const before: number = performance.now();
    await page.type('#editor', 'test');
    editLatencies.push(performance.now() - before);
  }
  metrics.editLatency = editLatencies.reduce((a, b) => a + b) / editLatencies.length;

  // Measure scroll performance
  const scrollMetrics = await page.evaluate((): Promise<number[]> => {
    const results: number[] = [];
    let lastFrameTime: number = performance.now();

    return new Promise(resolve => {
      requestAnimationFrame(function measure() {
        const now: number = performance.now();
        results.push(now - lastFrameTime);
        lastFrameTime = now;

        if (results.length < 100) {
          requestAnimationFrame(measure);
        } else {
          resolve(results);
        }
      });

      // Trigger smooth scroll
      const editorElement = document.getElementById('editor');
      if (editorElement) {
        editorElement.scrollTo({
          top: 10000,
          behavior: 'smooth'
        });
      }
    });
  });

  metrics.scrollFrameDrops = scrollMetrics.filter(t => t > 16.67).length;

  await browser.close();
  return metrics;
}