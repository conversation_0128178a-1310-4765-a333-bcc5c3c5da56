/**
 * Manages virtualized rendering of editor content
 */
import type { ThemeDefinition } from '../themes/theme-types';

// Define types for clarity
interface VisibleRange {
  start: number;
  end: number;
}

type BatchCallback = () => void;

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

export class VirtualViewport {
  // Declare class properties with types
  private readonly container: HTMLElement;
  private readonly bufferSize: number;
  private lines: string[] = [];
  private visibleRange: VisibleRange = { start: 0, end: 0 };
  private readonly batchQueue: Set<BatchCallback> = new Set();
  private isUpdating: boolean = false;
  private observer: IntersectionObserver | null = null;
  private mutationObserver: MutationObserver | null = null;

  // Theme integration
  private themeManager?: IThemeManager;
  private themeUnsubscribe?: () => void;
  private currentTheme: ThemeDefinition | null = null;

  /**
   * Create a new virtual viewport
   * @param {HTMLElement} container - The editor container
   * @param {number} bufferSize - Number of lines to render above/below viewport
   * @param {IThemeManager} themeManager - Optional theme manager for theme integration
   */
  constructor(container: HTMLElement, bufferSize = 50, themeManager?: IThemeManager) {
    this.container = container;
    this.bufferSize = bufferSize;

    // Set up theme integration if theme manager is provided
    this.themeManager = themeManager;
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }

    this._setupIntersectionObserver();
    this._setupMutationObserver();
    this._bindScrollHandler();
  }

  /**
   * Initialize with content
   * @param {string[]} lines - Array of HTML strings for each line
   */
  setContent(lines: string[]) {
    this.lines = lines;
    this._updateVisibleRange();
    this._scheduleUpdate();
  }

  /**
   * Schedule a DOM update
   * @private
   */
  _scheduleUpdate() {
    if (this.isUpdating) return;

    this.isUpdating = true;
    requestAnimationFrame(() => {
      this._updateDOM();
      this.isUpdating = false;
    });
  }

  /**
   * Update the visible range based on scroll position
   * @private
   */
  _updateVisibleRange() {
    const containerRect = this.container.getBoundingClientRect();
    const lineHeight = 20; // Approximate line height

    const start = Math.max(0,
      Math.floor(this.container.scrollTop / lineHeight) - this.bufferSize
    );

    const visibleLines = Math.ceil(containerRect.height / lineHeight);
    const end = Math.min(
      this.lines.length,
      start + visibleLines + (this.bufferSize * 2)
    );

    this.visibleRange = { start, end };
  }

  /**
   * Update the DOM with visible content
   * @private
   */
  _updateDOM() {
    // Create document fragment for batch update
    const fragment = document.createDocumentFragment();

    // Add placeholder for maintaining scroll height
    const totalHeight = this.lines.length * 20; // line height
    const placeholder = document.createElement('div');
    placeholder.style.height = `${totalHeight}px`;
    placeholder.style.contain = 'strict';
    fragment.appendChild(placeholder);

    // Add visible lines
    for (let i = this.visibleRange.start; i < this.visibleRange.end; i++) {
      if (i >= 0 && i < this.lines.length) {
        const line = document.createElement('div');
        // Ensure lines[i] exists before assigning
        line.innerHTML = this.lines[i] ?? '';
        line.style.transform = `translateY(${i * 20}px)`; // Position absolutely
        line.style.position = 'absolute';
        line.style.left = '0';
        line.style.right = '0';
        line.style.willChange = 'transform'; // GPU hint

        // Apply theme-aware styling
        this._applyThemeToLine(line);

        fragment.appendChild(line);
      }
    }

    // Batch DOM update
    this.container.innerHTML = '';
    this.container.appendChild(fragment);
  }

  /**
   * Set up intersection observer for viewport tracking
   * @private
   */
  _setupIntersectionObserver() {
    this.observer = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this._updateVisibleRange();
          this._scheduleUpdate();
        }
      });
    }, {
      root: this.container,
      threshold: [0, 0.25, 0.5, 0.75, 1]
    });

    // Observe only if observer was successfully created
    this.observer?.observe(this.container);
  }

  /**
   * Set up mutation observer for content changes
   * @private
   */
  _setupMutationObserver() {
    this.mutationObserver = new MutationObserver((mutations: MutationRecord[]) => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          this.batchQueue.add(() => this._updateVisibleRange());
          this._scheduleUpdate();
        }
      });
    });

    // Observe only if observer was successfully created
    this.mutationObserver?.observe(this.container, {
      childList: true,
      subtree: true
    });
  }

  /**
   * Bind scroll handler
   * @private
   */
  _bindScrollHandler() {
    let scrollTimeout: number | undefined;

    this.container.addEventListener('scroll', () => {
      if (scrollTimeout) {
        cancelAnimationFrame(scrollTimeout);
      }

      scrollTimeout = requestAnimationFrame(() => {
        this._updateVisibleRange();
        this._scheduleUpdate();
      });
    }, { passive: true });
  }

  /**
   * Clean up observers and theme integration
   */
  destroy() {
    this.observer?.disconnect();
    this.mutationObserver?.disconnect();

    // Clean up theme integration
    if (this.themeUnsubscribe) {
      this.themeUnsubscribe();
      this.themeUnsubscribe = undefined;
    }
    this.themeManager = undefined;
    this.currentTheme = null;
  }

  /**
   * Get the current theme (for theme integration)
   */
  getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Handle theme changes and update virtual viewport styling
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;

      // Re-render with new theme
      this._scheduleUpdate();
    } catch (error) {
      console.error('[VirtualViewport] Error handling theme change:', error);
    }
  }

  /**
   * Apply theme-aware styling to a line element
   * @param line - Line element to style
   * @private
   */
  private _applyThemeToLine(line: HTMLElement): void {
    if (!this.currentTheme) return;

    try {
      // Apply theme-aware CSS variables for virtual viewport lines
      line.style.setProperty('--viewport-line-bg', 'var(--editor-content-bg, transparent)');
      line.style.setProperty('--viewport-line-text', 'var(--editor-content-text, inherit)');
      line.style.setProperty('--viewport-line-border', 'var(--editor-border, transparent)');

      // Add theme class for CSS targeting
      line.classList.add('virtual-viewport-line');
      line.classList.add(`theme-${this.currentTheme.id}`);

      // Apply background and text colors from theme
      line.style.backgroundColor = 'var(--viewport-line-bg)';
      line.style.color = 'var(--viewport-line-text)';

      // Add subtle border for line separation in some themes
      if (this.currentTheme.id === 'dark') {
        line.style.borderBottom = '1px solid var(--viewport-line-border)';
      }
    } catch (error) {
      console.error('[VirtualViewport] Error applying theme to line:', error);
    }
  }
}