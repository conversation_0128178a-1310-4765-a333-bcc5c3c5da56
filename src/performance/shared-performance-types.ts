/**
 * Shared Performance Types and Interfaces
 * Provides common types and interfaces for integration between src/performance/ and src/themes/performance/
 * Follows Single Responsibility Principle by focusing solely on type definitions.
 */

/**
 * Common performance metrics interface used across both performance directories
 */
export interface PerformanceMetrics {
  /** Operation start time in milliseconds */
  startTime: number;
  /** Operation end time in milliseconds */
  endTime: number;
  /** Operation duration in milliseconds */
  duration: number;
  /** Memory usage before operation (if available) */
  memoryBefore?: number;
  /** Memory usage after operation (if available) */
  memoryAfter?: number;
  /** Number of elements affected by the operation */
  elementCount?: number;
  /** Whether the operation meets performance benchmarks */
  meetsBenchmark: boolean;
  /** The benchmark threshold used for comparison */
  benchmark: number;
}

/**
 * Performance operation types that can be measured
 */
export enum PerformanceOperation {
  // DOM Operations
  DOM_READ = 'dom_read',
  DOM_WRITE = 'dom_write',
  DOM_BATCH = 'dom_batch',
  
  // Theme Operations
  THEME_SWITCH = 'theme_switch',
  THEME_LOAD = 'theme_load',
  THEME_SAVE = 'theme_save',
  THEME_VALIDATE = 'theme_validate',
  
  // Element Operations
  ELEMENT_CREATE = 'element_create',
  ELEMENT_UPDATE = 'element_update',
  ELEMENT_REMOVE = 'element_remove',
  
  // CSS Operations
  CSS_INJECT = 'css_inject',
  CSS_OPTIMIZE = 'css_optimize',
  
  // Plugin Operations
  PLUGIN_APPLY = 'plugin_apply',
  PLUGIN_RENDER = 'plugin_render',
  
  // Virtual Viewport Operations
  VIEWPORT_UPDATE = 'viewport_update',
  VIEWPORT_SCROLL = 'viewport_scroll',
  
  // Benchmark Operations
  BENCHMARK_RUN = 'benchmark_run',
  BENCHMARK_COMPARE = 'benchmark_compare'
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceConfig {
  /** Whether performance monitoring is enabled */
  enabled: boolean;
  /** Default performance thresholds in milliseconds */
  thresholds: Map<PerformanceOperation, number>;
  /** Whether to collect memory usage data */
  collectMemoryData: boolean;
  /** Maximum number of performance records to keep in memory */
  maxRecords: number;
  /** Whether to log performance violations */
  logViolations: boolean;
}

/**
 * Performance benchmark result
 */
export interface BenchmarkResult {
  /** Unique identifier for the benchmark */
  id: string;
  /** Operation that was benchmarked */
  operation: PerformanceOperation;
  /** Performance metrics collected */
  metrics: PerformanceMetrics;
  /** Whether this is a regression compared to historical data */
  isRegression: boolean;
  /** Percentage change from baseline (positive = slower, negative = faster) */
  changeFromBaseline?: number;
  /** Additional context about the benchmark */
  context?: Record<string, any>;
}

/**
 * Performance monitoring interface for cross-directory integration
 */
export interface IPerformanceMonitor {
  /**
   * Measure an asynchronous operation
   */
  measureOperation<T>(
    operation: PerformanceOperation,
    fn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T>;

  /**
   * Measure a synchronous operation
   */
  measureOperationSync<T>(
    operation: PerformanceOperation,
    fn: () => T,
    context?: Record<string, any>
  ): T;

  /**
   * Set performance threshold for an operation
   */
  setPerformanceThreshold(operation: PerformanceOperation, thresholdMs: number): void;

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    operationCounts: Map<string, number>;
    averageDurations: Map<string, number>;
    benchmarkViolations: Map<string, number>;
    memoryTrend: 'increasing' | 'decreasing' | 'stable';
  };
}

/**
 * Benchmark runner interface for cross-directory integration
 */
export interface IBenchmarkRunner {
  /**
   * Run a performance benchmark
   */
  runBenchmark(
    operation: PerformanceOperation,
    testFunction: () => Promise<void> | void,
    iterations?: number
  ): Promise<BenchmarkResult>;

  /**
   * Compare current performance against historical data
   */
  compareWithBaseline(
    operation: PerformanceOperation,
    currentMetrics: PerformanceMetrics
  ): {
    isRegression: boolean;
    changeFromBaseline: number;
    recommendation?: string;
  };

  /**
   * Get historical benchmark data
   */
  getHistoricalData(operation: PerformanceOperation): BenchmarkResult[];
}

/**
 * DOM optimization interface for cross-directory integration
 */
export interface IDOMOptimizer {
  /**
   * Batch DOM operations for performance
   */
  batchDOMOperations(operations: Array<() => void>): Promise<void>;

  /**
   * Optimize element creation for performance
   */
  optimizeElementCreation(count: number, factory: () => HTMLElement): HTMLElement[];

  /**
   * Monitor DOM mutation performance
   */
  monitorDOMMutations(target: Element, callback: (metrics: PerformanceMetrics) => void): () => void;
}

/**
 * Performance integration utilities
 */
export class PerformanceIntegration {
  /**
   * Convert theme operation to performance operation
   */
  static mapThemeToPerformanceOperation(themeOp: string): PerformanceOperation {
    const mapping: Record<string, PerformanceOperation> = {
      'theme_switch': PerformanceOperation.THEME_SWITCH,
      'theme_load': PerformanceOperation.THEME_LOAD,
      'theme_save': PerformanceOperation.THEME_SAVE,
      'theme_validate': PerformanceOperation.THEME_VALIDATE,
      'element_create': PerformanceOperation.ELEMENT_CREATE,
      'css_inject': PerformanceOperation.CSS_INJECT,
      'plugin_apply': PerformanceOperation.PLUGIN_APPLY
    };

    return mapping[themeOp] || PerformanceOperation.DOM_BATCH;
  }

  /**
   * Create default performance configuration
   */
  static createDefaultConfig(): PerformanceConfig {
    const thresholds = new Map<PerformanceOperation, number>([
      [PerformanceOperation.DOM_READ, 1],
      [PerformanceOperation.DOM_WRITE, 2],
      [PerformanceOperation.DOM_BATCH, 16],
      [PerformanceOperation.THEME_SWITCH, 100],
      [PerformanceOperation.THEME_LOAD, 50],
      [PerformanceOperation.THEME_SAVE, 200],
      [PerformanceOperation.THEME_VALIDATE, 10],
      [PerformanceOperation.ELEMENT_CREATE, 1],
      [PerformanceOperation.CSS_INJECT, 10],
      [PerformanceOperation.PLUGIN_APPLY, 50],
      [PerformanceOperation.VIEWPORT_UPDATE, 16],
      [PerformanceOperation.BENCHMARK_RUN, 1000]
    ]);

    return {
      enabled: true,
      thresholds,
      collectMemoryData: true,
      maxRecords: 1000,
      logViolations: true
    };
  }

  /**
   * Merge performance metrics from different sources
   */
  static mergeMetrics(metrics1: PerformanceMetrics, metrics2: PerformanceMetrics): PerformanceMetrics {
    return {
      startTime: Math.min(metrics1.startTime, metrics2.startTime),
      endTime: Math.max(metrics1.endTime, metrics2.endTime),
      duration: metrics1.duration + metrics2.duration,
      memoryBefore: metrics1.memoryBefore || metrics2.memoryBefore,
      memoryAfter: metrics2.memoryAfter || metrics1.memoryAfter,
      elementCount: (metrics1.elementCount || 0) + (metrics2.elementCount || 0),
      meetsBenchmark: metrics1.meetsBenchmark && metrics2.meetsBenchmark,
      benchmark: Math.max(metrics1.benchmark, metrics2.benchmark)
    };
  }
}
