import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
  type Mock,
} from 'vitest';
import { ClipboardManager } from './clipboard-manager';
import type {
  ISelectionManager,
  IRenderer,
} from '../types';
import type { ThemeDefinition } from '../themes/theme-types';
import '../setupTests'; // Explicitly import setupTests

// --- Mocks ---

// Mock theme manager interface
interface MockThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

// Mock theme definitions
const mockLightTheme: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  version: '1.0.0',
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#757575',
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    info: '#2196f3'
  },
  stateColors: {
    hover: { background: '#f5f5f5', border: '#d0d0d0', text: '#333333' },
    focus: { background: '#ffffff', border: '#1565c0', ring: '#3b82f6' },
    active: { background: '#e3f2fd', border: '#1565c0', text: '#333333' },
    disabled: { background: '#f5f5f5', border: '#e0e0e0', text: '#9e9e9e' }
  },
  pluginColors: {
    palette: { background: '#ffffff', border: '#e0e0e0', shadow: '0 2px 8px rgba(0,0,0,0.1)', tabActiveBorder: '#1565c0', swatchBorder: 'rgba(0,0,0,0.1)' },
    chart: { background: '#ffffff', gridLines: '#e0e0e0', dataColors: ['#1565c0', '#4caf50', '#ff9800'] },
    code: { background: '#f8f9fa', border: '#e0e0e0', text: '#333333', keyword: '#1565c0', string: '#4caf50', comment: '#757575' },
    table: { background: '#ffffff', border: '#e0e0e0', headerBackground: '#f8f9fa', alternateRowBackground: '#f8f9fa' },
    comments: { background: '#ffffff', border: '#e0e0e0', highlightBackground: '#fff3cd', avatarBackground: '#f8f9fa' }
  },
  animation: { duration: 300, easing: 'ease-in-out', respectReducedMotion: true }
};

const mockDarkTheme: ThemeDefinition = {
  ...mockLightTheme,
  id: 'dark',
  name: 'Dark Theme',
  colors: {
    ...mockLightTheme.colors,
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d'
  }
};

// Basic Mock Selection Manager (Expand with necessary methods/props)
class MockSelectionManager implements ISelectionManager {
  getRange = vi.fn();
  getSelection = vi.fn();
  getSelectedText = vi.fn();
  getSelectedHtml = vi.fn();
  isCollapsed = vi.fn().mockReturnValue(false);
  selectRange = vi.fn();
  setRange = vi.fn();
  initialize = vi.fn(); // Added
  destroy = vi.fn(); // Added
  applyState = vi.fn(); // Added
  deleteSelection = vi.fn(() => Promise.resolve()); // Add missing method
}

// Basic Mock Renderer (Expand with necessary methods/props)
class MockRenderer implements IRenderer {
  getElement = vi.fn();
  render = vi.fn();
  insertNode = vi.fn(); // Added
  insertHtml = vi.fn((html: string) => {
    // Simulate basic sanitization/handling if needed for tests
    return `Sanitized: ${html}`;
  });
  insertText = vi.fn(); // Added missing method
  deleteContents = vi.fn();
  initialize = vi.fn(); // Added
  destroy = vi.fn(); // Added
  applyState = vi.fn(); // Added
  getContent = vi.fn(); // Added
  setContent = vi.fn(); // Added
  focus = vi.fn(); // Added
  scrollIntoView = vi.fn(); // Added missing method
}

// --- Tests ---

describe('Clipboard Manager', () => {
  let editorElement: HTMLDivElement;
  let mockSelectionManager: MockSelectionManager;
  let mockRenderer: MockRenderer;
  let clipboardManager: ClipboardManager;
  let mockThemeManager: MockThemeManager;
  let themeChangeCallback: (theme: ThemeDefinition) => void;
  let writeSpy: any;

  // Helper to create a mock ClipboardEvent
  const mockClipboardEvent = (type: 'copy' | 'cut' | 'paste'): ClipboardEvent => {
    // Recreate mockDataTransfer for each event to isolate tests
    const eventDataTransfer = {
      getData: vi.fn(),
      setData: vi.fn(),
      types: [] as readonly string[],
      items: [] as unknown as DataTransferItemList,
      files: [] as unknown as FileList,
      dropEffect: 'none',
      effectAllowed: 'all',
      clearData: vi.fn(),
      setDragImage: vi.fn(),
    } as unknown as DataTransfer; // Use unknown cast for simplicity

    // Use createEvent/initEvent instead of constructor
    const event = document.createEvent('Event');
    event.initEvent(type, true, true); // type, bubbles, cancelable

    // Manually assign clipboardData
    Object.defineProperty(event, 'clipboardData', {
        value: eventDataTransfer, // Assign the locally created mock
        writable: false, // Typically read-only after creation
        configurable: true,
    });

    // Add preventDefault spy - ensure preventDefault exists before spying
    if (typeof event.preventDefault !== 'function') {
        // Add a basic preventDefault if missing (might happen with basic Event)
        Object.defineProperty(event, 'preventDefault', {
            value: vi.fn(),
            writable: true,
            configurable: true,
        });
    } else {
        vi.spyOn(event, 'preventDefault');
    }

    // Cast to ClipboardEvent for return type compatibility
    return event as ClipboardEvent;
  };

  beforeEach(() => {
    // Clean up any existing feedback elements
    document.querySelectorAll('[data-clipboard-feedback]').forEach(el => el.remove());

    // Setup DOM element
    editorElement = document.createElement('div');
    document.body.appendChild(editorElement);

    // Mock dependencies
    mockSelectionManager = new MockSelectionManager();
    mockSelectionManager.getRange = vi.fn().mockReturnValue({ /* mock Range */ } as unknown as Range);
    mockSelectionManager.getSelectedHtml = vi.fn().mockReturnValue('Selected HTML'); // Change getSelectedHtml mocking to synchronous return value
    mockSelectionManager.deleteSelection = vi.fn().mockResolvedValue(undefined);
    mockRenderer = new MockRenderer();
    mockRenderer.insertNode = vi.fn();
    mockRenderer.insertHtml = vi.fn();

    // Mock theme manager
    mockThemeManager = {
      getCurrentTheme: vi.fn(() => mockLightTheme),
      watch: vi.fn((callback) => {
        themeChangeCallback = callback;
        return vi.fn(); // Return unsubscribe function
      })
    };

    // Instantiate the manager with theme manager
    clipboardManager = new ClipboardManager(mockThemeManager);

    // Mock navigator.clipboard *before* initializing
    // Individual tests (like fallback) can override this
    Object.defineProperty(navigator, 'clipboard', {
      value: {
        write: vi.fn().mockResolvedValue(undefined),
        writeText: vi.fn().mockResolvedValue(undefined),
        read: vi.fn().mockResolvedValue([]),
        readText: vi.fn().mockResolvedValue(''),
      },
      writable: true,
      configurable: true,
    });
    // Re-assign spies if needed
    writeSpy = vi.spyOn(navigator.clipboard, 'write');

    // Initialize the manager with mocks
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer);
  });

  afterEach(() => {
    // Clean up listeners
    clipboardManager.destroy();

    // Clean up DOM elements
    if (editorElement && editorElement.parentNode) {
      editorElement.parentNode.removeChild(editorElement);
    }

    // Clean up any feedback elements
    document.querySelectorAll('[data-clipboard-feedback]').forEach(el => {
      if (el.parentNode) {
        el.parentNode.removeChild(el);
      }
    });

    // Restore mocks
    vi.restoreAllMocks();
  });

  // --- Initialization and Destruction Tests ---

  it('should add event listeners on initialize', () => {
    const addEventListenerSpy = vi.spyOn(editorElement, 'addEventListener');
    // Re-initialize to test listener attachment
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer);
    expect(addEventListenerSpy).toHaveBeenCalledWith('copy', expect.any(Function));
    expect(addEventListenerSpy).toHaveBeenCalledWith('cut', expect.any(Function));
    expect(addEventListenerSpy).toHaveBeenCalledWith('paste', expect.any(Function));
  });

  it('should remove event listeners on destroy', () => {
    const removeEventListenerSpy = vi.spyOn(editorElement, 'removeEventListener');

    // Create a new manager for this test to avoid interference with afterEach
    const testManager = new ClipboardManager(mockThemeManager);
    testManager.initialize(editorElement, mockSelectionManager, mockRenderer);

    testManager.destroy();
    expect(removeEventListenerSpy).toHaveBeenCalledWith('copy', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('cut', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('paste', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledTimes(3);

    // Call destroy again to ensure it doesn't try to remove listeners twice
    removeEventListenerSpy.mockClear();
    testManager.destroy();
    expect(removeEventListenerSpy).toHaveBeenCalledTimes(3); // Should be called again since element still exists
  });

  // --- Copy Tests ---

  it('handles copy event with modern API', async () => {
    const testText = 'Modern Copy Text';
    const testHtml = '<b>Modern Copy HTML</b>';
    mockSelectionManager.getSelectedText.mockReturnValue(testText);
    mockSelectionManager.getSelectedHtml.mockReturnValue(testHtml);
    mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);

    // --- Set navigator state for this test ---
    Object.defineProperty(window, 'navigator', {
      value: {
          clipboard: {
              write: vi.fn().mockResolvedValue(undefined),
              writeText: vi.fn().mockResolvedValue(undefined),
              read: vi.fn().mockResolvedValue([]),
              readText: vi.fn().mockResolvedValue(''),
          },
      },
      writable: true, configurable: true,
    });
    writeSpy = vi.spyOn(navigator.clipboard, 'write');
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer);

    const copyEvent = mockClipboardEvent('copy');
    editorElement.dispatchEvent(copyEvent);

    expect(writeSpy).toHaveBeenCalled(); // General check is fine

    // Check data passed to clipboard API
    expect(copyEvent.preventDefault).toHaveBeenCalled();
    expect(writeSpy).toHaveBeenCalledTimes(1);
  });

  it('handles copy event with fallback API', () => {
    const testText = 'Fallback Copy Text';
    const testHtml = '<b>Fallback Copy HTML</b>';
    mockSelectionManager.getSelectedText.mockReturnValue(testText);
    mockSelectionManager.getSelectedHtml.mockReturnValue(testHtml);
    mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);
    const execCommandSpy = vi.spyOn(document, 'execCommand');

    // --- Make navigator.clipboard unavailable for this test ---
    Object.defineProperty(window, 'navigator', {
        value: {},
        writable: true,
        configurable: true,
    });
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer); // Re-initialize

    const copyEvent = mockClipboardEvent('copy');
    editorElement.dispatchEvent(copyEvent);

    expect(copyEvent.preventDefault).toHaveBeenCalled();
    // Check fallback copy happened on the event's clipboardData
    // Fallback doesn't use event.clipboardData.setData reliably
    // Instead, check if execCommand was attempted
    expect(execCommandSpy).not.toHaveBeenCalled(); // Expect NOT called
  });

  it('does not copy if selection is collapsed', async () => {
      mockSelectionManager.isCollapsed.mockReturnValue(true);
      const execCommandSpy = vi.spyOn(document, 'execCommand');

      // --- Set navigator state for this test (modern API available) ---
      Object.defineProperty(window, 'navigator', {
        value: {
            clipboard: {
                write: vi.fn().mockResolvedValue(undefined),
                // ... other methods if needed
            },
        },
        writable: true, configurable: true,
      });
      writeSpy = vi.spyOn(navigator.clipboard, 'write');
      clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer);

      const copyEvent = mockClipboardEvent('copy');
      editorElement.dispatchEvent(copyEvent);

      expect(copyEvent.preventDefault).not.toHaveBeenCalled();
      expect(writeSpy).not.toHaveBeenCalled(); // Check the spy
      // Also check the fallback path wasn't triggered
      expect(copyEvent.clipboardData?.setData).not.toHaveBeenCalled();
      expect(execCommandSpy).not.toHaveBeenCalled();
  });

  // --- Cut Tests ---

  it('handles cut event with modern API', async () => {
    const testText = 'Modern Cut Text';
    const testHtml = '<b>Modern Cut HTML</b>';
    mockSelectionManager.getSelectedText.mockReturnValue(testText);
    mockSelectionManager.getSelectedHtml.mockReturnValue(testHtml);
    mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);
    const dispatchSpy = vi.spyOn(editorElement, 'dispatchEvent');
    const mockRange = mockSelectionManager.getRange(); // Get range from beforeEach setup

    // --- Set navigator state for this test ---
    Object.defineProperty(window, 'navigator', {
      value: {
          clipboard: {
              write: vi.fn().mockResolvedValue(undefined),
              // ... other methods if needed
          },
      },
      writable: true, configurable: true,
    });
    writeSpy = vi.spyOn(navigator.clipboard, 'write');
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer);

    const cutEvent = mockClipboardEvent('cut');
    // Need to dispatch cut *before* mocking clipboard write is checked
    editorElement.dispatchEvent(cutEvent);

    expect(writeSpy).toHaveBeenCalled();

    expect(cutEvent.preventDefault).toHaveBeenCalled();
    expect(writeSpy).toHaveBeenCalledTimes(1);
    // Verify content deletion
    expect(mockRenderer.deleteContents).toHaveBeenCalledWith(mockRange);
    // Verify input event dispatch (check type, ignore the cut event itself)
    expect(dispatchSpy).toHaveBeenCalledWith(expect.any(InputEvent));
    expect(dispatchSpy).toHaveBeenCalledTimes(2); // Cut event + Input event
  });

  it('handles cut event with fallback API', () => {
    const testText = 'Fallback Cut Text';
    const testHtml = '<b>Fallback Cut HTML</b>';
    const mockRange = {} as Range; // Mock range for deletion
    mockSelectionManager.getSelectedText.mockReturnValue(testText);
    mockSelectionManager.getSelectedHtml.mockReturnValue(testHtml);
    mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);
    mockSelectionManager.getRange.mockReturnValue(mockRange);
    const execCommandSpy = vi.spyOn(document, 'execCommand');
    const dispatchSpy = vi.spyOn(editorElement, 'dispatchEvent');

    // --- Make navigator.clipboard unavailable for this test ---
    Object.defineProperty(window, 'navigator', {
        value: {},
        writable: true,
        configurable: true,
    });
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer); // Re-initialize

    const cutEvent = mockClipboardEvent('cut');
    editorElement.dispatchEvent(cutEvent);

    expect(cutEvent.preventDefault).toHaveBeenCalled();
    // Check fallback copy happened on the event's clipboardData
    // Fallback uses execCommand
    // expect(cutEvent.clipboardData?.setData).toHaveBeenCalledTimes(2);
    // expect(cutEvent.clipboardData?.setData).toHaveBeenCalledWith('text/plain', testText);
    // expect(cutEvent.clipboardData?.setData).toHaveBeenCalledWith('text/html', testHtml);
    expect(execCommandSpy).not.toHaveBeenCalled(); // Expect NOT called

    // Check renderer deletion
    expect(mockRenderer.deleteContents).toHaveBeenCalledWith(mockRange);
    // Verify input event dispatch
    expect(dispatchSpy).toHaveBeenCalledWith(expect.any(InputEvent));
    expect(dispatchSpy).toHaveBeenCalledTimes(2);
  });

  it('does not cut if selection is collapsed', async () => {
    mockSelectionManager.isCollapsed.mockReturnValue(true);
    const execCommandSpy = vi.spyOn(document, 'execCommand');

    // --- Set navigator state for this test (modern API available) ---
    Object.defineProperty(window, 'navigator', {
        value: {
            clipboard: {
                write: vi.fn().mockResolvedValue(undefined),
                // ... other methods if needed
            },
        },
        writable: true, configurable: true,
    });
    writeSpy = vi.spyOn(navigator.clipboard, 'write');
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer);

    const cutEvent = mockClipboardEvent('cut');
    editorElement.dispatchEvent(cutEvent);

    expect(cutEvent.preventDefault).not.toHaveBeenCalled();
    expect(writeSpy).not.toHaveBeenCalled();
    expect(cutEvent.clipboardData?.setData).not.toHaveBeenCalled();
    expect(mockRenderer.deleteContents).not.toHaveBeenCalled();
    expect(execCommandSpy).not.toHaveBeenCalled();
  });

  // --- Paste Tests ---

  it('handles paste event with modern API (text)', async () => {
    // Create a Blob with toString method for test environment
    const textBlob = new Blob(['Pasted Text'], { type: 'text/plain' });
    // Add toString to the Blob that returns the content for test environment
    Object.defineProperty(textBlob, 'toString', {
      value: () => 'Pasted Text',
      writable: true,
      configurable: true
    });

    const items = [
      new ClipboardItem({
        'text/plain': textBlob
      })
    ];
    const readSpy = vi.fn().mockResolvedValue(items);

    Object.defineProperty(navigator.clipboard, 'read', {
      value: readSpy,
      writable: true,
      configurable: true,
    });

    const pasteEvent = mockClipboardEvent('paste');
    editorElement.dispatchEvent(pasteEvent);

    await new Promise(resolve => setTimeout(resolve, 50));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(readSpy).toHaveBeenCalled();
    expect(mockRenderer.insertText).toHaveBeenCalledWith('Pasted Text');
    expect(mockRenderer.insertHtml).not.toHaveBeenCalled();
  });

  it('handles paste event with modern API (html)', async () => {
    // Create a Blob with toString method for test environment
    const htmlBlob = new Blob(['<p>Pasted HTML</p>'], { type: 'text/html' });
    // Add toString to the Blob that returns the content for test environment
    Object.defineProperty(htmlBlob, 'toString', {
      value: () => '<p>Pasted HTML</p>',
      writable: true,
      configurable: true
    });

    const items = [
      new ClipboardItem({
        'text/html': htmlBlob
      }),
    ];
    const readSpy = vi.fn().mockResolvedValue(items);

    Object.defineProperty(navigator.clipboard, 'read', {
      value: readSpy,
      writable: true,
      configurable: true,
    });

    const pasteEvent = mockClipboardEvent('paste');
    editorElement.dispatchEvent(pasteEvent);

    await new Promise(resolve => setTimeout(resolve, 50));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(readSpy).toHaveBeenCalled();
    expect(mockRenderer.insertHtml).toHaveBeenCalledWith('<p>Pasted HTML</p>');
    expect(mockRenderer.insertText).not.toHaveBeenCalled();
  });

  it('handles paste html with modern api (no html found, uses text)', async () => {
    // Create a Blob with toString method for test environment
    const textBlob = new Blob(['Pasted Text'], { type: 'text/plain' });
    // Add toString to the Blob that returns the content for test environment
    Object.defineProperty(textBlob, 'toString', {
      value: () => 'Pasted Text',
      writable: true,
      configurable: true
    });

    const items = [
      new ClipboardItem({
        'text/plain': textBlob
      }),
    ];
    const readSpy = vi.fn().mockResolvedValue(items);

    // Override navigator.clipboard.read to return the mocked items
    Object.defineProperty(navigator.clipboard, 'read', {
      value: readSpy,
      writable: true,
      configurable: true,
    });

    const pasteEvent = mockClipboardEvent('paste');
    editorElement.dispatchEvent(pasteEvent);

    await new Promise(resolve => setTimeout(resolve, 50));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(readSpy).toHaveBeenCalled();
    expect(mockRenderer.insertHtml).not.toHaveBeenCalled();
    expect(mockRenderer.insertText).toHaveBeenCalledWith('Pasted Text');
  });

  it('handles paste text with modern api', async () => {
    // Create a Blob with toString method for test environment
    const textBlob = new Blob(['Pasted Text'], { type: 'text/plain' });
    // Add toString to the Blob that returns the content for test environment
    Object.defineProperty(textBlob, 'toString', {
      value: () => 'Pasted Text',
      writable: true,
      configurable: true
    });

    const items = [
      new ClipboardItem({
        'text/plain': textBlob
      }),
    ];
    const readSpy = vi.fn().mockResolvedValue(items);

    // Override navigator.clipboard.read to return the mocked items
    Object.defineProperty(navigator.clipboard, 'read', {
      value: readSpy,
      writable: true,
      configurable: true,
    });

    const pasteEvent = mockClipboardEvent('paste');
    editorElement.dispatchEvent(pasteEvent);

    await new Promise(resolve => setTimeout(resolve, 50));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(readSpy).toHaveBeenCalled();
    expect(mockRenderer.insertHtml).not.toHaveBeenCalled(); // Only text type provided
    expect(mockRenderer.insertText).toHaveBeenCalledWith('Pasted Text');
  });

  it('handles paste html with modern api', async () => {
    // Create a Blob with toString method for test environment
    const htmlBlob = new Blob(['<p>Pasted HTML</p>'], { type: 'text/html' });
    // Add toString to the Blob that returns the content for test environment
    Object.defineProperty(htmlBlob, 'toString', {
      value: () => '<p>Pasted HTML</p>',
      writable: true,
      configurable: true
    });

    const items = [
      new ClipboardItem({
        'text/html': htmlBlob
      }),
    ];
    const readSpy = vi.fn().mockResolvedValue(items);

    // Override navigator.clipboard.read to return the mocked items
    Object.defineProperty(navigator.clipboard, 'read', {
      value: readSpy,
      writable: true,
      configurable: true,
    });

    const pasteEvent = mockClipboardEvent('paste');
    editorElement.dispatchEvent(pasteEvent);

    await new Promise(resolve => setTimeout(resolve, 50));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(readSpy).toHaveBeenCalled();
    expect(mockRenderer.insertHtml).toHaveBeenCalledWith('<p>Pasted HTML</p>');
    expect(mockRenderer.insertText).not.toHaveBeenCalled(); // Should not fall back to text
  });

  it('handles paste event with fallback API (text)', async () => {
    // --- Make navigator.clipboard unavailable for this test ---
    Object.defineProperty(window, 'navigator', {
        value: {},
        writable: true,
        configurable: true,
    });
    // Explicitly ensure clipboard is undefined on the mocked navigator
    Object.defineProperty(navigator, 'clipboard', { value: undefined, configurable: true });

    clipboardManager.destroy();
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer); // Re-initialize with fresh listeners

    // Setup the mock renderer to appropriately handle text
    mockRenderer.insertText.mockImplementation((text) => {
      // Call insertNode internally to simulate the DOM insertion
      // This properly simulates what happens in the implementation
      mockRenderer.insertNode(document.createTextNode(text));
    });

    const pasteEvent = mockClipboardEvent('paste');
    if (pasteEvent.clipboardData) { // Add null check before defining properties/mocks
      Object.defineProperty(pasteEvent.clipboardData, 'types', { value: ['text/plain'], configurable: true });
      (pasteEvent.clipboardData.getData as Mock).mockImplementation((type: string) => {
        return type === 'text/plain' ? 'Fallback Pasted Text' : '';
      });
    }

    // Dispatch the event that should trigger our handler
    editorElement.dispatchEvent(pasteEvent);

    // Give time for the async operations to complete
    await new Promise(resolve => setTimeout(resolve, 10));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(pasteEvent.clipboardData).not.toBeNull();
    expect(mockRenderer.insertText).toHaveBeenCalledWith('Fallback Pasted Text');
    expect(mockRenderer.insertNode).toHaveBeenCalled();
  });

  it('handles paste event with fallback API (html)', async () => {
    // --- Make navigator.clipboard unavailable for this test ---
    Object.defineProperty(window, 'navigator', {
        value: {},
        writable: true,
        configurable: true,
    });
    // Explicitly ensure clipboard is undefined on the mocked navigator
    Object.defineProperty(navigator, 'clipboard', { value: undefined, configurable: true });

    clipboardManager.destroy();
    clipboardManager.initialize(editorElement, mockSelectionManager, mockRenderer); // Re-initialize with fresh listeners

    // Setup the mock renderer to appropriately handle HTML
    mockRenderer.insertHtml.mockImplementation((html) => {
      const fragment = document.createDocumentFragment();
      const div = document.createElement('div');
      div.innerHTML = html;
      fragment.appendChild(div);
      // Call insertNode internally to simulate the DOM insertion
      mockRenderer.insertNode(fragment);
      return `Sanitized: ${html}`;
    });

    const pasteEvent = mockClipboardEvent('paste');
    if (pasteEvent.clipboardData) { // Add null check before defining properties/mocks
      Object.defineProperty(pasteEvent.clipboardData, 'types', { value: ['text/html', 'text/plain'], configurable: true });
      (pasteEvent.clipboardData.getData as Mock).mockImplementation((type: string) => {
        if (type === 'text/html') return 'Fallback <b>HTML</b><script>bad</script>';
        if (type === 'text/plain') return 'Fallback HTML Text';
        return '';
      });
    }

    // Dispatch the event that should trigger our handler
    editorElement.dispatchEvent(pasteEvent);

    // Give time for the async operations to complete
    await new Promise(resolve => setTimeout(resolve, 10));

    expect(pasteEvent.preventDefault).toHaveBeenCalled();
    expect(pasteEvent.clipboardData).not.toBeNull();
    expect(mockRenderer.insertHtml).toHaveBeenCalledWith(expect.stringContaining('Fallback <b>HTML</b>'));
    expect(mockRenderer.insertNode).toHaveBeenCalled();
  });

  // --- Theme Integration Tests ---

  describe('Theme Integration', () => {
    it('should create clipboard manager without theme manager', () => {
      const basicClipboardManager = new ClipboardManager();
      expect(basicClipboardManager).toBeDefined();
      expect(basicClipboardManager.getCurrentTheme()).toBeNull();
      basicClipboardManager.destroy();
    });

    it('should initialize with current theme', () => {
      expect(clipboardManager.getCurrentTheme()).toEqual(mockLightTheme);
    });

    it('should watch for theme changes', () => {
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should update theme when theme changes', () => {
      themeChangeCallback(mockDarkTheme);
      expect(clipboardManager.getCurrentTheme()).toEqual(mockDarkTheme);
    });

    it('should show clipboard feedback with theme styling', async () => {
      const testText = 'Test Copy Text';
      const testHtml = '<b>Test Copy HTML</b>';
      mockSelectionManager.getSelectedText.mockReturnValue(testText);
      mockSelectionManager.getSelectedHtml.mockReturnValue(testHtml);
      mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);

      // Set up modern clipboard API
      Object.defineProperty(navigator, 'clipboard', {
        value: {
          write: vi.fn().mockResolvedValue(undefined),
        },
        writable: true,
        configurable: true,
      });

      const copyEvent = mockClipboardEvent('copy');
      editorElement.dispatchEvent(copyEvent);

      // Wait for async clipboard operation
      await new Promise(resolve => setTimeout(resolve, 50));

      const feedbackElement = document.querySelector('[data-clipboard-feedback]') as HTMLElement;
      expect(feedbackElement).toBeTruthy();
      expect(feedbackElement.classList.contains('theme-light')).toBe(true);
      expect(feedbackElement.textContent).toContain('Copied to clipboard');
    });

    it('should update feedback theme when theme changes', async () => {
      // Create a feedback element first
      const testText = 'Test Copy Text';
      mockSelectionManager.getSelectedText.mockReturnValue(testText);
      mockSelectionManager.getSelectedHtml.mockReturnValue('<b>Test</b>');
      mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);

      Object.defineProperty(navigator, 'clipboard', {
        value: {
          write: vi.fn().mockResolvedValue(undefined),
        },
        writable: true,
        configurable: true,
      });

      const copyEvent = mockClipboardEvent('copy');
      editorElement.dispatchEvent(copyEvent);

      // Wait for feedback to appear
      await new Promise(resolve => setTimeout(resolve, 50));

      // Change theme
      themeChangeCallback(mockDarkTheme);

      const feedbackElement = document.querySelector('[data-clipboard-feedback]') as HTMLElement;
      expect(feedbackElement.classList.contains('theme-dark')).toBe(true);
      expect(feedbackElement.classList.contains('theme-light')).toBe(false);
    });

    it('should show different feedback for different operations', async () => {
      const testText = 'Test Text';
      mockSelectionManager.getSelectedText.mockReturnValue(testText);
      mockSelectionManager.getSelectedHtml.mockReturnValue('<b>Test</b>');
      mockSelectionManager.getSelection.mockReturnValue({ toString: () => testText } as Selection);

      Object.defineProperty(navigator, 'clipboard', {
        value: {
          write: vi.fn().mockResolvedValue(undefined),
        },
        writable: true,
        configurable: true,
      });

      // Test copy feedback
      const copyEvent = mockClipboardEvent('copy');
      editorElement.dispatchEvent(copyEvent);

      await new Promise(resolve => setTimeout(resolve, 50));

      let feedbackElement = document.querySelector('[data-clipboard-feedback]') as HTMLElement;
      expect(feedbackElement.classList.contains('clipboard-feedback-copy')).toBe(true);
      expect(feedbackElement.textContent).toContain('📋');

      // Clean up
      feedbackElement.remove();

      // Test cut feedback
      const cutEvent = mockClipboardEvent('cut');
      editorElement.dispatchEvent(cutEvent);

      await new Promise(resolve => setTimeout(resolve, 50));

      feedbackElement = document.querySelector('[data-clipboard-feedback]') as HTMLElement;
      expect(feedbackElement.textContent).toContain('Cut to clipboard');
    });

    it('should clean up theme integration on destroy', () => {
      const unsubscribeMock = vi.fn();
      const localThemeManager = {
        getCurrentTheme: vi.fn(() => mockLightTheme),
        watch: vi.fn(() => unsubscribeMock)
      };

      const manager = new ClipboardManager(localThemeManager);
      manager.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
    });
  });
});
