import { <PERSON><PERSON><PERSON> } from 'jsdom';
import { vi } from 'vitest';

const dom = new JSDOM('<!doctype html><html><body></body></html>', {
  url: 'http://localhost',
});

// Assign global variables from JSDOM window
global.window = dom.window as unknown as Window & typeof globalThis;
global.document = dom.window.document;
global.navigator = dom.window.navigator;
global.HTMLElement = dom.window.HTMLElement;
global.HTMLDivElement = dom.window.HTMLDivElement;
global.HTMLButtonElement = dom.window.HTMLButtonElement;
global.HTMLInputElement = dom.window.HTMLInputElement;
global.HTMLDialogElement = dom.window.HTMLDialogElement || class MockHTMLDialogElement extends dom.window.HTMLElement {
  open = false;
  returnValue = '';
  close() { this.open = false; }
  show() { this.open = true; }
  showModal() { this.open = true; }
};
global.Node = dom.window.Node;
global.Text = dom.window.Text;
global.InputEvent = dom.window.InputEvent;
global.CustomEvent = dom.window.CustomEvent;
global.KeyboardEvent = dom.window.KeyboardEvent;
global.MouseEvent = dom.window.MouseEvent;
global.Element = dom.window.Element;

// --- ClipboardItem Mocking ---
if (typeof window.ClipboardItem === 'undefined') {
  console.log('[setupTests] Native ClipboardItem not found, creating mock.');

  // Define the expected structure for the constructor data
  interface MockClipboardItemData {
    [mimeType: string]: string | Blob | PromiseLike<string | Blob>;
  }

  // Basic mock, assuming string data for simplicity
  class MockClipboardItem {
    private data: Record<string, Blob> = {};
    public types: string[] = [];

    constructor(items: MockClipboardItemData) {
      for (const type in items) {
        // Explicitly type 'item' based on the interface
        const item: string | Blob | PromiseLike<string | Blob> = items[type];
        // Basic blob conversion for strings
        if (typeof item === 'string') {
          this.data[type] = new Blob([item], { type });
        } else if (item instanceof Blob) {
          this.data[type] = item;
        } else {
          // Handle Promises if necessary, basic mock ignores for now
          console.warn(`[setupTests] MockClipboardItem skipping Promise item for type: ${type}`);
        }
      }
      this.types = Object.keys(this.data);
    }

    async getType(type: string): Promise<Blob> {
      const blob = this.data[type];
      if (!blob) {
        throw new Error(`[setupTests] MockClipboardItem: No data found for type ${type}`);
      }
      return blob;
    }
  }
  // Assign mock to global/window, suppressing potential type errors
  // as the mock shape might differ from native definition (if it exists)
  // @ts-expect-error - Assigning mock class to global ClipboardItem
  global.ClipboardItem = MockClipboardItem;
  // @ts-expect-error - Assigning mock class to window.ClipboardItem
  window.ClipboardItem = MockClipboardItem;

} else {
  console.log('[setupTests] Using native ClipboardItem.');
  global.ClipboardItem = window.ClipboardItem;
}

// --- Range Mock Enhancements ---
if (!('cloneContents' in dom.window.Range.prototype)) {
  console.log('[setupTests] Patching Range.prototype.cloneContents');
  // Cast prototype to 'any' to allow adding the method without TS/ESLint error
  (dom.window.Range.prototype as any /* eslint-disable-line @typescript-eslint/no-explicit-any */).cloneContents = function() {
    // Simple mock: create a document fragment and append cloned nodes
    const fragment = document.createDocumentFragment();
    // This is a simplified mock. A real implementation is complex.
    // For many tests, just returning an empty fragment might suffice,
    // or cloning the commonAncestorContainer if simple enough.
    if (this.commonAncestorContainer) {
        try {
             // Attempt a simple clone - might fail for complex ranges
            fragment.appendChild(this.commonAncestorContainer.cloneNode(true));
        } catch (e) {
            console.warn('[setupTests] Mock cloneContents failed:', e);
        }
    }
    return fragment;
  };
}
global.Range = dom.window.Range; // Ensure Range is globally available

// --- ClipboardEvent Mocking ---
if (typeof window.ClipboardEvent === 'undefined') {
  console.log('[setupTests] Native ClipboardEvent not found, creating mock.');
  class MockClipboardEvent extends Event {
    clipboardData: DataTransfer | null;

    constructor(type: string, eventInitDict?: ClipboardEventInit) {
      super(type, eventInitDict);
      // Use the mock DataTransfer if clipboardData is needed
      this.clipboardData = eventInitDict?.clipboardData ?? new (global.DataTransfer as any)();
    }
  }
  global.ClipboardEvent = MockClipboardEvent as typeof ClipboardEvent;
  window.ClipboardEvent = MockClipboardEvent as typeof ClipboardEvent;
} else {
  console.log('[setupTests] Using native ClipboardEvent.');
  global.ClipboardEvent = window.ClipboardEvent;
}

// --- DataTransfer Mocking ---
if (typeof window.DataTransfer === 'undefined') {
  console.log('[setupTests] Native DataTransfer not found, creating mock.');
  class MockDataTransfer {
    private data: { [key: string]: string } = {};
    dropEffect: 'none' | 'copy' | 'link' | 'move' = 'none';
    effectAllowed: 'none' | 'copy' | 'copyLink' | 'copyMove' | 'link' | 'linkMove' | 'move' | 'all' | 'uninitialized' = 'uninitialized';
    files: FileList | null = null; // Simplified FileList mock would be needed for file tests
    items: DataTransferItemList | null = null; // Simplified DataTransferItemList mock needed for item tests
    types: string[] = [];

    clearData(format?: string): void {
      if (format) {
        delete this.data[format];
      } else {
        this.data = {};
      }
      this._updateTypes();
    }

    getData(format: string): string {
      return this.data[format.toLowerCase()] ?? '';
    }

    setData(format: string, data: string): void {
      this.data[format.toLowerCase()] = data;
      this._updateTypes();
    }

    setDragImage(_image: Element, _x: number, _y: number): void {
      // No-op in mock
    }

    private _updateTypes(): void {
      this.types = Object.keys(this.data);
    }
  }
  global.DataTransfer = MockDataTransfer as typeof DataTransfer;
  window.DataTransfer = MockDataTransfer as typeof DataTransfer;
} else {
    console.log('[setupTests] Using native DataTransfer.');
    global.DataTransfer = window.DataTransfer;
}

// --- Navigator.clipboard Mocking ---
if (!navigator.clipboard) {
  console.log('[setupTests] Mocking navigator.clipboard.');
  Object.defineProperty(navigator, 'clipboard', {
    value: {
      // Provide default mock implementations, tests can override
      write: vi.fn().mockResolvedValue(undefined),
      writeText: vi.fn().mockResolvedValue(undefined),
      read: vi.fn().mockResolvedValue([]), // Default to empty array for read()
      readText: vi.fn().mockResolvedValue(''), // Default to empty string for readText()
    },
    writable: true, // Allow tests to replace the value object
    configurable: true // IMPORTANT: Allow tests to redefine the property
  });
} else {
    console.log('[setupTests] Using existing navigator.clipboard.');
}

// --- Mock document.execCommand ---
if (typeof document.execCommand === 'undefined') {
  console.log('[setupTests] Mocking document.execCommand.');
  document.execCommand = vi.fn().mockReturnValue(true); // Return true
}

// --- Mock getComputedStyle ---
if (!window.getComputedStyle) {
  console.log('[setupTests] Mocking window.getComputedStyle.');
  window.getComputedStyle = vi.fn().mockImplementation((_element: Element) => {
    return {
      getPropertyValue: vi.fn().mockReturnValue(''),
      setProperty: vi.fn(),
      removeProperty: vi.fn(),
      // Add common CSS properties that tests might access
      display: 'block',
      visibility: 'visible',
      opacity: '1',
      color: 'rgb(0, 0, 0)',
      backgroundColor: 'rgba(0, 0, 0, 0)',
      fontSize: '16px',
      fontFamily: 'Arial, sans-serif',
      width: '100px',
      height: '100px',
      margin: '0px',
      padding: '0px',
      border: '0px none rgb(0, 0, 0)',
      position: 'static',
      top: 'auto',
      left: 'auto',
      right: 'auto',
      bottom: 'auto',
      zIndex: 'auto',
      // Add any other properties your tests might need
    } as unknown as CSSStyleDeclaration;
  });
  global.getComputedStyle = window.getComputedStyle;
}

// --- Mock IntersectionObserver ---
if (!window.IntersectionObserver) {
  console.log('[setupTests] Mocking IntersectionObserver.');
  const mockIntersectionObserver = vi.fn().mockImplementation((callback: IntersectionObserverCallback) => {
    const instance = {
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
      root: null,
      rootMargin: '',
      thresholds: [],
      takeRecords: vi.fn().mockReturnValue([]),
      // Helper method for tests to simulate intersection changes
      simulateIntersection: (entries: Partial<IntersectionObserverEntry>[]) => {
        callback(entries as IntersectionObserverEntry[], instance as IntersectionObserver);
      }
    };
    return instance;
  });
  window.IntersectionObserver = mockIntersectionObserver;
  global.IntersectionObserver = mockIntersectionObserver;
}

// --- Mock requestAnimationFrame and cancelAnimationFrame ---
if (!window.requestAnimationFrame) {
  console.log('[setupTests] Mocking requestAnimationFrame.');
  window.requestAnimationFrame = vi.fn().mockImplementation((callback: FrameRequestCallback) => {
    return setTimeout(callback, 16);
  });
  global.requestAnimationFrame = window.requestAnimationFrame;
}

if (!window.cancelAnimationFrame) {
  console.log('[setupTests] Mocking cancelAnimationFrame.');
  window.cancelAnimationFrame = vi.fn().mockImplementation((id: number) => {
    clearTimeout(id);
  });
  global.cancelAnimationFrame = window.cancelAnimationFrame;
}

// --- Mock performance.now ---
if (!window.performance || !window.performance.now) {
  console.log('[setupTests] Mocking performance.now.');
  if (!window.performance) {
    window.performance = {} as Performance;
  }
  window.performance.now = vi.fn().mockImplementation(() => Date.now());
  global.performance = window.performance;
}

// --- Mock navigator.hardwareConcurrency ---
console.log('[setupTests] Mocking navigator.hardwareConcurrency.');
Object.defineProperty(navigator, 'hardwareConcurrency', {
  value: 4,
  writable: false,
  configurable: false
});

// --- Mock ResizeObserver ---
if (!window.ResizeObserver) {
  console.log('[setupTests] Mocking ResizeObserver.');
  const mockResizeObserver = vi.fn().mockImplementation((callback: ResizeObserverCallback) => {
    const instance = {
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
      // Helper method for tests to simulate resize changes
      simulateResize: (entries: Partial<ResizeObserverEntry>[]) => {
        callback(entries as ResizeObserverEntry[], instance as ResizeObserver);
      }
    };
    return instance;
  });
  window.ResizeObserver = mockResizeObserver;
  global.ResizeObserver = mockResizeObserver;
}

// --- Mock MutationObserver ---
if (!window.MutationObserver) {
  console.log('[setupTests] Mocking MutationObserver.');
  const mockMutationObserver = vi.fn().mockImplementation((callback: MutationCallback) => {
    const instance = {
      observe: vi.fn(),
      disconnect: vi.fn(),
      takeRecords: vi.fn().mockReturnValue([]),
      // Helper method for tests to simulate mutations
      simulateMutation: (mutations: Partial<MutationRecord>[]) => {
        callback(mutations as MutationRecord[], instance as MutationObserver);
      }
    };
    return instance;
  });
  window.MutationObserver = mockMutationObserver;
  global.MutationObserver = mockMutationObserver;
}

// --- Mock CSS.supports ---
if (!window.CSS || !window.CSS.supports) {
  console.log('[setupTests] Mocking CSS.supports.');
  if (!window.CSS) {
    window.CSS = {
      supports: vi.fn().mockReturnValue(true)
    } as unknown as typeof CSS;
  } else {
    window.CSS.supports = vi.fn().mockReturnValue(true);
  }
  global.CSS = window.CSS;
}

// --- Mock matchMedia ---
if (!window.matchMedia) {
  console.log('[setupTests] Mocking window.matchMedia.');
  window.matchMedia = vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
  global.matchMedia = window.matchMedia;
}

console.log('JSDOM environment setup complete.');
