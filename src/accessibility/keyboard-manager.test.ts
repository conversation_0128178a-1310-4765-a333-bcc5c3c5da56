/**
 * Tests for the enhanced KeyboardManager with theme system integration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { KeyboardManager } from './keyboard-manager';
import type { ThemeDefinition } from '../themes/theme-types';
import type { Editor } from '../types';

// Mock Editor interface
const mockEditor: Editor = {
  getElement: vi.fn(() => document.createElement('div')),
  format: vi.fn(),
  undo: vi.fn(),
  redo: vi.fn()
} as any;

// Mock theme manager interface
interface MockThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

// Mock theme definitions
const mockLightTheme: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  version: '1.0.0',
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#757575',
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    info: '#2196f3'
  },
  stateColors: {
    hover: { background: '#f5f5f5', border: '#d0d0d0', text: '#333333' },
    focus: { background: '#ffffff', border: '#1565c0', ring: '#3b82f6' },
    active: { background: '#e3f2fd', border: '#1565c0', text: '#333333' },
    disabled: { background: '#f5f5f5', border: '#e0e0e0', text: '#9e9e9e' }
  },
  pluginColors: {
    palette: { background: '#ffffff', border: '#e0e0e0', shadow: '0 2px 8px rgba(0,0,0,0.1)', tabActiveBorder: '#1565c0', swatchBorder: 'rgba(0,0,0,0.1)' },
    chart: { background: '#ffffff', gridLines: '#e0e0e0', dataColors: ['#1565c0', '#4caf50', '#ff9800'] },
    code: { background: '#f8f9fa', border: '#e0e0e0', text: '#333333', keyword: '#1565c0', string: '#4caf50', comment: '#757575' },
    table: { background: '#ffffff', border: '#e0e0e0', headerBackground: '#f8f9fa', alternateRowBackground: '#f8f9fa' },
    comments: { background: '#ffffff', border: '#e0e0e0', highlightBackground: '#fff3cd', avatarBackground: '#f8f9fa' }
  },
  animation: { duration: 300, easing: 'ease-in-out', respectReducedMotion: true }
};

const mockDarkTheme: ThemeDefinition = {
  ...mockLightTheme,
  id: 'dark',
  name: 'Dark Theme',
  colors: {
    ...mockLightTheme.colors,
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d'
  },
  stateColors: {
    ...mockLightTheme.stateColors,
    focus: { background: '#2d2d2d', border: '#60a5fa', ring: '#60a5fa' }
  }
};

describe('KeyboardManager', () => {
  let keyboardManager: KeyboardManager;
  let mockThemeManager: MockThemeManager;
  let themeChangeCallback: (theme: ThemeDefinition) => void;

  beforeEach(() => {
    // Set up DOM
    document.body.innerHTML = '';

    // Mock theme manager
    mockThemeManager = {
      getCurrentTheme: vi.fn(() => mockLightTheme),
      watch: vi.fn((callback) => {
        themeChangeCallback = callback;
        return vi.fn(); // Return unsubscribe function
      })
    };

    // Create KeyboardManager with theme manager
    keyboardManager = new KeyboardManager(mockEditor, mockThemeManager);
  });

  afterEach(() => {
    keyboardManager.destroy();
    vi.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    it('should create keyboard manager without theme manager', () => {
      const basicKeyboardManager = new KeyboardManager(mockEditor);
      expect(basicKeyboardManager).toBeDefined();
      expect(basicKeyboardManager.getCurrentTheme()).toBeNull();
      basicKeyboardManager.destroy();
    });

    it('should register and execute shortcuts', () => {
      const handler = vi.fn();
      const success = keyboardManager.registerShortcut('ctrl+t', handler, 'Test shortcut');

      expect(success).toBe(true);

      // Create a target element for the event
      const targetElement = document.createElement('div');
      document.body.appendChild(targetElement);

      // Mock the editor's getElement to return an element that contains our target
      const editorElement = document.createElement('div');
      editorElement.appendChild(targetElement);
      mockEditor.getElement = vi.fn(() => editorElement);

      // Simulate keydown event with proper target
      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true,
        bubbles: true
      });

      // Manually set the target since JSDOM doesn't do this automatically
      Object.defineProperty(event, 'target', {
        value: targetElement,
        enumerable: true
      });

      document.dispatchEvent(event);
      expect(handler).toHaveBeenCalled();
    });

    it('should unregister shortcuts', () => {
      const handler = vi.fn();
      keyboardManager.registerShortcut('ctrl+t', handler, 'Test shortcut');

      const removed = keyboardManager.unregisterShortcut('ctrl+t');
      expect(removed).toBe(true);

      // Create a target element for the event
      const targetElement = document.createElement('div');
      document.body.appendChild(targetElement);

      // Mock the editor's getElement to return an element that contains our target
      const editorElement = document.createElement('div');
      editorElement.appendChild(targetElement);
      mockEditor.getElement = vi.fn(() => editorElement);

      // Simulate keydown event - should not trigger handler
      const event = new KeyboardEvent('keydown', {
        key: 't',
        ctrlKey: true,
        bubbles: true
      });

      // Manually set the target since JSDOM doesn't do this automatically
      Object.defineProperty(event, 'target', {
        value: targetElement,
        enumerable: true
      });

      document.dispatchEvent(event);
      expect(handler).not.toHaveBeenCalled();
    });
  });

  describe('Theme Integration', () => {
    it('should initialize with current theme', () => {
      expect(keyboardManager.getCurrentTheme()).toEqual(mockLightTheme);
    });

    it('should watch for theme changes', () => {
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should update theme when theme changes', () => {
      themeChangeCallback(mockDarkTheme);
      expect(keyboardManager.getCurrentTheme()).toEqual(mockDarkTheme);
    });

    it('should create theme-aware shortcut feedback', () => {
      const feedback = keyboardManager.createShortcutFeedback('Ctrl+B', 'Bold text');

      expect(feedback).toBeInstanceOf(HTMLElement);
      expect(feedback.classList.contains('theme-light')).toBe(true);
      expect(feedback.getAttribute('data-keyboard-feedback')).toBe('true');
      expect(feedback.querySelector('.keyboard-shortcut-key')?.textContent).toBe('Ctrl+B');
      expect(feedback.querySelector('.keyboard-shortcut-description')?.textContent).toBe('Bold text');
    });

    it('should update feedback theme when theme changes', () => {
      const feedback = keyboardManager.createShortcutFeedback('Ctrl+B', 'Bold text');
      document.body.appendChild(feedback);

      // Change theme
      themeChangeCallback(mockDarkTheme);

      expect(feedback.classList.contains('theme-dark')).toBe(true);
      expect(feedback.classList.contains('theme-light')).toBe(false);
    });

    it('should apply theme CSS variables to feedback elements', () => {
      const feedback = keyboardManager.createShortcutFeedback('Ctrl+B', 'Bold text');

      expect(feedback.style.getPropertyValue('--keyboard-feedback-bg')).toBe('#f8f9fa');
      expect(feedback.style.getPropertyValue('--keyboard-feedback-text')).toBe('#333333');
      expect(feedback.style.getPropertyValue('--keyboard-feedback-border')).toBe('#1565c0');
    });
  });

  describe('Cleanup', () => {
    it('should unsubscribe from theme changes on destroy', () => {
      const unsubscribeMock = vi.fn();
      mockThemeManager.watch = vi.fn(() => unsubscribeMock);

      const manager = new KeyboardManager(mockEditor, mockThemeManager);
      manager.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
    });

    it('should clear shortcuts on destroy', () => {
      keyboardManager.registerShortcut('ctrl+t', vi.fn(), 'Test');
      const shortcuts = keyboardManager.getShortcuts();
      expect(Object.keys(shortcuts)).toContain('ctrl+t');

      keyboardManager.destroy();

      const shortcutsAfterDestroy = keyboardManager.getShortcuts();
      expect(Object.keys(shortcutsAfterDestroy)).toHaveLength(0);
    });
  });

  describe('Shortcut Management', () => {
    it('should get shortcuts by category', () => {
      keyboardManager.registerShortcut('ctrl+b', vi.fn(), 'Bold', { category: 'formatting' });
      keyboardManager.registerShortcut('ctrl+z', vi.fn(), 'Undo', { category: 'history' });

      const shortcuts = keyboardManager.getShortcutsByCategory();
      expect(shortcuts.formatting).toBeDefined();
      expect(shortcuts.history).toBeDefined();
      expect(shortcuts.formatting['ctrl+b']).toBe('Bold');
      expect(shortcuts.history['ctrl+z']).toBe('Undo');
    });

    it('should enable/disable shortcuts', () => {
      const handler = vi.fn();
      keyboardManager.registerShortcut('ctrl+t', handler, 'Test');

      const disabledCount = keyboardManager.setShortcutsEnabled(['ctrl+t'], false);
      expect(disabledCount).toBe(1);

      // Should not appear in active shortcuts
      const shortcuts = keyboardManager.getShortcuts();
      expect(shortcuts['ctrl+t']).toBeUndefined();
    });
  });
});
