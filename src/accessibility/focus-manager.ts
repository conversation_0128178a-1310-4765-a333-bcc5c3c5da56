/**
 * Manages focus trapping and restoration for accessibility.
 *
 * Implements focus trapping within modal dialogs, popovers, and other UI elements
 * that need to capture and restore focus for keyboard navigation.
 * Enhanced with theme system integration for consistent focus indicators.
 */

import type { ThemeDefinition } from '../themes/theme-types';

// Define the structure for focus trap state with stronger typing
interface FocusTrap {
  /** The container element in which focus is trapped */
  element: HTMLElement;
  /** All focusable elements within the trap */
  focusableElements: HTMLElement[];
  /** Element that had focus before trapping began (to restore later) */
  lastFocused: Element | null;
  /** Optional ID for the trap (useful for debugging and management) */
  id?: string;
}

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Manages focus trapping for accessibility compliance (WCAG 2.1 SC 2.4.3 and 2.4.7)
 * Ensures keyboard navigation stays within appropriate contexts.
 * Enhanced with theme system integration for consistent focus indicators.
 */
export class FocusManager {
  /** Stack of active focus traps (newest/active at the end) */
  private readonly trapStack: FocusTrap[] = [];
  /** Cached reusable bound handler function */
  private readonly boundHandleKeyDown: (event: KeyboardEvent) => void;
  /** Theme change unsubscribe function */
  private themeUnsubscribe?: () => void;

  /** Counter for generating unique IDs when not provided */
  private nextTrapId = 1;

  /** List of all selector patterns for focusable elements */
  private static readonly FOCUSABLE_SELECTORS = [
    'a[href]:not([tabindex="-1"]):not([aria-hidden="true"])',
    'button:not([disabled]):not([tabindex="-1"]):not([aria-hidden="true"])',
    'input:not([disabled]):not([tabindex="-1"]):not([type="hidden"]):not([aria-hidden="true"])',
    'select:not([disabled]):not([tabindex="-1"]):not([aria-hidden="true"])',
    'textarea:not([disabled]):not([tabindex="-1"]):not([aria-hidden="true"])',
    '[tabindex]:not([tabindex="-1"]):not([aria-hidden="true"])',
    '[contenteditable]:not([tabindex="-1"]):not([aria-hidden="true"])',
    'audio[controls]:not([tabindex="-1"]):not([aria-hidden="true"])',
    'video[controls]:not([tabindex="-1"]):not([aria-hidden="true"])'
  ];

  constructor(private readonly themeManager?: IThemeManager) {
    this.boundHandleKeyDown = this._handleKeyDown.bind(this);

    // Set up theme change listener if theme manager is provided
    if (this.themeManager) {
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }
  }

  /**
   * Start trapping focus within an element
   * @param element - Element to trap focus within
   * @param options - Optional configuration
   * @returns The ID of the created focus trap
   * @throws Error if element is invalid or can't contain focus
   */
  trapFocus(element: HTMLElement, options: {
    initialFocus?: HTMLElement | null;
    id?: string;
    preventScroll?: boolean;
  } = {}): string {
    if (!element) {
      throw new Error('[FocusManager] Cannot trap focus: element is missing');
    }

    const { initialFocus = null, id, preventScroll = false } = options;

    try {
      // Find all focusable elements in the container
      const focusableElements = this._getFocusableElements(element);

      if (focusableElements.length === 0) {
        console.warn('[FocusManager] No focusable elements found in trap container');
      }

      // Create the trap configuration
      const trapId = id || `focus-trap-${this.nextTrapId++}`;
      const trap: FocusTrap = {
        element,
        focusableElements,
        lastFocused: document.activeElement,
        id: trapId
      };

      // Store this trap on our stack
      this.trapStack.push(trap);

      // Set up event listeners
      element.addEventListener('keydown', this.boundHandleKeyDown);

      // Set appropriate ARIA attributes
      element.setAttribute('aria-modal', 'true');

      // Apply current theme to the trap if theme manager is available
      if (this.themeManager) {
        const currentTheme = this.themeManager.getCurrentTheme();
        if (currentTheme) {
          this._applyThemeToTrap(trap, currentTheme);
        }
      }

      // Focus the appropriate element
      const elementToFocus = initialFocus ||
                           (focusableElements.length > 0 ? focusableElements[0] : element);

      if (elementToFocus && typeof elementToFocus.focus === 'function') {
        // Use requestAnimationFrame to ensure the DOM is ready
        requestAnimationFrame(() => {
          try {
            elementToFocus.focus({ preventScroll });
          } catch (focusError) {
            console.error('[FocusManager] Error focusing element:', focusError);
            // Try focusing the container as fallback
            element.focus({ preventScroll });
          }
        });
      }

      return trapId;
    } catch (error) {
      console.error('[FocusManager] Error trapping focus:', error);
      throw new Error(`[FocusManager] Failed to trap focus: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Release the most recent focus trap or a specific trap by ID
   * @param id - Optional ID of the trap to release
   * @returns True if a trap was found and released, false otherwise
   */
  releaseFocus(id?: string): boolean {
    try {
      let trapIndex: number;
      let trap: FocusTrap | undefined;

      if (id) {
        // Find specific trap by ID
        trapIndex = this.trapStack.findIndex(t => t.id === id);
        if (trapIndex === -1) {
          console.warn(`[FocusManager] No focus trap found with ID: ${id}`);
          return false;
        }
        trap = this.trapStack[trapIndex];
        // Remove this specific trap
        this.trapStack.splice(trapIndex, 1);
      } else {
        // Pop the most recent trap
        trap = this.trapStack.pop();
        if (!trap) {
          return false;
        }
      }

      // Clean up event listeners
      trap.element.removeEventListener('keydown', this.boundHandleKeyDown);

      // Remove ARIA attributes
      trap.element.removeAttribute('aria-modal');

      // Restore focus to the previously focused element
      if (trap.lastFocused instanceof HTMLElement && trap.lastFocused.isConnected) {
        // Use requestAnimationFrame to ensure DOM is ready
        requestAnimationFrame(() => {
          try {
            if (trap.lastFocused instanceof HTMLElement) {
              trap.lastFocused.focus();
            }
          } catch (focusError) {
            console.error('[FocusManager] Error restoring focus:', focusError);
          }
        });
      }

      return true;
    } catch (error) {
      console.error('[FocusManager] Error releasing focus trap:', error);
      return false;
    }
  }

  /**
   * Release all active focus traps
   * @returns Number of traps that were released
   */
  releaseAllTraps(): number {
    const count = this.trapStack.length;

    try {
      // Clean up each trap individually
      while (this.trapStack.length > 0) {
        this.releaseFocus();
      }

      return count;
    } catch (error) {
      console.error('[FocusManager] Error releasing all focus traps:', error);
      return 0;
    }
  }

  /**
   * Check if there are any active focus traps
   * @returns True if there's at least one active focus trap
   */
  hasActiveTraps(): boolean {
    return this.trapStack.length > 0;
  }

  /**
   * Get the currently active focus trap if any exists
   * @returns The active focus trap or null if none exists
   */
  getActiveTrap(): FocusTrap | null {
    return this.trapStack.length > 0 ? this.trapStack[this.trapStack.length - 1] : null;
  }

  /**
   * Handle keydown events for focus trapping
   * @param event - Keyboard event
   * @private
   */
  private _handleKeyDown(event: KeyboardEvent): void {
    // Only process Tab key events
    if (event.key !== 'Tab') return;

    // Get the currently active trap (last in stack)
    const trap = this.trapStack[this.trapStack.length - 1];
    if (!trap) return;

    // Optimization: normalize the focusable elements array for fast lookups
    // This only needs to run once per trap activation
    if (trap.focusableElements.length === 0) {
      // No focusable elements, prevent all tab navigation
      event.preventDefault();
      return;
    }

    const { focusableElements } = trap;
    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    // Handle Tab or Shift+Tab to create a focus loop
    try {
      if (event.shiftKey) {
        // Shift+Tab: If at first element, wrap to last
        if (document.activeElement === firstFocusable) {
          event.preventDefault();
          lastFocusable.focus();
        }
      } else {
        // Tab: If at last element, wrap to first
        if (document.activeElement === lastFocusable) {
          event.preventDefault();
          firstFocusable.focus();
        }
      }
    } catch (keyError) {
      console.error('[FocusManager] Error handling Tab key:', keyError);
      // Prevent default as safety measure
      event.preventDefault();
    }
  }

  /**
   * Get all focusable elements within a container
   * @param container - Container element
   * @returns Array of focusable elements
   * @private
   */
  private _getFocusableElements(container: HTMLElement): HTMLElement[] {
    try {
      if (!container) {
        return [];
      }

      // Join all selector patterns into a single selector string
      const selector = FocusManager.FOCUSABLE_SELECTORS.join(',');

      // Find all elements matching our selector
      const elements = Array.from(container.querySelectorAll<HTMLElement>(selector));

      // Filter for only visible elements with size (display:none or visibility:hidden elements should be excluded)
      return elements.filter(el => {
        // Check if element is visible and has dimensions
        const isVisible = el.offsetWidth > 0 &&
                        el.offsetHeight > 0 &&
                        window.getComputedStyle(el).visibility !== 'hidden';

        return isVisible;
      });
    } catch (error) {
      console.error('[FocusManager] Error finding focusable elements:', error);
      return [];
    }
  }

  /**
   * Handle theme changes and update focus indicators
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      // Update focus indicators for all active traps
      this.trapStack.forEach(trap => {
        this._applyThemeToTrap(trap, theme);
      });
    } catch (error) {
      console.error('[FocusManager] Error handling theme change:', error);
    }
  }

  /**
   * Apply theme-aware focus indicators to a focus trap
   * @param trap - Focus trap to update
   * @param theme - Theme to apply
   * @private
   */
  private _applyThemeToTrap(trap: FocusTrap, theme: ThemeDefinition): void {
    try {
      // Apply theme class to the trap container
      trap.element.classList.add(`theme-${theme.id}`);

      // Apply theme-aware focus indicators to all focusable elements
      trap.focusableElements.forEach(element => {
        this._applyThemeToElement(element, theme);
      });
    } catch (error) {
      console.error('[FocusManager] Error applying theme to trap:', error);
    }
  }

  /**
   * Apply theme-aware styling to a focusable element
   * @param element - Element to style
   * @param theme - Theme to apply
   * @private
   */
  private _applyThemeToElement(element: HTMLElement, theme: ThemeDefinition): void {
    try {
      // Add theme class for CSS variable inheritance
      element.classList.add(`theme-${theme.id}`);

      // Set focus ring color CSS variables if theme has focus state colors
      if (theme.stateColors?.focus) {
        element.style.setProperty('--focus-ring-color', theme.stateColors.focus.ring);
        element.style.setProperty('--focus-outline-color', 'transparent');
      }
    } catch (error) {
      console.error('[FocusManager] Error applying theme to element:', error);
    }
  }

  /**
   * Clean up theme-related resources
   */
  public destroy(): void {
    try {
      // Release all focus traps
      this.releaseAllTraps();

      // Unsubscribe from theme changes
      if (this.themeUnsubscribe) {
        this.themeUnsubscribe();
        this.themeUnsubscribe = undefined;
      }
    } catch (error) {
      console.error('[FocusManager] Error during cleanup:', error);
    }
  }
}