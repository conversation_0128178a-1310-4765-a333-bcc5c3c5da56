/**
 * Tests for the enhanced FocusManager with theme system integration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { FocusManager } from './focus-manager';
import type { ThemeDefinition } from '../themes/theme-types';

// Mock theme manager interface
interface MockThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

// Mock theme definitions
const mockLightTheme: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  version: '1.0.0',
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#757575',
    success: '#4caf50',
    warning: '#ff9800',
    error: '#f44336',
    info: '#2196f3'
  },
  stateColors: {
    hover: { background: '#f5f5f5', border: '#d0d0d0', text: '#333333' },
    focus: { background: '#ffffff', border: '#1565c0', ring: '#3b82f6' },
    active: { background: '#e3f2fd', border: '#1565c0', text: '#333333' },
    disabled: { background: '#f5f5f5', border: '#e0e0e0', text: '#9e9e9e' }
  },
  pluginColors: {
    palette: { background: '#ffffff', border: '#e0e0e0', shadow: '0 2px 8px rgba(0,0,0,0.1)', tabActiveBorder: '#1565c0', swatchBorder: 'rgba(0,0,0,0.1)' },
    chart: { background: '#ffffff', gridLines: '#e0e0e0', dataColors: ['#1565c0', '#4caf50', '#ff9800'] },
    code: { background: '#f8f9fa', border: '#e0e0e0', text: '#333333', keyword: '#1565c0', string: '#4caf50', comment: '#757575' },
    table: { background: '#ffffff', border: '#e0e0e0', headerBackground: '#f8f9fa', alternateRowBackground: '#f8f9fa' },
    comments: { background: '#ffffff', border: '#e0e0e0', highlightBackground: '#fff3cd', avatarBackground: '#f8f9fa' }
  },
  animation: { duration: 300, easing: 'ease-in-out', respectReducedMotion: true }
};

const mockDarkTheme: ThemeDefinition = {
  ...mockLightTheme,
  id: 'dark',
  name: 'Dark Theme',
  colors: {
    ...mockLightTheme.colors,
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d'
  },
  stateColors: {
    ...mockLightTheme.stateColors,
    focus: { background: '#2d2d2d', border: '#60a5fa', ring: '#60a5fa' }
  }
};

describe('FocusManager', () => {
  let focusManager: FocusManager;
  let mockThemeManager: MockThemeManager;
  let container: HTMLElement;
  let focusableButton: HTMLButtonElement;
  let focusableInput: HTMLInputElement;
  let themeChangeCallback: (theme: ThemeDefinition) => void;

  beforeEach(() => {
    // Set up DOM
    document.body.innerHTML = '';
    container = document.createElement('div');
    container.id = 'test-container';
    container.style.width = '200px';
    container.style.height = '100px';
    container.style.position = 'relative';

    focusableButton = document.createElement('button');
    focusableButton.textContent = 'Test Button';
    focusableButton.style.width = '80px';
    focusableButton.style.height = '30px';
    focusableButton.style.display = 'block';

    focusableInput = document.createElement('input');
    focusableInput.type = 'text';
    focusableInput.placeholder = 'Test Input';
    focusableInput.style.width = '80px';
    focusableInput.style.height = '20px';
    focusableInput.style.display = 'block';

    container.appendChild(focusableButton);
    container.appendChild(focusableInput);
    document.body.appendChild(container);

    // Mock offsetWidth and offsetHeight for JSDOM
    Object.defineProperty(focusableButton, 'offsetWidth', { value: 80, configurable: true });
    Object.defineProperty(focusableButton, 'offsetHeight', { value: 30, configurable: true });
    Object.defineProperty(focusableInput, 'offsetWidth', { value: 80, configurable: true });
    Object.defineProperty(focusableInput, 'offsetHeight', { value: 20, configurable: true });

    // Mock theme manager
    mockThemeManager = {
      getCurrentTheme: vi.fn(() => mockLightTheme),
      watch: vi.fn((callback) => {
        themeChangeCallback = callback;
        return vi.fn(); // Return unsubscribe function
      })
    };

    // Create FocusManager with theme manager
    focusManager = new FocusManager(mockThemeManager);
  });

  afterEach(() => {
    focusManager.destroy();
    document.body.innerHTML = '';
    vi.clearAllMocks();
  });

  describe('Basic Focus Trapping', () => {
    it('should create focus trap without theme manager', () => {
      const basicFocusManager = new FocusManager();
      const trapId = basicFocusManager.trapFocus(container);

      expect(trapId).toBeDefined();
      expect(basicFocusManager.hasActiveTraps()).toBe(true);

      basicFocusManager.destroy();
    });

    it('should trap focus within container', () => {
      const trapId = focusManager.trapFocus(container);

      expect(trapId).toBeDefined();
      expect(focusManager.hasActiveTraps()).toBe(true);
      expect(container.getAttribute('aria-modal')).toBe('true');
    });

    it('should release focus trap', () => {
      const trapId = focusManager.trapFocus(container);
      const released = focusManager.releaseFocus(trapId);

      expect(released).toBe(true);
      expect(focusManager.hasActiveTraps()).toBe(false);
      expect(container.getAttribute('aria-modal')).toBeNull();
    });
  });

  describe('Theme Integration', () => {
    it('should apply theme to trap on creation', () => {
      focusManager.trapFocus(container);

      expect(container.classList.contains('theme-light')).toBe(true);
      expect(focusableButton.classList.contains('theme-light')).toBe(true);
      expect(focusableInput.classList.contains('theme-light')).toBe(true);
    });

    it('should apply focus ring colors from theme', () => {
      focusManager.trapFocus(container);

      expect(focusableButton.style.getPropertyValue('--focus-ring-color')).toBe('#3b82f6');
      expect(focusableInput.style.getPropertyValue('--focus-ring-color')).toBe('#3b82f6');
    });

    it('should update theme when theme changes', () => {
      focusManager.trapFocus(container);

      // Simulate theme change
      themeChangeCallback(mockDarkTheme);

      expect(container.classList.contains('theme-dark')).toBe(true);
      expect(focusableButton.style.getPropertyValue('--focus-ring-color')).toBe('#60a5fa');
    });

    it('should watch for theme changes', () => {
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
    });
  });

  describe('Cleanup', () => {
    it('should unsubscribe from theme changes on destroy', () => {
      const unsubscribeMock = vi.fn();
      mockThemeManager.watch = vi.fn(() => unsubscribeMock);

      const manager = new FocusManager(mockThemeManager);
      manager.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
    });

    it('should release all traps on destroy', () => {
      focusManager.trapFocus(container);
      expect(focusManager.hasActiveTraps()).toBe(true);

      focusManager.destroy();
      expect(focusManager.hasActiveTraps()).toBe(false);
    });
  });
});
