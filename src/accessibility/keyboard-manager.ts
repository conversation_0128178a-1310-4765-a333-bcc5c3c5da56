import type { Editor } from '../types';
import type { ThemeDefinition } from '../themes/theme-types';

/**
 * Manages keyboard navigation and shortcuts for editor functionality
 * Implements keyboard command patterns with proper event handling and cross-platform support
 * Enhanced with theme system integration for visual feedback and accessibility
 */

/**
 * Structure containing details about a keyboard shortcut
 * including its handler function and human-readable description
 */
interface ShortcutDetail {
  /** Function to execute when shortcut is triggered */
  handler: () => void;
  /** Human-readable description for documentation/UI */
  description: string;
  /** Optional category for grouping shortcuts (e.g., 'formatting', 'history') */
  category?: string;
  /** Optional flag to disable the shortcut in certain contexts */
  disabled?: boolean;
}

/** Platform-specific key mapping type */
type KeyMap = Record<string, string>;

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Manages keyboard shortcuts and accessibility key events for the editor
 * Enhanced with theme system integration for visual feedback and accessibility
 */
export class KeyboardManager {
  /** Reference to the editor instance this manager controls */
  private readonly editor: Editor;

  /** Map of normalized key combinations to their handlers and metadata */
  private readonly shortcuts: Map<string, ShortcutDetail> = new Map();

  /** Bound handler reference for event listener management */
  private readonly boundKeydownHandler: (event: KeyboardEvent) => void;

  /** Flag indicating whether keyboard events are currently being processed */
  private isProcessingEvent = false;

  /** Theme change unsubscribe function */
  private themeUnsubscribe?: () => void;

  /** Current theme for visual feedback */
  private currentTheme: ThemeDefinition | null = null;

  /** Maps for platform-specific key normalization */
  private static readonly KEY_MAPS: Record<string, KeyMap> = {
    mac: {
      'meta': 'cmd',
      'escape': 'esc',
      'delete': 'del',
      ' ': 'space'
    },
    other: {
      'control': 'ctrl',
      'escape': 'esc',
      'delete': 'del',
      ' ': 'space'
    }
  };

  /**
   * Create a new KeyboardManager instance
   * @param editor - The editor this manager will control
   * @param themeManager - Optional theme manager for visual feedback
   * @throws Error if an invalid editor is provided
   */
  constructor(editor: Editor, private readonly themeManager?: IThemeManager) {
    if (!editor) {
      throw new Error('[KeyboardManager] Editor instance is required');
    }

    this.editor = editor;
    this.boundKeydownHandler = this._handleKeyDown.bind(this);

    // Set up theme integration if theme manager is provided
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }

    this._setupDefaultShortcuts();
    this._bindEvents();
  }

  /**
   * Register a keyboard shortcut with the editor
   * @param key - Key combination (e.g., 'ctrl+b', 'cmd+shift+z')
   * @param handler - Function to execute when shortcut is triggered
   * @param description - Human-readable description of the shortcut
   * @param options - Additional shortcut options
   * @returns Boolean indicating if registration was successful
   */
  registerShortcut(
    key: string,
    handler: () => void,
    description: string,
    options: { category?: string; disabled?: boolean } = {}
  ): boolean {
    try {
      if (!key || typeof key !== 'string') {
        console.error('[KeyboardManager] Invalid shortcut key:', key);
        return false;
      }

      if (typeof handler !== 'function') {
        console.error('[KeyboardManager] Invalid shortcut handler for key:', key);
        return false;
      }

      const normalizedKey = this._normalizeKey(key);

      this.shortcuts.set(normalizedKey, {
        handler,
        description,
        category: options.category,
        disabled: options.disabled
      });

      return true;
    } catch (error) {
      console.error('[KeyboardManager] Error registering shortcut:', error);
      return false;
    }
  }

  /**
   * Unregister a previously registered keyboard shortcut
   * @param key - Key combination to remove
   * @returns Boolean indicating if the shortcut was found and removed
   */
  unregisterShortcut(key: string): boolean {
    try {
      const normalizedKey = this._normalizeKey(key);
      return this.shortcuts.delete(normalizedKey);
    } catch (error) {
      console.error('[KeyboardManager] Error unregistering shortcut:', error);
      return false;
    }
  }

  /**
   * Set up default editor shortcuts
   * @private
   */
  private _setupDefaultShortcuts(): void {
    try {
      // Text formatting shortcuts
      this.registerShortcut('ctrl+b', () => {
        this.editor.format('bold');
      }, 'Toggle bold text', { category: 'formatting' });

      this.registerShortcut('ctrl+i', () => {
        this.editor.format('italic');
      }, 'Toggle italic text', { category: 'formatting' });

      this.registerShortcut('ctrl+u', () => {
        this.editor.format('underline');
      }, 'Toggle underlined text', { category: 'formatting' });

      // History shortcuts
      this.registerShortcut('ctrl+z', () => {
        this.editor.undo();
      }, 'Undo last change', { category: 'history' });

      this.registerShortcut('ctrl+y', () => {
        this.editor.redo();
      }, 'Redo last undone change', { category: 'history' });

      // Add Mac-specific command key shortcuts
      if (this._isMac()) {
        this.registerShortcut('cmd+b', () => {
          this.editor.format('bold');
        }, 'Toggle bold text', { category: 'formatting' });

        this.registerShortcut('cmd+i', () => {
          this.editor.format('italic');
        }, 'Toggle italic text', { category: 'formatting' });

        this.registerShortcut('cmd+z', () => {
          this.editor.undo();
        }, 'Undo last change', { category: 'history' });

        this.registerShortcut('cmd+shift+z', () => {
          this.editor.redo();
        }, 'Redo last undone change', { category: 'history' });
      }
    } catch (error) {
      console.error('[KeyboardManager] Error setting up default shortcuts:', error);
    }
  }

  /**
   * Bind keyboard events to the document
   * @private
   */
  private _bindEvents(): void {
    try {
      document.addEventListener('keydown', this.boundKeydownHandler);
    } catch (error) {
      console.error('[KeyboardManager] Failed to bind keyboard events:', error);
    }
  }

  /**
   * Remove all event listeners and clean up theme integration
   */
  destroy(): void {
    try {
      document.removeEventListener('keydown', this.boundKeydownHandler);
      this.shortcuts.clear();

      // Unsubscribe from theme changes
      if (this.themeUnsubscribe) {
        this.themeUnsubscribe();
        this.themeUnsubscribe = undefined;
      }
    } catch (error) {
      console.error('[KeyboardManager] Error during cleanup:', error);
    }
  }

  /**
   * Handle keydown events and execute shortcuts
   * @param event - Keyboard event object
   * @private
   */
  private _handleKeyDown(event: KeyboardEvent): void {
    // Guard against re-entrance (shortcuts triggering more events)
    if (this.isProcessingEvent) return;

    try {
      this.isProcessingEvent = true;

      // Check if event target is an input or contenteditable that we shouldn't override
      const target = event.target as HTMLElement;
      const isEditorElement = this.editor.getElement().contains(target);

      // If it's a text input outside our editor, don't intercept shortcuts
      if (!isEditorElement && this._isTextInput(target)) {
        return;
      }

      const combo = this._getKeyCombo(event);
      const shortcut = this.shortcuts.get(combo);

      if (shortcut && !shortcut.disabled) {
        // Only prevent default if we're handling this shortcut
        event.preventDefault();
        event.stopPropagation();

        // Execute the shortcut handler
        shortcut.handler();
      }
    } catch (error) {
      console.error('[KeyboardManager] Error handling keyboard event:', error);
    } finally {
      this.isProcessingEvent = false;
    }
  }

  /**
   * Check if the target element is a text input that should handle its own shortcuts
   * @param element - DOM element to check
   * @returns True if this is a text input element
   * @private
   */
  private _isTextInput(element: HTMLElement): boolean {
    if (!element) return false;

    const tag = element.tagName.toLowerCase();
    const type = (element as HTMLInputElement).type?.toLowerCase();

    // Input fields that should handle their own shortcuts
    if (tag === 'input' && ['text', 'password', 'email', 'search', 'tel', 'url', 'number'].includes(type)) {
      return true;
    }

    // Textarea and contenteditable
    return tag === 'textarea' || element.getAttribute('contenteditable') === 'true';
  }

  /**
   * Get standardized key combination string from keyboard event
   * @param event - Keyboard event
   * @returns Standardized key combination string
   * @private
   */
  private _getKeyCombo(event: KeyboardEvent): string {
    try {
      const parts: string[] = [];

      // Order modifiers consistently
      if (event.ctrlKey) parts.push('ctrl');
      if (event.altKey) parts.push('alt');
      if (event.shiftKey) parts.push('shift');
      if (event.metaKey) parts.push(this._isMac() ? 'cmd' : 'meta');

      // Normalize the key and add it
      let key = event.key.toLowerCase();

      // Map special keys to consistent names
      const keyMap = this._isMac() ? KeyboardManager.KEY_MAPS.mac : KeyboardManager.KEY_MAPS.other;
      key = keyMap[key] || key;

      // For single character keys, add the key
      parts.push(key);

      return parts.join('+');
    } catch (error) {
      console.error('[KeyboardManager] Error generating key combo:', error);
      return ''; // Return empty string for invalid combos
    }
  }

  /**
   * Normalize a shortcut key for consistent storage and lookup
   * @param key - Raw shortcut key
   * @returns Normalized shortcut key
   * @private
   */
  private _normalizeKey(key: string): string {
    return key.toLowerCase().replace(/\s+/g, '');
  }

  /**
   * Detect if running on macOS for platform-specific key mappings
   * @returns True if running on Mac
   * @private
   */
  private _isMac(): boolean {
    return typeof navigator !== 'undefined' &&
           /Mac|iPod|iPhone|iPad/.test(navigator.platform);
  }

  /**
   * Get all registered shortcuts organized by category
   * @returns Map of shortcuts and their descriptions
   */
  getShortcuts(): Record<string, { description: string; category?: string }> {
    const result: Record<string, { description: string; category?: string }> = {};

    try {
      for (const [key, { description, category, disabled }] of this.shortcuts) {
        if (!disabled) {
          result[key] = { description, category };
        }
      }
    } catch (error) {
      console.error('[KeyboardManager] Error getting shortcuts:', error);
    }

    return result;
  }

  /**
   * Get shortcuts organized by category
   * @returns Record of categories with their shortcuts
   */
  getShortcutsByCategory(): Record<string, Record<string, string>> {
    const result: Record<string, Record<string, string>> = {
      'uncategorized': {}
    };

    try {
      for (const [key, { description, category, disabled }] of this.shortcuts) {
        if (disabled) continue;

        const cat = category || 'uncategorized';

        if (!result[cat]) {
          result[cat] = {};
        }

        result[cat][key] = description;
      }
    } catch (error) {
      console.error('[KeyboardManager] Error categorizing shortcuts:', error);
    }

    return result;
  }

  /**
   * Enable or disable specific shortcuts
   * @param keys - Array of shortcut keys to modify
   * @param enabled - Whether to enable or disable the shortcuts
   * @returns Number of shortcuts successfully modified
   */
  setShortcutsEnabled(keys: string[], enabled: boolean): number {
    let count = 0;

    try {
      for (const key of keys) {
        const normalizedKey = this._normalizeKey(key);
        const shortcut = this.shortcuts.get(normalizedKey);

        if (shortcut) {
          shortcut.disabled = !enabled;
          count++;
        }
      }
    } catch (error) {
      console.error('[KeyboardManager] Error modifying shortcut states:', error);
    }

    return count;
  }

  /**
   * Handle theme changes and update visual feedback
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;
      // Update any existing visual feedback elements with new theme
      this._updateVisualFeedbackTheme(theme);
    } catch (error) {
      console.error('[KeyboardManager] Error handling theme change:', error);
    }
  }

  /**
   * Update visual feedback elements with new theme
   * @param theme - Theme to apply
   * @private
   */
  private _updateVisualFeedbackTheme(theme: ThemeDefinition): void {
    try {
      // Find any existing keyboard feedback elements and update their theme
      const feedbackElements = document.querySelectorAll('[data-keyboard-feedback]');
      feedbackElements.forEach(element => {
        if (element instanceof HTMLElement) {
          // Remove old theme classes
          element.classList.remove('theme-light', 'theme-dark');
          // Add new theme class
          element.classList.add(`theme-${theme.id}`);

          // Update CSS variables for theme-aware styling
          if (theme.stateColors?.focus) {
            element.style.setProperty('--keyboard-feedback-bg', theme.colors.surface);
            element.style.setProperty('--keyboard-feedback-text', theme.colors.text);
            element.style.setProperty('--keyboard-feedback-border', theme.stateColors.focus.border);
          }
        }
      });
    } catch (error) {
      console.error('[KeyboardManager] Error updating visual feedback theme:', error);
    }
  }

  /**
   * Create theme-aware visual feedback for keyboard shortcuts
   * @param shortcut - Shortcut key combination
   * @param description - Description of the shortcut
   * @returns HTMLElement for the feedback
   */
  public createShortcutFeedback(shortcut: string, description: string): HTMLElement {
    try {
      const feedback = document.createElement('div');
      feedback.setAttribute('data-keyboard-feedback', 'true');
      feedback.className = 'keyboard-shortcut-feedback';

      // Apply current theme if available
      if (this.currentTheme) {
        feedback.classList.add(`theme-${this.currentTheme.id}`);

        // Set theme-aware CSS variables
        if (this.currentTheme.stateColors?.focus) {
          feedback.style.setProperty('--keyboard-feedback-bg', this.currentTheme.colors.surface);
          feedback.style.setProperty('--keyboard-feedback-text', this.currentTheme.colors.text);
          feedback.style.setProperty('--keyboard-feedback-border', this.currentTheme.stateColors.focus.border);
        }
      }

      // Create shortcut display
      const shortcutSpan = document.createElement('span');
      shortcutSpan.className = 'keyboard-shortcut-key';
      shortcutSpan.textContent = shortcut;

      const descriptionSpan = document.createElement('span');
      descriptionSpan.className = 'keyboard-shortcut-description';
      descriptionSpan.textContent = description;

      feedback.appendChild(shortcutSpan);
      feedback.appendChild(descriptionSpan);

      return feedback;
    } catch (error) {
      console.error('[KeyboardManager] Error creating shortcut feedback:', error);
      return document.createElement('div'); // Return empty div as fallback
    }
  }

  /**
   * Get current theme for external use
   * @returns Current theme definition or null
   */
  public getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }
}