/**
 * Interface for the editor instance exposed to plugins.
 * This follows the interface segregation principle by exposing only
 * the functionality needed by plugins.
 */
export interface Editor {
  getElement(): HTMLElement;
  getContent(): string;
  setContent(html: string): void;
  format(command: string, value?: string | null): void;
  undo(): void;
  redo(): void;
  focus(): void;
  destroy(): void;
}

export interface HistoryManager {
  saveState(): void;
  undo(): void;
  redo(): void;
  canUndo(): boolean;
  canRedo(): boolean;
}

export interface ClipboardManager {
  destroy(): void;
}

/**
 * Plugin interface for all FeatherJS plugins
 */
export interface Plugin {
  /**
   * Unique identifier for the plugin
   */
  id: string;
  
  /**
   * Initializes the plugin with the editor instance
   * @param editor The editor instance
   */
  init(editor: Editor): void;
  
  /**
   * Optional cleanup method called when the plugin is destroyed
   */
  destroy?(): void;
  
  /**
   * Optional toolbar items for this plugin
   */
  toolbarItems?: ToolbarItem[];
}

/**
 * Toolbar item configuration
 */
export interface ToolbarItem {
  /**
   * Command to execute when the toolbar item is clicked
   */
  command: string;
  
  /**
   * Display label for the toolbar item
   */
  label: string;
  
  /**
   * Optional icon for the toolbar item (can be a symbol or an icon name)
   */
  icon?: string;
  
  /**
   * Optional keyboard shortcut for the command
   */
  shortcut?: string;
  
  /**
   * Optional title attribute for the toolbar button (maps to tooltip)
   */
  title?: string;
  
  /**
   * Optional ARIA label for accessibility
   */
  ariaLabel?: string;

  /**
   * Optional ARIA role
   */
  ariaRole?: string;

  /**
   * Optional group this item belongs to
   */
  group?: string;
  
  /**
   * Optional position for ordering
   */
  position?: number;
  
  /**
   * Optional component name if this toolbar item renders a custom component
   */
  component?: string;

  /**
   * Optional type (e.g. 'button', 'dropdown')
   */
  type?: string;

  /**
   * Unique ID for the toolbar item (often same as command or specific if multiple items use same command)
   */
  id?: string; // Added id as it's in ToolbarItemConfig and useful
}

export interface ThemeManager {
  init(): void;
  setTheme(theme: string): void;
}

export interface BatchOperation {
  handler: () => void;
  description?: string;
}

// === Core Editor Interfaces ===

/**
 * Manages the undo/redo history stack.
 */
export interface IHistoryManager {
  /** Saves the provided editor state to the history stack. */
  saveState(currentState: string): void;
  /** Reverts to the previous state and returns it. Returns null if no undo is possible. */
  undo(): string | null;
  /** Re-applies the previously undone state and returns it. Returns null if no redo is possible. */
  redo(): string | null;
  /** Checks if an undo operation is available. */
  canUndo(): boolean;
  /** Checks if a redo operation is available. */
  canRedo(): boolean;
  // Optional: Add event handling for history changes
  // onHistoryChange?(callback: (canUndo: boolean, canRedo: boolean) => void): void;
}

/**
 * Manages selection within the editor element.
 */
export interface ISelectionManager {
  /** Initializes the manager, setting up necessary event listeners. */
  initialize(element: HTMLElement): void;
  /** Removes event listeners and cleans up resources. */
  destroy(): void;
  /** Gets the current window selection object. */
  getSelection(): Selection | null;
  /** Gets the current selection range within the editor. */
  getRange(): Range | null;
  /** Sets the current selection range. */
  setRange(range: Range): void;
  /** Checks if the current selection is collapsed (cursor). */
  isCollapsed(): boolean;
  /** Gets the HTML content of the current selection. */
  getSelectedHtml(): string;
  // Optional: Add event handling for selection changes
  // onSelectionChange?(callback: (selection: Selection | null, range: Range | null) => void): void;
}

/**
 * Handles rendering and direct DOM manipulation of the editor content.
 */
export interface IRenderer {
  /** Initializes the renderer with the target editor element. */
  initialize(element: HTMLElement): void;
  /** Returns the editor's root HTML element. */
  getElement(): HTMLElement;
  /** Gets the current HTML content of the editor. */
  getContent(): string;
  /** Sets the HTML content of the editor. */
  setContent(html: string): void;
  /** Sets focus to the editor element. */
  focus(): void;
  /** Inserts a DOM node at the current cursor position or replaces the selection. */
  insertNode(node: Node): void;
  /** Deletes the content within the given range. */
  deleteContents(range: Range): void;
  /** Applies a given HTML state to the editor (used by HistoryManager). */
  applyState(html: string): void;
  /**
   * Scrolls the editor to make a node or range visible. Should mirror implementation in Renderer.
   * @param nodeOrRange Node or Range to scroll into view
   * @param options Optional ScrollIntoViewOptions controlling behavior.
   */
  scrollIntoView(nodeOrRange?: Node | Range | null, options?: ScrollIntoViewOptions): void;
  // Potential additions: Placeholder management, specific node manipulation
}

/**
 * Manages clipboard interactions (copy, cut, paste).
 */
export interface IClipboardManager {
  /** Initializes the manager, setting up listeners on the editor element. */
  initialize(element: HTMLElement, selectionManager: ISelectionManager, renderer: IRenderer): void;
  /** Removes event listeners and cleans up resources. */
  destroy(): void;
  // Internal clipboard logic (copy/cut/paste handlers) will use ISelectionManager and IRenderer.
}

/**
 * Applies text formatting commands to the selection.
 */
export interface IFormattingManager {
  /** Initializes the manager with necessary dependencies. */
  initialize(selectionManager: ISelectionManager, renderer: IRenderer): void;
  /** Applies a formatting command (e.g., 'bold', 'italic') to the current selection. */
  applyFormat(command: string, value?: string | null): void;
}

/**
 * Defines the public API of the Feather editor instance.
 */
export interface IEditorCore {
  /** Returns the editor's root HTML element. */
  getElement(): HTMLElement;
  /** Gets the current HTML content of the editor. */
  getContent(): string;
  /** Sets the HTML content of the editor, replacing existing content. */
  setContent(html: string): void;
  /** Applies a formatting command (e.g., 'bold') to the current selection. */
  format(command: string, value?: string | null): void;
  /** Undoes the last recorded action. */
  undo(): void;
  /** Redoes the last undone action. */
  redo(): void;
  /** Sets focus to the editor. */
  focus(): void;
  /** Destroys the editor instance, removing listeners and cleaning up resources. */
  destroy(): void;
}

export type FeatherEventBus = {
  publish: (event: string, data?: unknown) => void;
  subscribe: (
    event: string,
    cb: (data: unknown) => void
  ) => { unsubscribe: () => void };
};
