@import 'tailwindcss';

/*
 * CSS Layer Organization for FeatherJS Theme System
 *
 * Layer hierarchy (from lowest to highest specificity):
 * 1. base - Reset styles and fundamental layout
 * 2. theme-variables - CSS custom properties and semantic variables
 * 3. theme - Theme-specific styles and component theming
 * 4. components - Component-specific styles and patterns
 * 5. utilities - Utility classes and helper styles
 * 6. overrides - Theme overrides and media query fixes
 *
 * This organization ensures predictable cascade behavior and eliminates
 * specificity conflicts while maintaining theme consistency.
 */

/* Establish layer order - this must come before any @layer usage */
@layer base, theme-variables, theme, components, utilities, overrides;

:root {
  --feather-indent-size: 40px; /* Default size, can be overridden by themes */
}

@import './base.css';
@import './theme-variables.css'; /* Import semantic CSS variables */
@import './theme.css';
@import './components.css'; /* Import legacy component-specific styles */
@import './components/index.css'; /* Import modular component styles */
@import './utilities.css'; /* Import utility classes and helpers */
