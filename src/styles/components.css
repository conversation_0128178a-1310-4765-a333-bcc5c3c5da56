/*
 * Component-specific styles for FeatherJS
 * 
 * This file contains styles for UI components that use theme variables
 * but are not theme-specific themselves. These styles provide the
 * structural and behavioral styling for components.
 * 
 * Uses the 'components' layer for proper cascade hierarchy
 */

@layer components {
  /* ==========================================================================
     EDITOR COMPONENT STYLES
     Core editor styling using theme variables
     ========================================================================== */

  /* Editor content styling (basic defaults) */
  #editor p {
    margin-bottom: 16px;
  }

  #editor strong {
    font-weight: bold;
  }

  #editor em {
    font-style: italic;
  }

  #editor u {
    text-decoration: underline;
  }

  #editor s {
    text-decoration: line-through;
  }

  /* ==========================================================================
     ACCESSIBILITY COMPONENT STYLES
     Keyboard feedback and focus indicators using theme variables
     ========================================================================== */

  /* Keyboard feedback styling */
  .keyboard-shortcut-feedback {
    background: var(--theme-component-keyboard-feedback-background, var(--keyboard-feedback-bg, #ffffff));
    color: var(--theme-component-keyboard-feedback-text, var(--keyboard-feedback-text, #333333));
    border: 1px solid var(--theme-component-keyboard-feedback-border, var(--keyboard-feedback-border, #e0e0e0));
    box-shadow: var(--theme-component-keyboard-feedback-shadow, var(--keyboard-feedback-shadow, 0 2px 8px rgba(0, 0, 0, 0.1)));
    border-radius: var(--theme-border-radius-sm, 4px);
    padding: 8px 12px;
    font-size: 12px;
    font-family: var(--theme-font-family-mono, monospace);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
    z-index: var(--theme-z-index-tooltip, 1070);
    position: absolute;
    pointer-events: none;
  }

  .keyboard-shortcut-key {
    background: var(--theme-component-keyboard-feedback-border, var(--keyboard-feedback-border, #e0e0e0));
    color: var(--theme-component-keyboard-feedback-text, var(--keyboard-feedback-text, #333333));
    padding: 2px 6px;
    border-radius: var(--theme-border-radius-sm, 3px);
    font-weight: bold;
    font-size: 11px;
  }

  .keyboard-shortcut-description {
    font-size: 11px;
    opacity: var(--theme-opacity-hover, 0.8);
  }

  /* ==========================================================================
     CLIPBOARD COMPONENT STYLES
     Visual feedback for clipboard operations using theme variables
     ========================================================================== */

  /* Clipboard feedback styling */
  .clipboard-feedback {
    background: var(--theme-component-clipboard-feedback-background, var(--clipboard-feedback-bg, #ffffff));
    color: var(--theme-component-clipboard-feedback-text, var(--clipboard-feedback-text, #333333));
    border: 1px solid var(--theme-component-clipboard-feedback-border, var(--clipboard-feedback-border, #e0e0e0));
    box-shadow: var(--theme-component-clipboard-feedback-shadow, var(--clipboard-feedback-shadow, 0 4px 12px rgba(0, 0, 0, 0.15)));
    border-radius: var(--theme-border-radius-md, 6px);
    padding: 12px 16px;
    font-size: 14px;
    font-family: var(--theme-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all var(--theme-animation-duration, 200ms) var(--theme-animation-easing, ease-in-out);
    z-index: var(--theme-z-index-tooltip, 10000);
    position: fixed;
    pointer-events: none;
    min-width: 200px;
    animation: clipboardFadeIn var(--theme-animation-duration, 200ms) var(--theme-animation-easing-ease-out, ease-out);
  }

  .clipboard-feedback-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .clipboard-feedback-message {
    font-size: 13px;
    font-weight: 500;
  }

  /* ==========================================================================
     COMPONENT ANIMATIONS
     Keyframe animations for component interactions
     ========================================================================== */

  /* Clipboard feedback animations */
  @keyframes clipboardFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* ==========================================================================
     COMPONENT STATE STYLES
     Operation-specific styling for different component states
     ========================================================================== */

  /* Operation-specific clipboard feedback styling */
  .clipboard-feedback-copy {
    border-left: 4px solid var(--theme-color-primary, var(--color-blue-500, #3b82f6));
  }

  .clipboard-feedback-cut {
    border-left: 4px solid var(--theme-color-warning, var(--color-orange-500, #f97316));
  }

  .clipboard-feedback-paste {
    border-left: 4px solid var(--theme-color-success, var(--color-green-500, #22c55e));
  }

  .clipboard-feedback-error {
    border-left: 4px solid var(--theme-color-error, var(--color-red-500, #ef4444));
  }
}
