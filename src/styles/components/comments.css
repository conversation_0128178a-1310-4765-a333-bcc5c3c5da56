/*
 * Comments Component Styles
 * 
 * Comments panel component styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 * 
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     COMMENTS PANEL BASE STYLES
     Core comments panel styling and layout
     ========================================================================== */

  .feather-comment-panel {
    display: flex;
    flex-direction: column;
    background-color: var(--theme-plugin-comments-background, var(--theme-plugin-comments-background-fallback, #ffffff));
    border: 1px solid var(--theme-plugin-comments-border, var(--theme-plugin-comments-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    box-shadow: var(--theme-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
    max-height: 400px;
    overflow-y: auto;
  }

  /* ==========================================================================
     COMMENT ITEMS
     Individual comment styling
     ========================================================================== */

  .feather-comment {
    padding: var(--theme-spacing-md, 16px);
    border-bottom: 1px solid var(--theme-plugin-comments-border, var(--theme-plugin-comments-border-fallback, #cccccc));
    transition: background-color var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-comment:last-child {
    border-bottom: none;
  }

  .feather-comment:hover {
    background-color: var(--theme-plugin-comments-highlight-background, rgba(74, 134, 232, 0.1));
  }

  .feather-comment__header {
    display: flex;
    align-items: center;
    gap: var(--theme-spacing-sm, 8px);
    margin-bottom: var(--theme-spacing-xs, 4px);
  }

  .feather-comment__avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--theme-plugin-comments-avatar-background, var(--theme-color-surface-fallback, #f5f5f5));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    border: 1px solid var(--theme-plugin-comments-border, var(--theme-plugin-comments-border-fallback, #cccccc));
  }

  .feather-comment__author {
    font-weight: 600;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  .feather-comment__timestamp {
    font-size: 0.75rem;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    margin-left: auto;
  }

  .feather-comment__content {
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    line-height: 1.6;
    word-wrap: break-word;
  }

  /* ==========================================================================
     COMMENT FORM
     New comment input styling
     ========================================================================== */

  .feather-comment-form {
    padding: var(--theme-spacing-md, 16px);
    border-top: 1px solid var(--theme-plugin-comments-border, var(--theme-plugin-comments-border-fallback, #cccccc));
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
  }

  .feather-comment-form__textarea {
    width: 100%;
    min-height: 80px;
    padding: var(--theme-spacing-sm, 8px);
    border: 1px solid var(--theme-plugin-comments-border, var(--theme-plugin-comments-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-sm, 3px);
    background-color: var(--theme-plugin-comments-background, var(--theme-plugin-comments-background-fallback, #ffffff));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    resize: vertical;
    transition: border-color var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-comment-form__textarea:focus {
    outline: none;
    border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    box-shadow: 0 0 0 2px var(--theme-component-focus-ring-color, rgba(59, 130, 246, 0.5));
  }

  .feather-comment-form__actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--theme-spacing-sm, 8px);
    margin-top: var(--theme-spacing-sm, 8px);
  }

  /* ==========================================================================
     COMMENT STATES
     Different comment states and variants
     ========================================================================== */

  .feather-comment--highlighted {
    background-color: var(--theme-plugin-comments-highlight-background, rgba(74, 134, 232, 0.1));
    border-left: 4px solid var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    padding-left: calc(var(--theme-spacing-md, 16px) - 4px);
  }

  .feather-comment--resolved {
    opacity: 0.7;
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
  }

  .feather-comment--resolved .feather-comment__content {
    text-decoration: line-through;
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .feather-comment-panel {
      max-height: 300px;
      font-size: 0.875rem;
    }

    .feather-comment {
      padding: var(--theme-spacing-sm, 8px);
    }

    .feather-comment__avatar {
      width: 28px;
      height: 28px;
      font-size: 0.75rem;
    }

    .feather-comment-form {
      padding: var(--theme-spacing-sm, 8px);
    }

    .feather-comment-form__textarea {
      min-height: 60px;
      font-size: 0.875rem;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .feather-comment,
    .feather-comment-form__textarea {
      transition: none;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure comment panel respects user theme selection over system preferences */
  html.theme-light .feather-comment-panel {
    background-color: var(--theme-plugin-comments-background, #f9fafb) !important;
    border-color: var(--theme-plugin-comments-border, #d1d5db) !important;
    color: var(--theme-color-text, #1a202c) !important;
  }

  html.theme-dark .feather-comment-panel {
    background-color: var(--theme-plugin-comments-background, #1e293b) !important;
    border-color: var(--theme-plugin-comments-border, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
  }
}
