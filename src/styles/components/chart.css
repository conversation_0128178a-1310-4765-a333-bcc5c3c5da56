/*
 * Chart Component Styles
 * 
 * Chart component styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 * 
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     CHART WRAPPER BASE STYLES
     Core chart container styling and layout
     ========================================================================== */

  .feather-chart-wrapper {
    display: flex;
    flex-direction: column;
    background-color: var(--theme-plugin-chart-background, var(--theme-plugin-chart-background-fallback, #ffffff));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    padding: var(--theme-spacing-md, 16px);
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
    overflow: hidden;
  }

  /* ==========================================================================
     CHART CANVAS STYLES
     Canvas element styling
     ========================================================================== */

  canvas.feather-chart {
    background-color: var(--theme-plugin-chart-background, var(--theme-plugin-chart-background-fallback, #ffffff));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-sm, 3px);
    max-width: 100%;
    height: auto;
  }

  /* ==========================================================================
     CHART HEADER AND TITLE
     Chart title and metadata styling
     ========================================================================== */

  .feather-chart__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--theme-spacing-md, 16px);
    padding-bottom: var(--theme-spacing-sm, 8px);
    border-bottom: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  .feather-chart__title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    margin: 0;
  }

  .feather-chart__subtitle {
    font-size: 0.875rem;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    margin: var(--theme-spacing-xs, 4px) 0 0 0;
  }

  .feather-chart__actions {
    display: flex;
    gap: var(--theme-spacing-xs, 4px);
  }

  /* ==========================================================================
     CHART LEGEND
     Legend styling for chart data series
     ========================================================================== */

  .feather-chart__legend {
    display: flex;
    flex-wrap: wrap;
    gap: var(--theme-spacing-md, 16px);
    margin-top: var(--theme-spacing-md, 16px);
    padding-top: var(--theme-spacing-sm, 8px);
    border-top: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  .feather-chart__legend-item {
    display: flex;
    align-items: center;
    gap: var(--theme-spacing-xs, 4px);
    font-size: 0.875rem;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  .feather-chart__legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
  }

  /* Default legend colors */
  .feather-chart__legend-color--1 {
    background-color: var(--theme-plugin-chart-data-color-1, #1565c0);
  }

  .feather-chart__legend-color--2 {
    background-color: var(--theme-plugin-chart-data-color-2, #28a745);
  }

  .feather-chart__legend-color--3 {
    background-color: var(--theme-plugin-chart-data-color-3, #ffc107);
  }

  .feather-chart__legend-color--4 {
    background-color: var(--theme-plugin-chart-data-color-4, #dc3545);
  }

  .feather-chart__legend-color--5 {
    background-color: var(--theme-plugin-chart-data-color-5, #17a2b8);
  }

  .feather-chart__legend-color--6 {
    background-color: var(--theme-plugin-chart-data-color-6, #6f42c1);
  }

  /* ==========================================================================
     CHART VARIANTS
     Different chart styles for various use cases
     ========================================================================== */

  /* Compact chart */
  .feather-chart-wrapper--compact {
    padding: var(--theme-spacing-sm, 8px);
  }

  .feather-chart-wrapper--compact .feather-chart__header {
    margin-bottom: var(--theme-spacing-sm, 8px);
  }

  .feather-chart-wrapper--compact .feather-chart__title {
    font-size: 1rem;
  }

  .feather-chart-wrapper--compact .feather-chart__legend {
    margin-top: var(--theme-spacing-sm, 8px);
    gap: var(--theme-spacing-sm, 8px);
  }

  /* Large chart */
  .feather-chart-wrapper--large {
    padding: var(--theme-spacing-lg, 24px);
  }

  .feather-chart-wrapper--large .feather-chart__title {
    font-size: 1.25rem;
  }

  /* Borderless chart */
  .feather-chart-wrapper--borderless {
    border: none;
    box-shadow: none;
    padding: 0;
  }

  .feather-chart-wrapper--borderless canvas.feather-chart {
    border: none;
  }

  /* ==========================================================================
     CHART LOADING AND ERROR STATES
     Loading indicators and error messages
     ========================================================================== */

  .feather-chart__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    font-size: 0.875rem;
  }

  .feather-chart__error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    font-size: 0.875rem;
    text-align: center;
    padding: var(--theme-spacing-md, 16px);
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .feather-chart-wrapper {
      padding: var(--theme-spacing-sm, 8px);
    }

    .feather-chart__header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--theme-spacing-xs, 4px);
    }

    .feather-chart__title {
      font-size: 1rem;
    }

    .feather-chart__legend {
      gap: var(--theme-spacing-sm, 8px);
    }

    .feather-chart__legend-item {
      font-size: 0.75rem;
    }

    .feather-chart__loading,
    .feather-chart__error {
      min-height: 150px;
      font-size: 0.75rem;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .feather-chart-wrapper,
    canvas.feather-chart {
      border-width: 2px;
    }

    .feather-chart__header,
    .feather-chart__legend {
      border-width: 2px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure chart elements respect user theme selection over system preferences */
  html.theme-light canvas.feather-chart,
  html.theme-light .feather-chart-wrapper {
    background-color: var(--theme-plugin-chart-background, #ffffff) !important;
    border-color: var(--theme-color-border, #e5e7eb) !important;
  }

  html.theme-dark canvas.feather-chart,
  html.theme-dark .feather-chart-wrapper {
    background-color: var(--theme-plugin-chart-background, #0f172a) !important;
    border-color: var(--theme-color-border, #334155) !important;
  }

  /* Override any media query styles for chart elements */
  @media (prefers-color-scheme: light) {
    html.theme-dark canvas.feather-chart,
    html.theme-dark .feather-chart-wrapper {
      background-color: var(--theme-plugin-chart-background, #0f172a) !important;
      border-color: var(--theme-color-border, #334155) !important;
    }
  }

  @media (prefers-color-scheme: dark) {
    html.theme-light canvas.feather-chart,
    html.theme-light .feather-chart-wrapper {
      background-color: var(--theme-plugin-chart-background, #ffffff) !important;
      border-color: var(--theme-color-border, #e5e7eb) !important;
    }
  }
}
