/*
 * Collapsible Component Styles
 * 
 * Collapsible component styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 * 
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     COLLAPSIBLE BASE STYLES
     Core collapsible styling and layout
     ========================================================================== */

  .feather-collapsible {
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    overflow: hidden;
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  /* ==========================================================================
     COLLAPSIBLE HEADER
     Clickable header with toggle functionality
     ========================================================================== */

  .feather-collapsible__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--theme-spacing-md, 16px);
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border-bottom: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    cursor: pointer;
    user-select: none;
    transition: background-color var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-collapsible__header:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
  }

  .feather-collapsible__header:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
  }

  .feather-collapsible__title {
    font-weight: 600;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    margin: 0;
  }

  .feather-collapsible__toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    cursor: pointer;
    transition: transform var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-collapsible__toggle::before {
    content: "▼";
    font-size: 0.875rem;
    transition: transform var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-collapsible--collapsed .feather-collapsible__toggle::before {
    transform: rotate(-90deg);
  }

  /* ==========================================================================
     COLLAPSIBLE CONTENT
     Expandable content area
     ========================================================================== */

  .feather-collapsible__content {
    padding: var(--theme-spacing-md, 16px);
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    overflow: hidden;
    transition: max-height var(--theme-animation-duration-normal, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-collapsible--collapsed .feather-collapsible__content {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }

  .feather-collapsible--expanded .feather-collapsible__content {
    max-height: 1000px; /* Large enough to accommodate most content */
  }

  /* ==========================================================================
     COLLAPSIBLE VARIANTS
     Different collapsible styles for various use cases
     ========================================================================== */

  /* Borderless variant */
  .feather-collapsible--borderless {
    border: none;
    box-shadow: none;
  }

  .feather-collapsible--borderless .feather-collapsible__header {
    border-bottom: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  /* Compact variant */
  .feather-collapsible--compact .feather-collapsible__header,
  .feather-collapsible--compact .feather-collapsible__content {
    padding: var(--theme-spacing-sm, 8px);
  }

  /* Large variant */
  .feather-collapsible--large .feather-collapsible__header,
  .feather-collapsible--large .feather-collapsible__content {
    padding: var(--theme-spacing-lg, 24px);
  }

  /* Nested collapsibles */
  .feather-collapsible .feather-collapsible {
    margin: var(--theme-spacing-sm, 8px) 0;
    border-color: var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  .feather-collapsible .feather-collapsible .feather-collapsible__header {
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    font-size: 0.875rem;
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .feather-collapsible {
      font-size: 0.875rem;
    }

    .feather-collapsible__header,
    .feather-collapsible__content {
      padding: var(--theme-spacing-sm, 8px);
    }

    .feather-collapsible__title {
      font-size: 0.875rem;
    }

    .feather-collapsible__toggle {
      width: 20px;
      height: 20px;
    }

    .feather-collapsible--compact .feather-collapsible__header,
    .feather-collapsible--compact .feather-collapsible__content {
      padding: var(--theme-spacing-xs, 4px);
    }

    .feather-collapsible--large .feather-collapsible__header,
    .feather-collapsible--large .feather-collapsible__content {
      padding: var(--theme-spacing-md, 16px);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .feather-collapsible__header,
    .feather-collapsible__toggle,
    .feather-collapsible__toggle::before,
    .feather-collapsible__content {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .feather-collapsible {
      border-width: 2px;
    }

    .feather-collapsible__header {
      border-bottom-width: 2px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure collapsible respects user theme selection over system preferences */
  html.theme-light .feather-collapsible {
    border-color: var(--theme-color-border, #d1d5db) !important;
    color: var(--theme-color-text, #1a202c) !important;
  }

  html.theme-dark .feather-collapsible {
    border-color: var(--theme-color-border, #475569) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
  }
}
