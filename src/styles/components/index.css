/*
 * Component Styles Index
 *
 * Centralized import file for all component CSS modules.
 * This file imports all component styles in the proper order
 * to ensure consistent styling across the FeatherJS application.
 *
 * All components use semantic theme variables and follow BEM naming conventions.
 * Components are theme-agnostic and adapt automatically to light/dark themes.
 */

/* Import all component styles */
@import './button.css';
@import './input.css';
@import './table.css';
@import './code.css';
@import './dialog.css';
@import './overlay.css';
@import './presence.css';
@import './comments.css';
@import './collapsible.css';
@import './chart.css';
@import './math.css';

/*
 * Component Import Order Explanation:
 *
 * 1. button.css - Basic interactive elements
 * 2. input.css - Form controls and inputs
 * 3. table.css - Data display components
 * 4. code.css - Code display and syntax highlighting
 * 5. dialog.css - Modal and dialog overlays
 * 6. overlay.css - Tooltips, popovers, and notifications
 * 7. presence.css - User presence indicators
 * 8. comments.css - Comment panels and threads
 * 9. collapsible.css - Expandable content sections
 * 10. chart.css - Data visualization components
 * 11. math.css - Mathematical expression rendering
 *
 * This order ensures that more complex components can build upon
 * simpler ones without specificity conflicts, thanks to the
 * CSS layer system established in Phase 3.1.2.
 */
