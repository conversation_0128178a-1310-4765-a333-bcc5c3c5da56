/*
 * Presence Component Styles
 * 
 * Presence bar component styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 * 
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     PRESENCE BAR BASE STYLES
     Core presence bar styling and layout
     ========================================================================== */

  .feather-presence-bar {
    display: flex;
    align-items: center;
    gap: var(--theme-spacing-sm, 8px);
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-sm, 14px);
    line-height: var(--theme-line-height-base, 1.5);
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  /* ==========================================================================
     PRESENCE INDICATORS
     User presence status indicators
     ========================================================================== */

  .feather-presence-indicator {
    display: flex;
    align-items: center;
    gap: var(--theme-spacing-xs, 4px);
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    border-radius: var(--theme-border-radius-sm, 3px);
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .feather-presence-indicator:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
  }

  .feather-presence-indicator__avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
  }

  .feather-presence-indicator__name {
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-weight: 500;
    white-space: nowrap;
  }

  .feather-presence-indicator__status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  /* Status colors */
  .feather-presence-indicator__status--online {
    background-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
  }

  .feather-presence-indicator__status--away {
    background-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
  }

  .feather-presence-indicator__status--offline {
    background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .feather-presence-bar {
      padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
      font-size: 0.75rem;
      gap: var(--theme-spacing-xs, 4px);
    }

    .feather-presence-indicator {
      padding: 2px var(--theme-spacing-xs, 4px);
    }

    .feather-presence-indicator__avatar {
      width: 20px;
      height: 20px;
      font-size: 0.625rem;
    }

    .feather-presence-indicator__name {
      display: none; /* Hide names on mobile to save space */
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .feather-presence-indicator {
      transition: none;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure presence bar respects user theme selection over system preferences */
  html.theme-light .feather-presence-bar {
    background-color: var(--theme-color-surface, #f9fafb) !important;
    border-color: var(--theme-color-border, #e5e7eb) !important;
  }

  html.theme-dark .feather-presence-bar {
    background-color: var(--theme-color-surface, #1e293b) !important;
    border-color: var(--theme-color-border, #334155) !important;
  }
}
