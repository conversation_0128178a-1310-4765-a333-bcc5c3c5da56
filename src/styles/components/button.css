/*
 * Button Component Styles
 *
 * Button variants and states using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 *
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     BUTTON BASE STYLES
     Core button styling and reset
     ========================================================================== */

  .button {
    /* Reset browser defaults */
    appearance: none;
    border: none;
    background: none;
    margin: 0;
    padding: 0;
    font: inherit;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    /* Base styling */
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    font-weight: 500;
    line-height: var(--theme-line-height-base, 1.5);
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;

    /* Layout and spacing */
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    border-radius: var(--theme-border-radius-md, 6px);
    border: 1px solid transparent;
    gap: var(--theme-spacing-xs, 4px);

    /* Transitions */
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);

    /* Default variant styling */
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    border-color: var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  /* Button focus state */
  .button:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
  }

  /* Button hover state */
  .button:hover:not(:disabled) {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
    color: var(--theme-hover-text, var(--theme-hover-text-fallback, #000000));
    transform: translateY(-1px);
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  /* Button active state */
  .button:active:not(:disabled) {
    background-color: var(--theme-active-background, var(--theme-active-background-fallback, #e0e0e0));
    border-color: var(--theme-active-border, var(--theme-active-border-fallback, #cccccc));
    color: var(--theme-active-text, var(--theme-active-text-fallback, #000000));
    transform: translateY(0);
    box-shadow: none;
  }

  /* Button disabled state */
  .button:disabled {
    background-color: var(--theme-disabled-background, var(--theme-disabled-background-fallback, #f0f0f0));
    border-color: var(--theme-disabled-border, var(--theme-disabled-border-fallback, #e0e0e0));
    color: var(--theme-disabled-text, var(--theme-disabled-text-fallback, #999999));
    cursor: not-allowed;
    opacity: var(--theme-opacity-disabled, 0.6);
    transform: none;
    box-shadow: none;
  }

  /* ==========================================================================
     BUTTON VARIANTS
     Different button styles for various use cases
     ========================================================================== */

  /* Primary button */
  .button--primary {
    background-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    color: white;
    border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
  }

  .button--primary:hover:not(:disabled) {
    background-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    color: white;
    filter: brightness(1.1);
  }

  .button--primary:active:not(:disabled) {
    filter: brightness(0.9);
  }

  /* Secondary button */
  .button--secondary {
    background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    color: white;
    border-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
  }

  .button--secondary:hover:not(:disabled) {
    background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    border-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    color: white;
    filter: brightness(1.1);
  }

  .button--secondary:active:not(:disabled) {
    filter: brightness(0.9);
  }

  /* Success button */
  .button--success {
    background-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    color: white;
    border-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
  }

  .button--success:hover:not(:disabled) {
    background-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    border-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    color: white;
    filter: brightness(1.1);
  }

  .button--success:active:not(:disabled) {
    filter: brightness(0.9);
  }

  /* Warning button */
  .button--warning {
    background-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
    color: white;
    border-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
  }

  .button--warning:hover:not(:disabled) {
    background-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
    border-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
    color: white;
    filter: brightness(1.1);
  }

  .button--warning:active:not(:disabled) {
    filter: brightness(0.9);
  }

  /* Error/Danger button */
  .button--error {
    background-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    color: white;
    border-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
  }

  .button--error:hover:not(:disabled) {
    background-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    border-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    color: white;
    filter: brightness(1.1);
  }

  .button--error:active:not(:disabled) {
    filter: brightness(0.9);
  }

  /* Ghost/Outline button */
  .button--ghost {
    background-color: transparent;
    color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
  }

  .button--ghost:hover:not(:disabled) {
    background-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    color: white;
    border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
  }

  /* Link button */
  .button--link {
    background-color: transparent;
    color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    border-color: transparent;
    text-decoration: underline;
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  }

  .button--link:hover:not(:disabled) {
    background-color: transparent;
    color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    border-color: transparent;
    text-decoration: none;
    transform: none;
    box-shadow: none;
    filter: brightness(1.1);
  }

  .button--link:active:not(:disabled) {
    filter: brightness(0.9);
  }

  /* ==========================================================================
     BUTTON SIZES
     Different button sizes for various contexts
     ========================================================================== */

  .button--small {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    font-size: 0.875rem;
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .button--large {
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
    font-size: 1.125rem;
    border-radius: var(--theme-border-radius-lg, 8px);
  }

  .button--block {
    width: 100%;
    display: flex;
  }

  /* ==========================================================================
     BUTTON ICONS AND CONTENT
     Icon positioning and content layout
     ========================================================================== */

  .button__icon {
    flex-shrink: 0;
    width: 1em;
    height: 1em;
  }

  .button__icon--left {
    margin-right: var(--theme-spacing-xs, 4px);
  }

  .button__icon--right {
    margin-left: var(--theme-spacing-xs, 4px);
  }

  .button--icon-only {
    padding: var(--theme-spacing-sm, 8px);
    aspect-ratio: 1;
  }

  .button--icon-only.button--small {
    padding: var(--theme-spacing-xs, 4px);
  }

  .button--icon-only.button--large {
    padding: var(--theme-spacing-md, 16px);
  }

  /* ==========================================================================
     BUTTON GROUPS
     Styling for grouped buttons
     ========================================================================== */

  .button-group {
    display: inline-flex;
    border-radius: var(--theme-border-radius-md, 6px);
    overflow: hidden;
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  .button-group .button {
    border-radius: 0;
    border-right-width: 0;
  }

  .button-group .button:first-child {
    border-top-left-radius: var(--theme-border-radius-md, 6px);
    border-bottom-left-radius: var(--theme-border-radius-md, 6px);
  }

  .button-group .button:last-child {
    border-top-right-radius: var(--theme-border-radius-md, 6px);
    border-bottom-right-radius: var(--theme-border-radius-md, 6px);
    border-right-width: 1px;
  }

  .button-group .button:not(:first-child):not(:last-child) {
    border-radius: 0;
  }

  /* ==========================================================================
     ACCESSIBILITY AND RESPONSIVE DESIGN
     Screen reader support and mobile adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .button {
      padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
      font-size: 1rem;
    }

    .button--small {
      padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
      font-size: 0.875rem;
    }

    .button--large {
      padding: var(--theme-spacing-md, 16px) var(--theme-spacing-xl, 32px);
      font-size: 1.125rem;
    }

    .button-group {
      flex-direction: column;
    }

    .button-group .button {
      border-right-width: 1px;
      border-bottom-width: 0;
    }

    .button-group .button:first-child {
      border-radius: var(--theme-border-radius-md, 6px) var(--theme-border-radius-md, 6px) 0 0;
    }

    .button-group .button:last-child {
      border-radius: 0 0 var(--theme-border-radius-md, 6px) var(--theme-border-radius-md, 6px);
      border-bottom-width: 1px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .button {
      transition: none;
      transform: none;
    }

    .button:hover:not(:disabled),
    .button:active:not(:disabled) {
      transform: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .button {
      border-width: 2px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure toolbar buttons respect user theme selection over system preferences */
  html.theme-light #toolbar button {
    background-color: var(--theme-color-surface, #f3f4f6) !important;
    color: var(--theme-color-text, #374151) !important;
    border-color: var(--theme-color-border, #d1d5db) !important;
  }

  html.theme-light #toolbar button:hover {
    background-color: var(--theme-hover-background, #e5e7eb) !important;
  }

  html.theme-dark #toolbar button {
    background-color: var(--theme-color-surface, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
    border-color: var(--theme-color-border, #475569) !important;
  }

  html.theme-dark #toolbar button:hover {
    background-color: var(--theme-hover-background, #475569) !important;
  }
}
