/*
 * Dialog Component Styles
 *
 * Universal dialog styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 *
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     DIALOG BASE STYLES
     Core dialog container and backdrop styling
     ========================================================================== */

  /* Dialog backdrop - covers entire viewport */
  .dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    z-index: var(--theme-z-index-modal-backdrop, 1040);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--theme-spacing-md, 16px);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                visibility var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  /* Active backdrop state */
  .dialog-backdrop--active {
    opacity: 1;
    visibility: visible;
  }

  /* Dialog container */
  .dialog {
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-lg, 8px);
    box-shadow: var(--theme-shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04));
    max-width: 90vw;
    max-height: 90vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: var(--theme-z-index-modal, 1050);
    transform: scale(0.95) translateY(-20px);
    transition: transform var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  /* Active dialog state */
  .dialog-backdrop--active .dialog {
    transform: scale(1) translateY(0);
  }

  /* ==========================================================================
     DIALOG SIZE VARIANTS
     Different dialog sizes for various use cases
     ========================================================================== */

  .dialog--small {
    max-width: 400px;
  }

  .dialog--medium {
    max-width: 600px;
  }

  .dialog--large {
    max-width: 800px;
  }

  .dialog--fullscreen {
    max-width: 95vw;
    max-height: 95vh;
    width: 95vw;
    height: 95vh;
  }

  /* ==========================================================================
     DIALOG HEADER STYLES
     Dialog title and close button area
     ========================================================================== */

  .dialog__header {
    padding: var(--theme-spacing-lg, 24px) var(--theme-spacing-lg, 24px) var(--theme-spacing-md, 16px);
    border-bottom: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
  }

  .dialog__title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    margin: 0;
    line-height: var(--theme-line-height-base, 1.5);
  }

  .dialog__close-button {
    background: transparent;
    border: none;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    cursor: pointer;
    padding: var(--theme-spacing-sm, 8px);
    border-radius: var(--theme-border-radius-sm, 3px);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
    font-size: 1.25rem;
    line-height: 1;
    width: 32px;
    height: 32px;
  }

  .dialog__close-button:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    color: var(--theme-hover-text, var(--theme-hover-text-fallback, #000000));
  }

  .dialog__close-button:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
  }

  /* ==========================================================================
     DIALOG CONTENT STYLES
     Main content area with scrolling support
     ========================================================================== */

  .dialog__content {
    padding: var(--theme-spacing-lg, 24px);
    overflow-y: auto;
    flex-grow: 1;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    line-height: var(--theme-line-height-base, 1.5);
  }

  .dialog__content::-webkit-scrollbar {
    width: 8px;
  }

  .dialog__content::-webkit-scrollbar-track {
    background: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .dialog__content::-webkit-scrollbar-thumb {
    background: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .dialog__content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  /* ==========================================================================
     DIALOG FOOTER STYLES
     Action buttons and footer content area
     ========================================================================== */

  .dialog__footer {
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px) var(--theme-spacing-lg, 24px);
    border-top: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--theme-spacing-sm, 8px);
    flex-shrink: 0;
  }

  .dialog__footer--centered {
    justify-content: center;
  }

  .dialog__footer--space-between {
    justify-content: space-between;
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .dialog-backdrop {
      padding: var(--theme-spacing-sm, 8px);
    }

    .dialog {
      max-width: 100%;
      max-height: 100%;
      border-radius: var(--theme-border-radius-md, 6px);
    }

    .dialog--fullscreen {
      max-width: 100vw;
      max-height: 100vh;
      width: 100vw;
      height: 100vh;
      border-radius: 0;
    }

    .dialog__header {
      padding: var(--theme-spacing-md, 16px);
    }

    .dialog__content {
      padding: var(--theme-spacing-md, 16px);
    }

    .dialog__footer {
      padding: var(--theme-spacing-md, 16px);
      flex-direction: column;
      gap: var(--theme-spacing-sm, 8px);
    }

    .dialog__footer--space-between {
      flex-direction: column;
      align-items: stretch;
    }
  }

  @media (max-width: 480px) {
    .dialog__title {
      font-size: 1.125rem;
    }

    .dialog__footer {
      gap: var(--theme-spacing-xs, 4px);
    }
  }

  /* ==========================================================================
     ACCESSIBILITY ENHANCEMENTS
     Screen reader and keyboard navigation support
     ========================================================================== */

  .dialog[role="dialog"] {
    /* Ensure proper ARIA role is applied */
  }

  .dialog__header[role="banner"] {
    /* Optional: semantic role for header */
  }

  .dialog__content[role="main"] {
    /* Optional: semantic role for main content */
  }

  /* Focus trap styling */
  .dialog--focus-trapped {
    /* Additional styling for focus-trapped dialogs */
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .dialog-backdrop,
    .dialog,
    .dialog__close-button {
      transition: none;
    }

    .dialog {
      transform: none;
    }

    .dialog-backdrop--active .dialog {
      transform: none;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure plugin UI elements respect user theme selection over system preferences */
  html.theme-light [role="dialog"],
  html.theme-light [role="menu"],
  html.theme-light .absolute.bg-white {
    background-color: var(--theme-color-background, #ffffff) !important;
    color: var(--theme-color-text, #1a202c) !important;
    border-color: var(--theme-color-border, #d1d5db) !important;
  }

  html.theme-dark [role="dialog"],
  html.theme-dark [role="menu"],
  html.theme-dark .absolute.dark\:bg-slate-700,
  html.theme-dark .absolute.dark\:bg-slate-800 {
    background-color: var(--theme-color-background, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
    border-color: var(--theme-color-border, #475569) !important;
  }

  /* Ensure plugin UI buttons respect theme */
  html.theme-light [role="dialog"] button,
  html.theme-light [role="menu"] button {
    color: var(--theme-color-text, #374151) !important;
  }

  html.theme-dark [role="dialog"] button,
  html.theme-dark [role="menu"] button {
    color: var(--theme-color-text, #e2e8f0) !important;
  }

  /* Ensure plugin UI tabs respect theme */
  html.theme-light [role="tablist"] button {
    background-color: var(--theme-color-surface, #f3f4f6) !important;
    color: var(--theme-color-text, #374151) !important;
  }

  html.theme-dark [role="tablist"] button {
    background-color: var(--theme-color-surface, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
  }
}
