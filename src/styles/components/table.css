/*
 * Table Component Styles
 *
 * Table component styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 *
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     TABLE BASE STYLES
     Core table styling and reset
     ========================================================================== */

  .table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    background-color: var(--theme-plugin-table-background, var(--theme-plugin-table-background-fallback, #ffffff));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    border: 1px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    overflow: hidden;
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  /* Table header */
  .table__header {
    background-color: var(--theme-plugin-table-header-background, var(--theme-color-surface-fallback, #f5f5f5));
    font-weight: 600;
  }

  .table__header-cell {
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-sm, 8px);
    text-align: left;
    border-bottom: 2px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
    border-right: 1px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .table__header-cell:last-child {
    border-right: none;
  }

  /* Table body */
  .table__body {
    background-color: var(--theme-plugin-table-background, var(--theme-plugin-table-background-fallback, #ffffff));
  }

  .table__row {
    transition: background-color var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .table__row:nth-child(even) {
    background-color: var(--theme-plugin-table-alternate-row-background, var(--theme-color-surface-fallback, #f5f5f5));
  }

  .table__row:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
  }

  .table__cell {
    padding: var(--theme-spacing-sm, 8px);
    border-bottom: 1px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
    border-right: 1px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
    vertical-align: top;
  }

  .table__cell:last-child {
    border-right: none;
  }

  .table__row:last-child .table__cell {
    border-bottom: none;
  }

  /* ==========================================================================
     TABLE VARIANTS
     Different table styles for various use cases
     ========================================================================== */

  /* Bordered table */
  .table--bordered {
    border: 2px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
  }

  .table--bordered .table__header-cell,
  .table--bordered .table__cell {
    border-width: 1px;
  }

  /* Borderless table */
  .table--borderless {
    border: none;
    box-shadow: none;
  }

  .table--borderless .table__header-cell,
  .table--borderless .table__cell {
    border: none;
  }

  /* Striped table */
  .table--striped .table__row:nth-child(odd) {
    background-color: var(--theme-plugin-table-background, var(--theme-plugin-table-background-fallback, #ffffff));
  }

  .table--striped .table__row:nth-child(even) {
    background-color: var(--theme-plugin-table-alternate-row-background, var(--theme-color-surface-fallback, #f5f5f5));
  }

  /* Compact table */
  .table--compact .table__header-cell,
  .table--compact .table__cell {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
  }

  /* Spacious table */
  .table--spacious .table__header-cell,
  .table--spacious .table__cell {
    padding: var(--theme-spacing-lg, 24px) var(--theme-spacing-md, 16px);
  }

  /* ==========================================================================
     TABLE CELL ALIGNMENT
     Text alignment utilities for table cells
     ========================================================================== */

  .table__cell--center,
  .table__header-cell--center {
    text-align: center;
  }

  .table__cell--right,
  .table__header-cell--right {
    text-align: right;
  }

  .table__cell--left,
  .table__header-cell--left {
    text-align: left;
  }

  .table__cell--top {
    vertical-align: top;
  }

  .table__cell--middle {
    vertical-align: middle;
  }

  .table__cell--bottom {
    vertical-align: bottom;
  }

  /* ==========================================================================
     TABLE CELL TYPES
     Special cell styling for different content types
     ========================================================================== */

  .table__cell--numeric {
    text-align: right;
    font-family: var(--theme-font-family-mono, monospace);
    font-variant-numeric: tabular-nums;
  }

  .table__cell--currency {
    text-align: right;
    font-family: var(--theme-font-family-mono, monospace);
    font-variant-numeric: tabular-nums;
  }

  .table__cell--currency::before {
    content: "$";
    margin-right: 0.25em;
  }

  .table__cell--percentage {
    text-align: right;
    font-family: var(--theme-font-family-mono, monospace);
    font-variant-numeric: tabular-nums;
  }

  .table__cell--percentage::after {
    content: "%";
    margin-left: 0.25em;
  }

  .table__cell--date {
    font-family: var(--theme-font-family-mono, monospace);
    white-space: nowrap;
  }

  .table__cell--status {
    text-align: center;
  }

  .table__cell--actions {
    text-align: center;
    white-space: nowrap;
  }

  /* ==========================================================================
     TABLE STATUS INDICATORS
     Visual indicators for different states
     ========================================================================== */

  .table__status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    border-radius: var(--theme-border-radius-sm, 3px);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .table__status-badge--success {
    background-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    color: white;
  }

  .table__status-badge--warning {
    background-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
    color: white;
  }

  .table__status-badge--error {
    background-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    color: white;
  }

  .table__status-badge--info {
    background-color: var(--theme-color-info, var(--theme-color-info-fallback, #0099cc));
    color: white;
  }

  .table__status-badge--neutral {
    background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    color: white;
  }

  /* ==========================================================================
     TABLE SORTING
     Sortable column header styling
     ========================================================================== */

  .table__header-cell--sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: calc(var(--theme-spacing-sm, 8px) + 20px);
  }

  .table__header-cell--sortable:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
  }

  .table__header-cell--sortable::after {
    content: "";
    position: absolute;
    right: var(--theme-spacing-sm, 8px);
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    opacity: 0.3;
    transition: opacity var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .table__header-cell--sortable:hover::after {
    opacity: 0.7;
  }

  .table__header-cell--sorted-asc::after {
    border-bottom: 4px solid var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    opacity: 1;
  }

  .table__header-cell--sorted-desc::after {
    border-bottom: none;
    border-top: 4px solid var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    opacity: 1;
  }

  /* ==========================================================================
     TABLE CONTAINER AND SCROLLING
     Responsive table wrapper with horizontal scrolling
     ========================================================================== */

  .table-container {
    width: 100%;
    overflow-x: auto;
    border-radius: var(--theme-border-radius-md, 6px);
    border: 1px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
  }

  .table-container .table {
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  /* Custom scrollbar styling */
  .table-container::-webkit-scrollbar {
    height: 8px;
  }

  .table-container::-webkit-scrollbar-track {
    background: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .table-container::-webkit-scrollbar-thumb {
    background: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    background: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .table {
      font-size: 0.875rem;
    }

    .table__header-cell,
    .table__cell {
      padding: var(--theme-spacing-xs, 4px);
    }

    .table--compact .table__header-cell,
    .table--compact .table__cell {
      padding: 2px var(--theme-spacing-xs, 4px);
    }

    .table--spacious .table__header-cell,
    .table--spacious .table__cell {
      padding: var(--theme-spacing-md, 16px) var(--theme-spacing-sm, 8px);
    }

    /* Stack table on very small screens */
    .table--responsive {
      border: none;
      box-shadow: none;
    }

    .table--responsive .table__header {
      display: none;
    }

    .table--responsive .table__row {
      display: block;
      border: 1px solid var(--theme-plugin-table-border, var(--theme-plugin-table-border-fallback, #cccccc));
      border-radius: var(--theme-border-radius-md, 6px);
      margin-bottom: var(--theme-spacing-sm, 8px);
      padding: var(--theme-spacing-sm, 8px);
    }

    .table--responsive .table__cell {
      display: block;
      border: none;
      padding: var(--theme-spacing-xs, 4px) 0;
      text-align: left !important;
    }

    .table--responsive .table__cell::before {
      content: attr(data-label) ": ";
      font-weight: 600;
      margin-right: var(--theme-spacing-xs, 4px);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .table__row,
    .table__header-cell--sortable::after {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .table {
      border-width: 2px;
    }

    .table__header-cell,
    .table__cell {
      border-width: 1px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure table elements respect user theme selection over system preferences */
  html.theme-light .feather-table,
  html.theme-light table.feather-table {
    background-color: var(--theme-plugin-table-background, #ffffff) !important;
    color: var(--theme-color-text, #1a202c) !important;
  }

  html.theme-light .feather-table td,
  html.theme-light .feather-table th,
  html.theme-light table.feather-table td,
  html.theme-light table.feather-table th {
    border-color: var(--theme-plugin-table-border, #d1d5db) !important;
    background-color: var(--theme-plugin-table-background, #ffffff) !important;
    color: var(--theme-color-text, #1a202c) !important;
  }

  html.theme-dark .feather-table,
  html.theme-dark table.feather-table {
    background-color: var(--theme-plugin-table-background, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
  }

  html.theme-dark .feather-table td,
  html.theme-dark .feather-table th,
  html.theme-dark table.feather-table td,
  html.theme-dark table.feather-table th {
    border-color: var(--theme-plugin-table-border, #475569) !important;
    background-color: var(--theme-plugin-table-background, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
  }

  /* Override any media query styles for tables */
  @media (prefers-color-scheme: light) {
    html.theme-dark .feather-table,
    html.theme-dark .feather-table td,
    html.theme-dark .feather-table th,
    html.theme-dark table.feather-table,
    html.theme-dark table.feather-table td,
    html.theme-dark table.feather-table th {
      background-color: var(--theme-plugin-table-background, #334155) !important;
      color: var(--theme-color-text, #e2e8f0) !important;
      border-color: var(--theme-plugin-table-border, #475569) !important;
    }
  }

  @media (prefers-color-scheme: dark) {
    html.theme-light .feather-table,
    html.theme-light .feather-table td,
    html.theme-light .feather-table th,
    html.theme-light table.feather-table,
    html.theme-light table.feather-table td,
    html.theme-light table.feather-table th {
      background-color: var(--theme-plugin-table-background, #ffffff) !important;
      color: var(--theme-color-text, #1a202c) !important;
      border-color: var(--theme-plugin-table-border, #d1d5db) !important;
    }
  }
}
