/*
 * Input Component Styles
 *
 * Form input styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 *
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     INPUT BASE STYLES
     Core input styling and reset
     ========================================================================== */

  .input {
    /* Reset browser defaults */
    appearance: none;
    border: none;
    background: none;
    margin: 0;
    padding: 0;
    font: inherit;

    /* Base styling */
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));

    /* Layout and spacing */
    width: 100%;
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));

    /* Transitions */
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  /* Input placeholder styling */
  .input::placeholder {
    color: var(--theme-component-editor-placeholder, #9ca3af);
    opacity: 1;
  }

  /* Input focus state */
  .input:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
    border-color: var(--theme-focus-border, var(--theme-focus-border-fallback, #0066cc));
    background-color: var(--theme-focus-background, var(--theme-focus-background-fallback, #ffffff));
  }

  /* Input hover state */
  .input:hover:not(:disabled):not(:focus) {
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
  }

  /* Input disabled state */
  .input:disabled {
    background-color: var(--theme-disabled-background, var(--theme-disabled-background-fallback, #f0f0f0));
    border-color: var(--theme-disabled-border, var(--theme-disabled-border-fallback, #e0e0e0));
    color: var(--theme-disabled-text, var(--theme-disabled-text-fallback, #999999));
    cursor: not-allowed;
    opacity: var(--theme-opacity-disabled, 0.6);
  }

  .input:disabled::placeholder {
    color: var(--theme-disabled-text, var(--theme-disabled-text-fallback, #999999));
  }

  /* Input readonly state */
  .input:read-only {
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    cursor: default;
  }

  /* ==========================================================================
     INPUT VARIANTS
     Different input styles for various use cases
     ========================================================================== */

  /* Error state */
  .input--error {
    border-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
  }

  .input--error:focus {
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    border-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
  }

  /* Success state */
  .input--success {
    border-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
  }

  .input--success:focus {
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    border-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
  }

  /* Warning state */
  .input--warning {
    border-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
  }

  .input--warning:focus {
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
    border-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
  }

  /* ==========================================================================
     INPUT SIZES
     Different input sizes for various contexts
     ========================================================================== */

  .input--small {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    font-size: 0.875rem;
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .input--large {
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
    font-size: 1.125rem;
    border-radius: var(--theme-border-radius-lg, 8px);
  }

  /* ==========================================================================
     TEXTAREA STYLES
     Multi-line text input styling
     ========================================================================== */

  .textarea {
    /* Inherit all input styles */
    @extend .input;

    /* Textarea-specific styling */
    min-height: 80px;
    resize: vertical;
    font-family: var(--theme-font-family, sans-serif);
    line-height: var(--theme-line-height-base, 1.5);
  }

  /* Since @extend isn't available in pure CSS, we'll duplicate the styles */
  .textarea {
    appearance: none;
    border: none;
    background: none;
    margin: 0;
    padding: 0;
    font: inherit;
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    width: 100%;
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
    min-height: 80px;
    resize: vertical;
  }

  .textarea::placeholder {
    color: var(--theme-component-editor-placeholder, #9ca3af);
    opacity: 1;
  }

  .textarea:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
    border-color: var(--theme-focus-border, var(--theme-focus-border-fallback, #0066cc));
    background-color: var(--theme-focus-background, var(--theme-focus-background-fallback, #ffffff));
  }

  .textarea:hover:not(:disabled):not(:focus) {
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
  }

  .textarea:disabled {
    background-color: var(--theme-disabled-background, var(--theme-disabled-background-fallback, #f0f0f0));
    border-color: var(--theme-disabled-border, var(--theme-disabled-border-fallback, #e0e0e0));
    color: var(--theme-disabled-text, var(--theme-disabled-text-fallback, #999999));
    cursor: not-allowed;
    opacity: var(--theme-opacity-disabled, 0.6);
    resize: none;
  }

  /* ==========================================================================
     SELECT STYLES
     Dropdown select input styling
     ========================================================================== */

  .select {
    /* Inherit all input styles */
    appearance: none;
    border: none;
    background: none;
    margin: 0;
    padding: 0;
    font: inherit;
    font-family: var(--theme-font-family, sans-serif);
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    width: 100%;
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);

    /* Select-specific styling */
    cursor: pointer;
    padding-right: calc(var(--theme-spacing-lg, 24px) + var(--theme-spacing-md, 16px));
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
    background-position: right var(--theme-spacing-md, 16px) center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
  }

  .select:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
    border-color: var(--theme-focus-border, var(--theme-focus-border-fallback, #0066cc));
    background-color: var(--theme-focus-background, var(--theme-focus-background-fallback, #ffffff));
  }

  .select:hover:not(:disabled):not(:focus) {
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
  }

  .select:disabled {
    background-color: var(--theme-disabled-background, var(--theme-disabled-background-fallback, #f0f0f0));
    border-color: var(--theme-disabled-border, var(--theme-disabled-border-fallback, #e0e0e0));
    color: var(--theme-disabled-text, var(--theme-disabled-text-fallback, #999999));
    cursor: not-allowed;
    opacity: var(--theme-opacity-disabled, 0.6);
  }

  /* ==========================================================================
     INPUT GROUPS AND LABELS
     Form field grouping and labeling
     ========================================================================== */

  .input-group {
    display: flex;
    flex-direction: column;
    gap: var(--theme-spacing-xs, 4px);
  }

  .input-group__label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    margin-bottom: var(--theme-spacing-xs, 4px);
  }

  .input-group__label--required::after {
    content: " *";
    color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
  }

  .input-group__help {
    font-size: 0.75rem;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    margin-top: var(--theme-spacing-xs, 4px);
  }

  .input-group__error {
    font-size: 0.75rem;
    color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    margin-top: var(--theme-spacing-xs, 4px);
  }

  .input-group__success {
    font-size: 0.75rem;
    color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    margin-top: var(--theme-spacing-xs, 4px);
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .input,
    .textarea,
    .select {
      font-size: 1rem; /* Prevent zoom on iOS */
      padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    }

    .input--small,
    .textarea--small,
    .select--small {
      font-size: 0.875rem;
      padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    }

    .input--large,
    .textarea--large,
    .select--large {
      font-size: 1.125rem;
      padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .input,
    .textarea,
    .select {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .input,
    .textarea,
    .select {
      border-width: 2px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure core UI elements respect user theme selection over system preferences */

  /* Editor element */
  html.theme-light #editor {
    background-color: var(--theme-color-background, #ffffff) !important;
    color: var(--theme-color-text, #1a202c) !important;
  }

  html.theme-dark #editor {
    background-color: var(--theme-color-background, #334155) !important;
    color: var(--theme-color-text, #f9fafb) !important;
  }

  /* Toolbar element */
  html.theme-light #toolbar {
    background-color: var(--theme-color-surface, #f9fafb) !important;
    border-color: var(--theme-color-border, #e5e7eb) !important;
  }

  html.theme-dark #toolbar {
    background-color: var(--theme-color-surface, #1e293b) !important;
    border-color: var(--theme-color-border, #334155) !important;
  }

  /* Body element */
  html.theme-light body {
    background-color: var(--theme-color-background, #f3f4f6) !important;
    color: var(--theme-color-text, #1f2937) !important;
  }

  html.theme-dark body {
    background-color: var(--theme-color-background, #111827) !important;
    color: var(--theme-color-text, #f3f4f6) !important;
  }

  /* Select elements */
  html.theme-light select {
    background-color: var(--theme-color-background, #ffffff) !important;
    color: var(--theme-color-text, #1a202c) !important;
    border-color: var(--theme-color-border, #d1d5db) !important;
  }

  html.theme-dark select {
    background-color: var(--theme-color-background, #334155) !important;
    color: var(--theme-color-text, #f9fafb) !important;
    border-color: var(--theme-color-border, #475569) !important;
  }

  /* Core theme enforcement */
  html.theme-light {
    color-scheme: light;
  }

  html.theme-dark {
    color-scheme: dark;
  }

  /* Tailwind dark mode overrides */
  @media (prefers-color-scheme: dark) {
    html:not(.theme-light) .dark\:bg-slate-700 {
      background-color: rgba(51, 65, 85, var(--tw-bg-opacity, 1)) !important;
    }

    html.theme-light .dark\:bg-slate-700 {
      background-color: transparent !important;
    }

    html.theme-light .dark\:text-slate-200,
    html.theme-light .dark\:text-slate-100,
    html.theme-light .dark\:text-gray-100,
    html.theme-light .dark\:text-gray-50 {
      color: inherit !important;
    }

    html.theme-light .dark\:border-slate-700,
    html.theme-light .dark\:border-slate-600 {
      border-color: inherit !important;
    }
  }

  @media (prefers-color-scheme: light) {
    html.theme-dark .dark\:bg-slate-700 {
      background-color: rgba(51, 65, 85, var(--tw-bg-opacity, 1)) !important;
    }

    html.theme-dark .dark\:text-slate-200,
    html.theme-dark .dark\:text-slate-100,
    html.theme-dark .dark\:text-gray-100,
    html.theme-dark .dark\:text-gray-50 {
      color: white !important;
    }

    html.theme-dark .dark\:border-slate-700,
    html.theme-dark .dark\:border-slate-600 {
      border-color: rgba(51, 65, 85, var(--tw-border-opacity, 1)) !important;
    }
  }
}
