/*
 * Code Component Styles
 *
 * Code block and inline code styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 *
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     INLINE CODE STYLES
     Small code snippets within text
     ========================================================================== */

  .code-inline {
    font-family: var(--theme-font-family-mono, monospace);
    font-size: 0.875em;
    background-color: var(--theme-plugin-code-background, var(--theme-plugin-code-background-fallback, #f5f5f5));
    color: var(--theme-plugin-code-text, var(--theme-plugin-code-text-fallback, #000000));
    border: 1px solid var(--theme-plugin-code-border, var(--theme-plugin-code-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-sm, 3px);
    padding: 0.125em 0.375em;
    white-space: nowrap;
    vertical-align: baseline;
  }

  /* ==========================================================================
     CODE BLOCK BASE STYLES
     Multi-line code blocks with syntax highlighting support
     ========================================================================== */

  .code-block {
    font-family: var(--theme-font-family-mono, monospace);
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: var(--theme-plugin-code-background, var(--theme-plugin-code-background-fallback, #f5f5f5));
    color: var(--theme-plugin-code-text, var(--theme-plugin-code-text-fallback, #000000));
    border: 1px solid var(--theme-plugin-code-border, var(--theme-plugin-code-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    padding: var(--theme-spacing-md, 16px);
    margin: var(--theme-spacing-md, 16px) 0;
    overflow-x: auto;
    position: relative;
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  .code-block__content {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
    font: inherit;
    color: inherit;
    white-space: pre;
    overflow-x: auto;
    display: block;
  }

  /* Custom scrollbar for code blocks */
  .code-block::-webkit-scrollbar,
  .code-block__content::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .code-block::-webkit-scrollbar-track,
  .code-block__content::-webkit-scrollbar-track {
    background: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .code-block::-webkit-scrollbar-thumb,
  .code-block__content::-webkit-scrollbar-thumb {
    background: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    border-radius: var(--theme-border-radius-sm, 3px);
  }

  .code-block::-webkit-scrollbar-thumb:hover,
  .code-block__content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  /* ==========================================================================
     CODE BLOCK HEADER
     Title, language indicator, and action buttons
     ========================================================================== */

  .code-block--with-header {
    padding-top: 0;
  }

  .code-block__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px);
    margin: 0 calc(-1 * var(--theme-spacing-md, 16px)) var(--theme-spacing-sm, 8px);
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border-bottom: 1px solid var(--theme-plugin-code-border, var(--theme-plugin-code-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px) var(--theme-border-radius-md, 6px) 0 0;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .code-block__title {
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    margin: 0;
    font-size: inherit;
    font-weight: inherit;
  }

  .code-block__language {
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.625rem;
    font-weight: 600;
  }

  .code-block__actions {
    display: flex;
    align-items: center;
    gap: var(--theme-spacing-xs, 4px);
  }

  .code-block__copy-button {
    background: transparent;
    border: 1px solid var(--theme-plugin-code-border, var(--theme-plugin-code-border-fallback, #cccccc));
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    border-radius: var(--theme-border-radius-sm, 3px);
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    font-size: 0.625rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .code-block__copy-button:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
    color: var(--theme-hover-text, var(--theme-hover-text-fallback, #000000));
  }

  .code-block__copy-button:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
  }

  .code-block__copy-button--copied {
    background-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    border-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000));
    color: white;
  }

  /* ==========================================================================
     SYNTAX HIGHLIGHTING
     Basic syntax highlighting using theme variables
     ========================================================================== */

  .code-block .token.comment,
  .code-block .token.prolog,
  .code-block .token.doctype,
  .code-block .token.cdata {
    color: var(--theme-plugin-code-comment, #6a737d);
    font-style: italic;
  }

  .code-block .token.punctuation {
    color: var(--theme-plugin-code-text, var(--theme-plugin-code-text-fallback, #000000));
  }

  .code-block .token.property,
  .code-block .token.tag,
  .code-block .token.boolean,
  .code-block .token.number,
  .code-block .token.constant,
  .code-block .token.symbol,
  .code-block .token.deleted {
    color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
  }

  .code-block .token.selector,
  .code-block .token.attr-name,
  .code-block .token.string,
  .code-block .token.char,
  .code-block .token.builtin,
  .code-block .token.inserted {
    color: var(--theme-plugin-code-string, #032f62);
  }

  .code-block .token.operator,
  .code-block .token.entity,
  .code-block .token.url,
  .code-block .language-css .token.string,
  .code-block .style .token.string {
    color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
  }

  .code-block .token.atrule,
  .code-block .token.attr-value,
  .code-block .token.keyword {
    color: var(--theme-plugin-code-keyword, #d73a49);
    font-weight: 600;
  }

  .code-block .token.function,
  .code-block .token.class-name {
    color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    font-weight: 500;
  }

  .code-block .token.regex,
  .code-block .token.important,
  .code-block .token.variable {
    color: var(--theme-color-info, var(--theme-color-info-fallback, #0099cc));
  }

  /* ==========================================================================
     CODE BLOCK VARIANTS
     Different code block styles for various use cases
     ========================================================================== */

  .code-block--compact {
    padding: var(--theme-spacing-sm, 8px);
    margin: var(--theme-spacing-sm, 8px) 0;
    font-size: 0.75rem;
  }

  .code-block--compact .code-block__header {
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    margin: 0 calc(-1 * var(--theme-spacing-sm, 8px)) var(--theme-spacing-xs, 4px);
  }

  .code-block--large {
    padding: var(--theme-spacing-lg, 24px);
    margin: var(--theme-spacing-lg, 24px) 0;
    font-size: 1rem;
  }

  .code-block--large .code-block__header {
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-lg, 24px);
    margin: 0 calc(-1 * var(--theme-spacing-lg, 24px)) var(--theme-spacing-md, 16px);
  }

  .code-block--borderless {
    border: none;
    box-shadow: none;
  }

  .code-block--rounded {
    border-radius: var(--theme-border-radius-lg, 8px);
  }

  .code-block--rounded .code-block__header {
    border-radius: var(--theme-border-radius-lg, 8px) var(--theme-border-radius-lg, 8px) 0 0;
  }

  /* ==========================================================================
     LINE NUMBERS
     Optional line numbering for code blocks
     ========================================================================== */

  .code-block--with-line-numbers {
    padding-left: 0;
  }

  .code-block__line-numbers {
    float: left;
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border-right: 1px solid var(--theme-plugin-code-border, var(--theme-plugin-code-border-fallback, #cccccc));
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-sm, 8px);
    margin: calc(-1 * var(--theme-spacing-md, 16px)) 0 calc(-1 * var(--theme-spacing-md, 16px)) calc(-1 * var(--theme-spacing-md, 16px));
    text-align: right;
    user-select: none;
    font-size: 0.75rem;
    line-height: 1.5;
    min-width: 2.5em;
  }

  .code-block__content--with-line-numbers {
    margin-left: calc(2.5em + var(--theme-spacing-md, 16px));
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .code-block {
      font-size: 0.75rem;
      padding: var(--theme-spacing-sm, 8px);
      margin: var(--theme-spacing-sm, 8px) 0;
      border-radius: var(--theme-border-radius-sm, 3px);
    }

    .code-block__header {
      padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
      margin: 0 calc(-1 * var(--theme-spacing-sm, 8px)) var(--theme-spacing-xs, 4px);
      flex-direction: column;
      align-items: flex-start;
      gap: var(--theme-spacing-xs, 4px);
    }

    .code-block__actions {
      align-self: flex-end;
    }

    .code-block__line-numbers {
      padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-xs, 4px);
      margin: calc(-1 * var(--theme-spacing-sm, 8px)) 0 calc(-1 * var(--theme-spacing-sm, 8px)) calc(-1 * var(--theme-spacing-sm, 8px));
      min-width: 2em;
    }

    .code-block__content--with-line-numbers {
      margin-left: calc(2em + var(--theme-spacing-sm, 8px));
    }

    .code-inline {
      font-size: 0.75em;
      padding: 0.125em 0.25em;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .code-block__copy-button {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .code-block,
    .code-inline {
      border-width: 2px;
    }

    .code-block__header {
      border-bottom-width: 2px;
    }

    .code-block__line-numbers {
      border-right-width: 2px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure code block elements respect user theme selection over system preferences */
  html.theme-light .feather-code-block {
    background-color: var(--theme-plugin-code-background, #f3f4f6) !important;
    border-color: var(--theme-plugin-code-border, #d1d5db) !important;
    color: var(--theme-plugin-code-text, #1a202c) !important;
  }

  html.theme-dark .feather-code-block {
    background-color: var(--theme-plugin-code-background, #1e293b) !important;
    border-color: var(--theme-plugin-code-border, #334155) !important;
    color: var(--theme-plugin-code-text, #e2e8f0) !important;
  }
}
