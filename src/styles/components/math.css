/*
 * Math Component Styles
 * 
 * Math expression component styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 * 
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     MATH WRAPPER BASE STYLES
     Core math expression container styling
     ========================================================================== */

  .math-inline-wrapper,
  .math-display-wrapper {
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-sm, 3px);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    font-family: var(--theme-font-family, sans-serif);
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  /* ==========================================================================
     INLINE MATH EXPRESSIONS
     Math expressions within text flow
     ========================================================================== */

  .math-inline-wrapper {
    display: inline-flex;
    align-items: center;
    padding: 0.125em 0.375em;
    margin: 0 0.125em;
    vertical-align: baseline;
    font-size: 0.9em;
    line-height: 1;
  }

  .math-inline-wrapper:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
  }

  /* ==========================================================================
     DISPLAY MATH EXPRESSIONS
     Block-level math expressions
     ========================================================================== */

  .math-display-wrapper {
    display: block;
    padding: var(--theme-spacing-md, 16px);
    margin: var(--theme-spacing-md, 16px) 0;
    text-align: center;
    box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  }

  .math-display-wrapper:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc));
  }

  /* ==========================================================================
     KATEX CONTAINER STYLES
     KaTeX-specific styling overrides
     ========================================================================== */

  .katex-container {
    background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-sm, 3px);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    padding: var(--theme-spacing-sm, 8px);
    margin: var(--theme-spacing-sm, 8px) 0;
    overflow-x: auto;
  }

  .katex-container .katex {
    color: inherit;
  }

  .katex-container .katex-display {
    margin: 0;
    text-align: center;
  }

  /* ==========================================================================
     MATH EXPRESSION VARIANTS
     Different math expression styles
     ========================================================================== */

  /* Compact math expressions */
  .math-inline-wrapper--compact,
  .math-display-wrapper--compact {
    padding: 0.0625em 0.25em;
    font-size: 0.875em;
  }

  .math-display-wrapper--compact {
    padding: var(--theme-spacing-sm, 8px);
    margin: var(--theme-spacing-sm, 8px) 0;
  }

  /* Large math expressions */
  .math-display-wrapper--large {
    padding: var(--theme-spacing-lg, 24px);
    margin: var(--theme-spacing-lg, 24px) 0;
    font-size: 1.125em;
  }

  /* Borderless math expressions */
  .math-inline-wrapper--borderless,
  .math-display-wrapper--borderless,
  .katex-container--borderless {
    border: none;
    background-color: transparent;
    box-shadow: none;
  }

  /* Highlighted math expressions */
  .math-inline-wrapper--highlighted,
  .math-display-wrapper--highlighted,
  .katex-container--highlighted {
    background-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    color: white;
    border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
  }

  .math-inline-wrapper--highlighted:hover,
  .math-display-wrapper--highlighted:hover {
    background-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc));
    filter: brightness(1.1);
  }

  /* ==========================================================================
     MATH EXPRESSION STATES
     Different states for math expressions
     ========================================================================== */

  .math-inline-wrapper--error,
  .math-display-wrapper--error,
  .katex-container--error {
    background-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
    color: white;
    border-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
  }

  .math-inline-wrapper--loading,
  .math-display-wrapper--loading,
  .katex-container--loading {
    background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    color: white;
    border-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    opacity: 0.7;
  }

  /* ==========================================================================
     MATH EXPRESSION ACCESSIBILITY
     Screen reader and keyboard navigation support
     ========================================================================== */

  .math-inline-wrapper[tabindex],
  .math-display-wrapper[tabindex],
  .katex-container[tabindex] {
    cursor: pointer;
  }

  .math-inline-wrapper:focus,
  .math-display-wrapper:focus,
  .katex-container:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .math-display-wrapper {
      padding: var(--theme-spacing-sm, 8px);
      margin: var(--theme-spacing-sm, 8px) 0;
      font-size: 0.875rem;
    }

    .math-inline-wrapper {
      font-size: 0.8em;
      padding: 0.0625em 0.25em;
    }

    .katex-container {
      padding: var(--theme-spacing-xs, 4px);
      margin: var(--theme-spacing-xs, 4px) 0;
      font-size: 0.875rem;
    }

    .math-display-wrapper--large {
      padding: var(--theme-spacing-md, 16px);
      margin: var(--theme-spacing-md, 16px) 0;
      font-size: 1rem;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .math-inline-wrapper,
    .math-display-wrapper,
    .katex-container {
      transition: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .math-inline-wrapper,
    .math-display-wrapper,
    .katex-container {
      border-width: 2px;
    }
  }
}

/* ==========================================================================
   THEME OVERRIDE LAYER
   Media query overrides to ensure user theme selection takes precedence
   ========================================================================== */

@layer overrides {
  /* Ensure math elements respect user theme selection over system preferences */
  html.theme-light .math-inline-wrapper,
  html.theme-light .math-display-wrapper,
  html.theme-light .katex-container {
    background-color: var(--theme-color-surface, #f9fafb) !important;
    border-color: var(--theme-color-border, #e5e7eb) !important;
    color: var(--theme-color-text, #1a202c) !important;
  }

  html.theme-dark .math-inline-wrapper,
  html.theme-dark .math-display-wrapper,
  html.theme-dark .katex-container {
    background-color: var(--theme-color-surface, #1e293b) !important;
    border-color: var(--theme-color-border, #334155) !important;
    color: var(--theme-color-text, #e2e8f0) !important;
  }

  /* Override any media query styles for math elements */
  @media (prefers-color-scheme: light) {
    html.theme-dark .math-inline-wrapper,
    html.theme-dark .math-display-wrapper,
    html.theme-dark .katex-container {
      background-color: var(--theme-color-surface, #1e293b) !important;
      border-color: var(--theme-color-border, #334155) !important;
      color: var(--theme-color-text, #e2e8f0) !important;
    }
  }

  @media (prefers-color-scheme: dark) {
    html.theme-light .math-inline-wrapper,
    html.theme-light .math-display-wrapper,
    html.theme-light .katex-container {
      background-color: var(--theme-color-surface, #f9fafb) !important;
      border-color: var(--theme-color-border, #e5e7eb) !important;
      color: var(--theme-color-text, #1a202c) !important;
    }
  }
}
