/*
 * Overlay Component Styles
 * 
 * Overlay, tooltip, and popover styling using semantic theme variables.
 * Follows BEM naming convention for maintainable and reusable components.
 * Theme-agnostic design that adapts to light and dark themes automatically.
 * 
 * Uses the 'components' layer for proper cascade hierarchy.
 */

@layer components {
  /* ==========================================================================
     OVERLAY BASE STYLES
     Core overlay container and backdrop styling
     ========================================================================== */

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(1px);
    z-index: var(--theme-z-index-modal-backdrop, 1040);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--theme-spacing-md, 16px);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                visibility var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  .overlay--active {
    opacity: 1;
    visibility: visible;
  }

  .overlay--light {
    background-color: rgba(255, 255, 255, 0.8);
  }

  .overlay--dark {
    background-color: rgba(0, 0, 0, 0.6);
  }

  /* ==========================================================================
     TOOLTIP STYLES
     Small contextual information overlays
     ========================================================================== */

  .tooltip {
    position: absolute;
    background-color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    padding: var(--theme-spacing-xs, 4px) var(--theme-spacing-sm, 8px);
    border-radius: var(--theme-border-radius-sm, 3px);
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1.2;
    white-space: nowrap;
    z-index: var(--theme-z-index-tooltip, 1070);
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95);
    transition: opacity var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out),
                visibility var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out),
                transform var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
    max-width: 200px;
    word-wrap: break-word;
    white-space: normal;
  }

  .tooltip--active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }

  /* Tooltip arrow */
  .tooltip::before {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border: 4px solid transparent;
  }

  /* Tooltip positioning variants */
  .tooltip--top {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) scale(0.95);
    margin-bottom: var(--theme-spacing-xs, 4px);
  }

  .tooltip--top.tooltip--active {
    transform: translateX(-50%) scale(1);
  }

  .tooltip--top::before {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  .tooltip--bottom {
    top: 100%;
    left: 50%;
    transform: translateX(-50%) scale(0.95);
    margin-top: var(--theme-spacing-xs, 4px);
  }

  .tooltip--bottom.tooltip--active {
    transform: translateX(-50%) scale(1);
  }

  .tooltip--bottom::before {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  .tooltip--left {
    right: 100%;
    top: 50%;
    transform: translateY(-50%) scale(0.95);
    margin-right: var(--theme-spacing-xs, 4px);
  }

  .tooltip--left.tooltip--active {
    transform: translateY(-50%) scale(1);
  }

  .tooltip--left::before {
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-left-color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  .tooltip--right {
    left: 100%;
    top: 50%;
    transform: translateY(-50%) scale(0.95);
    margin-left: var(--theme-spacing-xs, 4px);
  }

  .tooltip--right.tooltip--active {
    transform: translateY(-50%) scale(1);
  }

  .tooltip--right::before {
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-right-color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
  }

  /* ==========================================================================
     POPOVER STYLES
     Larger contextual content overlays
     ========================================================================== */

  .popover {
    position: absolute;
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    box-shadow: var(--theme-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05));
    z-index: var(--theme-z-index-popover, 1060);
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95);
    transition: opacity var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                visibility var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                transform var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
    max-width: 320px;
    min-width: 200px;
  }

  .popover--active {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
  }

  .popover__header {
    padding: var(--theme-spacing-md, 16px) var(--theme-spacing-md, 16px) var(--theme-spacing-sm, 8px);
    border-bottom: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .popover__title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    margin: 0;
  }

  .popover__close-button {
    background: transparent;
    border: none;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    cursor: pointer;
    padding: var(--theme-spacing-xs, 4px);
    border-radius: var(--theme-border-radius-sm, 3px);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
    font-size: 1rem;
    line-height: 1;
    width: 24px;
    height: 24px;
  }

  .popover__close-button:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    color: var(--theme-hover-text, var(--theme-hover-text-fallback, #000000));
  }

  .popover__close-button:focus {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
  }

  .popover__content {
    padding: var(--theme-spacing-md, 16px);
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    line-height: var(--theme-line-height-base, 1.5);
  }

  .popover__footer {
    padding: var(--theme-spacing-sm, 8px) var(--theme-spacing-md, 16px) var(--theme-spacing-md, 16px);
    border-top: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--theme-spacing-sm, 8px);
  }

  /* Popover arrow */
  .popover::before {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border: 8px solid transparent;
  }

  .popover::after {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border: 7px solid transparent;
  }

  /* Popover positioning variants */
  .popover--top {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) scale(0.95);
    margin-bottom: var(--theme-spacing-sm, 8px);
  }

  .popover--top.popover--active {
    transform: translateX(-50%) scale(1);
  }

  .popover--top::before {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  .popover--top::after {
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-top-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    margin-top: -1px;
  }

  .popover--bottom {
    top: 100%;
    left: 50%;
    transform: translateX(-50%) scale(0.95);
    margin-top: var(--theme-spacing-sm, 8px);
  }

  .popover--bottom.popover--active {
    transform: translateX(-50%) scale(1);
  }

  .popover--bottom::before {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
  }

  .popover--bottom::after {
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    margin-bottom: -1px;
  }

  /* ==========================================================================
     NOTIFICATION STYLES
     Toast and alert overlays
     ========================================================================== */

  .notification {
    position: fixed;
    top: var(--theme-spacing-md, 16px);
    right: var(--theme-spacing-md, 16px);
    background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff));
    border: 1px solid var(--theme-color-border, var(--theme-color-border-fallback, #cccccc));
    border-radius: var(--theme-border-radius-md, 6px);
    box-shadow: var(--theme-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05));
    padding: var(--theme-spacing-md, 16px);
    z-index: var(--theme-z-index-tooltip, 1070);
    max-width: 400px;
    min-width: 300px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(100%);
    transition: opacity var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                visibility var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                transform var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  .notification--active {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
  }

  .notification__content {
    display: flex;
    align-items: flex-start;
    gap: var(--theme-spacing-sm, 8px);
  }

  .notification__icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
  }

  .notification__message {
    flex-grow: 1;
    color: var(--theme-color-text, var(--theme-color-text-fallback, #000000));
    line-height: var(--theme-line-height-base, 1.5);
  }

  .notification__close-button {
    background: transparent;
    border: none;
    color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666));
    cursor: pointer;
    padding: 0;
    margin-left: var(--theme-spacing-sm, 8px);
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--theme-border-radius-sm, 3px);
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .notification__close-button:hover {
    background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0));
    color: var(--theme-hover-text, var(--theme-hover-text-fallback, #000000));
  }

  /* Notification variants */
  .notification--success {
    border-left: 4px solid var(--theme-color-success, var(--theme-color-success-fallback, #008000));
  }

  .notification--warning {
    border-left: 4px solid var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00));
  }

  .notification--error {
    border-left: 4px solid var(--theme-color-error, var(--theme-color-error-fallback, #cc0000));
  }

  .notification--info {
    border-left: 4px solid var(--theme-color-info, var(--theme-color-info-fallback, #0099cc));
  }

  /* ==========================================================================
     RESPONSIVE DESIGN
     Mobile and tablet adaptations
     ========================================================================== */

  @media (max-width: 768px) {
    .overlay {
      padding: var(--theme-spacing-sm, 8px);
    }

    .popover {
      max-width: calc(100vw - var(--theme-spacing-md, 16px));
      min-width: auto;
    }

    .popover__header,
    .popover__content,
    .popover__footer {
      padding: var(--theme-spacing-sm, 8px);
    }

    .notification {
      top: var(--theme-spacing-sm, 8px);
      right: var(--theme-spacing-sm, 8px);
      left: var(--theme-spacing-sm, 8px);
      max-width: none;
      min-width: auto;
      transform: translateY(-100%);
    }

    .notification--active {
      transform: translateY(0);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .overlay,
    .tooltip,
    .popover,
    .notification,
    .popover__close-button,
    .notification__close-button {
      transition: none;
    }

    .tooltip,
    .popover,
    .notification {
      transform: none;
    }

    .tooltip--active,
    .popover--active,
    .notification--active {
      transform: none;
    }
  }
}
