/**
 * FeatherJS Theme Variables
 * Semantic CSS Variable System for Dynamic Theming
 *
 * This file defines all theme-aware CSS custom properties organized by category.
 * Variables follow the --theme-* naming convention for consistency and clarity.
 *
 * Categories:
 * - Core: Fundamental colors (text, background, primary, etc.)
 * - State: Interactive state colors (hover, focus, active, disabled)
 * - Component: UI component specific colors (editor, toolbar, etc.)
 * - Plugin: Plugin-specific color schemes (palette, chart, code, etc.)
 *
 * Each variable includes fallback values for graceful degradation.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

@layer theme-variables {
  /* ==========================================================================
     CORE THEME VARIABLES
     Fundamental colors that form the foundation of the theme system
     ========================================================================== */

  :root {
    /* Core Colors - Light Theme Defaults */
    --theme-color-text: #333333;
    --theme-color-background: #ffffff;
    --theme-color-surface: #f8f9fa;
    --theme-color-border: #e0e0e0;
    --theme-color-primary: #1565c0;
    --theme-color-secondary: #6c757d;
    --theme-color-success: #28a745;
    --theme-color-warning: #ffc107;
    --theme-color-error: #dc3545;
    --theme-color-info: #17a2b8;

    /* Core Color Fallbacks */
    --theme-color-text-fallback: #000000;
    --theme-color-background-fallback: #ffffff;
    --theme-color-surface-fallback: #f5f5f5;
    --theme-color-border-fallback: #cccccc;
    --theme-color-primary-fallback: #0066cc;
    --theme-color-secondary-fallback: #666666;
    --theme-color-success-fallback: #008000;
    --theme-color-warning-fallback: #ff8c00;
    --theme-color-error-fallback: #cc0000;
    --theme-color-info-fallback: #0099cc;
  }

  /* Light Theme Core Colors */
  :root.theme-light,
  html.theme-light,
  .theme-light {
    --theme-color-text: #333333;
    --theme-color-background: #ffffff;
    --theme-color-surface: #f8f9fa;
    --theme-color-border: #e0e0e0;
    --theme-color-primary: #1565c0;
    --theme-color-secondary: #6c757d;
    --theme-color-success: #28a745;
    --theme-color-warning: #ffc107;
    --theme-color-error: #dc3545;
    --theme-color-info: #17a2b8;
  }

  /* Dark Theme Core Colors */
  :root.theme-dark,
  html.theme-dark,
  .theme-dark {
    --theme-color-text: #f0f0f0;
    --theme-color-background: #1a1a1a;
    --theme-color-surface: #2d2d2d;
    --theme-color-border: #444444;
    --theme-color-primary: #4fc3f7;
    --theme-color-secondary: #6c757d;
    --theme-color-success: #28a745;
    --theme-color-warning: #ffc107;
    --theme-color-error: #dc3545;
    --theme-color-info: #17a2b8;
  }

  /* ==========================================================================
     STATE THEME VARIABLES
     Interactive state colors for hover, focus, active, and disabled states
     ========================================================================== */

  :root {
    /* State Colors - Light Theme Defaults */
    --theme-hover-background: #f8f9fa;
    --theme-hover-border: #e0e0e0;
    --theme-hover-text: #333333;

    --theme-focus-background: #ffffff;
    --theme-focus-border: #1565c0;
    --theme-focus-ring: rgba(21, 101, 192, 0.25);

    --theme-active-background: #e9ecef;
    --theme-active-border: #dee2e6;
    --theme-active-text: #333333;

    --theme-disabled-background: #f8f9fa;
    --theme-disabled-border: #e9ecef;
    --theme-disabled-text: #6c757d;

    /* State Color Fallbacks */
    --theme-hover-background-fallback: #f0f0f0;
    --theme-hover-border-fallback: #cccccc;
    --theme-hover-text-fallback: #000000;
    --theme-focus-background-fallback: #ffffff;
    --theme-focus-border-fallback: #0066cc;
    --theme-focus-ring-fallback: rgba(0, 102, 204, 0.25);
    --theme-active-background-fallback: #e0e0e0;
    --theme-active-border-fallback: #cccccc;
    --theme-active-text-fallback: #000000;
    --theme-disabled-background-fallback: #f0f0f0;
    --theme-disabled-border-fallback: #e0e0e0;
    --theme-disabled-text-fallback: #999999;
  }

  /* Light Theme State Colors */
  :root.theme-light,
  html.theme-light,
  .theme-light {
    --theme-hover-background: #f8f9fa;
    --theme-hover-border: #e0e0e0;
    --theme-hover-text: #333333;

    --theme-focus-background: #ffffff;
    --theme-focus-border: #1565c0;
    --theme-focus-ring: rgba(21, 101, 192, 0.25);

    --theme-active-background: #e9ecef;
    --theme-active-border: #dee2e6;
    --theme-active-text: #333333;

    --theme-disabled-background: #f8f9fa;
    --theme-disabled-border: #e9ecef;
    --theme-disabled-text: #6c757d;
  }

  /* Dark Theme State Colors */
  :root.theme-dark,
  html.theme-dark,
  .theme-dark {
    --theme-hover-background: #3a3a3a;
    --theme-hover-border: #555555;
    --theme-hover-text: #ffffff;

    --theme-focus-background: #2d2d2d;
    --theme-focus-border: #4fc3f7;
    --theme-focus-ring: rgba(79, 195, 247, 0.25);

    --theme-active-background: #404040;
    --theme-active-border: #666666;
    --theme-active-text: #ffffff;

    --theme-disabled-background: #2d2d2d;
    --theme-disabled-border: #3a3a3a;
    --theme-disabled-text: #6c757d;
  }

  /* ==========================================================================
     COMPONENT THEME VARIABLES
     UI component specific colors for editor, toolbar, accessibility, etc.
     ========================================================================== */

  :root {
    /* Component Colors - Light Theme Defaults */

    /* Editor Core Components */
    --theme-component-editor-background: #ffffff;
    --theme-component-editor-text: #1f2937;
    --theme-component-editor-placeholder: #9ca3af;
    --theme-component-editor-border: #e5e7eb;
    --theme-component-editor-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --theme-component-editor-selection-background: rgba(59, 130, 246, 0.2);
    --theme-component-editor-selection-text: inherit;
    --theme-component-editor-cursor: #1f2937;
    --theme-component-editor-highlight-background: rgba(251, 191, 36, 0.3);

    /* Toolbar Components */
    --theme-component-toolbar-background: #f9fafb;
    --theme-component-toolbar-border: #e5e7eb;
    --theme-component-toolbar-button-background: transparent;
    --theme-component-toolbar-button-text: #374151;
    --theme-component-toolbar-button-hover-background: #f3f4f6;
    --theme-component-toolbar-button-active-background: #e5e7eb;
    --theme-component-toolbar-button-active-text: #1f2937;
    --theme-component-toolbar-button-border: #d1d5db;

    /* Accessibility Components */
    --theme-component-focus-ring-color: #3b82f6;
    --theme-component-focus-ring-offset: 2px;
    --theme-component-focus-ring-width: 2px;
    --theme-component-focus-ring-opacity: 1;
    --theme-component-focus-outline-color: transparent;

    /* Keyboard Feedback Components */
    --theme-component-keyboard-feedback-background: #ffffff;
    --theme-component-keyboard-feedback-text: #333333;
    --theme-component-keyboard-feedback-border: #e0e0e0;
    --theme-component-keyboard-feedback-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* Clipboard Feedback Components */
    --theme-component-clipboard-feedback-background: #ffffff;
    --theme-component-clipboard-feedback-text: #333333;
    --theme-component-clipboard-feedback-border: #e0e0e0;
    --theme-component-clipboard-feedback-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    /* History Components */
    --theme-component-history-button-background: transparent;
    --theme-component-history-button-text: #374151;
    --theme-component-history-button-hover-background: #f3f4f6;
    --theme-component-history-button-active-background: #e5e7eb;
    --theme-component-history-button-disabled-background: transparent;
    --theme-component-history-button-disabled-text: #9ca3af;
    --theme-component-history-indicator-background: #ffffff;
    --theme-component-history-indicator-border: #e5e7eb;
    --theme-component-history-indicator-text: #6b7280;
    --theme-component-history-change-highlight: rgba(59, 130, 246, 0.1);

    /* Performance Components */
    --theme-component-viewport-line-background: transparent;
    --theme-component-viewport-line-text: inherit;
    --theme-component-viewport-line-border: #e5e7eb;
    --theme-component-performance-debug-background: rgba(255, 255, 255, 0.95);
    --theme-component-performance-debug-border: #e5e7eb;
    --theme-component-performance-debug-text: #374151;
    --theme-component-performance-metric-good: #10b981;
    --theme-component-performance-metric-warning: #f59e0b;
    --theme-component-performance-metric-error: #ef4444;

    /* Component Color Fallbacks */
    --theme-component-editor-background-fallback: #ffffff;
    --theme-component-editor-text-fallback: #000000;
    --theme-component-toolbar-background-fallback: #f5f5f5;
    --theme-component-toolbar-border-fallback: #cccccc;
    --theme-component-focus-ring-color-fallback: #0066cc;
    --theme-component-keyboard-feedback-background-fallback: #ffffff;
    --theme-component-clipboard-feedback-background-fallback: #ffffff;
    --theme-component-history-button-background-fallback: transparent;
    --theme-component-performance-debug-background-fallback: rgba(255, 255, 255, 0.9);
  }

  /* Light Theme Component Colors */
  :root.theme-light,
  html.theme-light,
  .theme-light {
    /* Editor Core Components */
    --theme-component-editor-background: #ffffff;
    --theme-component-editor-text: #1f2937;
    --theme-component-editor-placeholder: #9ca3af;
    --theme-component-editor-border: #e5e7eb;
    --theme-component-editor-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --theme-component-editor-selection-background: rgba(59, 130, 246, 0.2);
    --theme-component-editor-selection-text: inherit;
    --theme-component-editor-cursor: #1f2937;
    --theme-component-editor-highlight-background: rgba(251, 191, 36, 0.3);

    /* Toolbar Components */
    --theme-component-toolbar-background: #f9fafb;
    --theme-component-toolbar-border: #e5e7eb;
    --theme-component-toolbar-button-background: transparent;
    --theme-component-toolbar-button-text: #374151;
    --theme-component-toolbar-button-hover-background: #f3f4f6;
    --theme-component-toolbar-button-active-background: #e5e7eb;
    --theme-component-toolbar-button-active-text: #1f2937;
    --theme-component-toolbar-button-border: #d1d5db;

    /* Accessibility Components */
    --theme-component-focus-ring-color: #3b82f6;
    --theme-component-focus-ring-offset: 2px;
    --theme-component-focus-ring-width: 2px;
    --theme-component-focus-ring-opacity: 1;
    --theme-component-focus-outline-color: transparent;

    /* Keyboard Feedback Components */
    --theme-component-keyboard-feedback-background: #ffffff;
    --theme-component-keyboard-feedback-text: #333333;
    --theme-component-keyboard-feedback-border: #e0e0e0;
    --theme-component-keyboard-feedback-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* Clipboard Feedback Components */
    --theme-component-clipboard-feedback-background: #ffffff;
    --theme-component-clipboard-feedback-text: #333333;
    --theme-component-clipboard-feedback-border: #e0e0e0;
    --theme-component-clipboard-feedback-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    /* History Components */
    --theme-component-history-button-background: transparent;
    --theme-component-history-button-text: #374151;
    --theme-component-history-button-hover-background: #f3f4f6;
    --theme-component-history-button-active-background: #e5e7eb;
    --theme-component-history-button-disabled-background: transparent;
    --theme-component-history-button-disabled-text: #9ca3af;
    --theme-component-history-indicator-background: #ffffff;
    --theme-component-history-indicator-border: #e5e7eb;
    --theme-component-history-indicator-text: #6b7280;
    --theme-component-history-change-highlight: rgba(59, 130, 246, 0.1);

    /* Performance Components */
    --theme-component-viewport-line-background: transparent;
    --theme-component-viewport-line-text: inherit;
    --theme-component-viewport-line-border: #e5e7eb;
    --theme-component-performance-debug-background: rgba(255, 255, 255, 0.95);
    --theme-component-performance-debug-border: #e5e7eb;
    --theme-component-performance-debug-text: #374151;
    --theme-component-performance-metric-good: #10b981;
    --theme-component-performance-metric-warning: #f59e0b;
    --theme-component-performance-metric-error: #ef4444;
  }

  /* Dark Theme Component Colors */
  :root.theme-dark,
  html.theme-dark,
  .theme-dark {
    /* Editor Core Components */
    --theme-component-editor-background: #334155;
    --theme-component-editor-text: #f1f5f9;
    --theme-component-editor-placeholder: #64748b;
    --theme-component-editor-border: #475569;
    --theme-component-editor-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --theme-component-editor-selection-background: rgba(96, 165, 250, 0.3);
    --theme-component-editor-selection-text: inherit;
    --theme-component-editor-cursor: #f1f5f9;
    --theme-component-editor-highlight-background: rgba(251, 191, 36, 0.4);

    /* Toolbar Components */
    --theme-component-toolbar-background: #1e293b;
    --theme-component-toolbar-border: #475569;
    --theme-component-toolbar-button-background: transparent;
    --theme-component-toolbar-button-text: #cbd5e1;
    --theme-component-toolbar-button-hover-background: #334155;
    --theme-component-toolbar-button-active-background: #475569;
    --theme-component-toolbar-button-active-text: #f1f5f9;
    --theme-component-toolbar-button-border: #64748b;

    /* Accessibility Components */
    --theme-component-focus-ring-color: #60a5fa;
    --theme-component-focus-ring-offset: 2px;
    --theme-component-focus-ring-width: 2px;
    --theme-component-focus-ring-opacity: 1;
    --theme-component-focus-outline-color: transparent;

    /* Keyboard Feedback Components */
    --theme-component-keyboard-feedback-background: #2d2d2d;
    --theme-component-keyboard-feedback-text: #f0f0f0;
    --theme-component-keyboard-feedback-border: #444444;
    --theme-component-keyboard-feedback-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    /* Clipboard Feedback Components */
    --theme-component-clipboard-feedback-background: #2d2d2d;
    --theme-component-clipboard-feedback-text: #f0f0f0;
    --theme-component-clipboard-feedback-border: #444444;
    --theme-component-clipboard-feedback-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);

    /* History Components */
    --theme-component-history-button-background: transparent;
    --theme-component-history-button-text: #cbd5e1;
    --theme-component-history-button-hover-background: #334155;
    --theme-component-history-button-active-background: #475569;
    --theme-component-history-button-disabled-background: transparent;
    --theme-component-history-button-disabled-text: #64748b;
    --theme-component-history-indicator-background: #334155;
    --theme-component-history-indicator-border: #475569;
    --theme-component-history-indicator-text: #94a3b8;
    --theme-component-history-change-highlight: rgba(96, 165, 250, 0.2);

    /* Performance Components */
    --theme-component-viewport-line-background: transparent;
    --theme-component-viewport-line-text: inherit;
    --theme-component-viewport-line-border: #475569;
    --theme-component-performance-debug-background: rgba(30, 41, 59, 0.95);
    --theme-component-performance-debug-border: #475569;
    --theme-component-performance-debug-text: #cbd5e1;
    --theme-component-performance-metric-good: #10b981;
    --theme-component-performance-metric-warning: #f59e0b;
    --theme-component-performance-metric-error: #ef4444;
  }

  /* ==========================================================================
     PLUGIN THEME VARIABLES
     Plugin-specific color schemes for consistent theming across all plugins
     ========================================================================== */

  :root {
    /* Plugin Colors - Light Theme Defaults */

    /* Color Palette Plugin */
    --theme-plugin-palette-background: #ffffff;
    --theme-plugin-palette-border: #e0e0e0;
    --theme-plugin-palette-shadow: rgba(0, 0, 0, 0.1);
    --theme-plugin-palette-tab-active-border: #1565c0;
    --theme-plugin-palette-swatch-border: rgba(0, 0, 0, 0.1);

    /* Chart Plugin */
    --theme-plugin-chart-background: #ffffff;
    --theme-plugin-chart-grid-lines: #e0e0e0;
    --theme-plugin-chart-data-color-1: #1565c0;
    --theme-plugin-chart-data-color-2: #28a745;
    --theme-plugin-chart-data-color-3: #ffc107;
    --theme-plugin-chart-data-color-4: #dc3545;
    --theme-plugin-chart-data-color-5: #17a2b8;
    --theme-plugin-chart-data-color-6: #6f42c1;

    /* Code Block Plugin */
    --theme-plugin-code-background: #f8f9fa;
    --theme-plugin-code-border: #e0e0e0;
    --theme-plugin-code-text: #333333;
    --theme-plugin-code-keyword: #d73a49;
    --theme-plugin-code-string: #032f62;
    --theme-plugin-code-comment: #6a737d;

    /* Table Plugin */
    --theme-plugin-table-background: #ffffff;
    --theme-plugin-table-border: #e0e0e0;
    --theme-plugin-table-header-background: #f8f9fa;
    --theme-plugin-table-alternate-row-background: #f8f9fa;

    /* Comments Plugin */
    --theme-plugin-comments-background: #ffffff;
    --theme-plugin-comments-border: #e0e0e0;
    --theme-plugin-comments-highlight-background: rgba(74, 134, 232, 0.1);
    --theme-plugin-comments-avatar-background: #f8f9fa;

    /* Plugin Color Fallbacks */
    --theme-plugin-palette-background-fallback: #ffffff;
    --theme-plugin-palette-border-fallback: #cccccc;
    --theme-plugin-palette-shadow-fallback: rgba(0, 0, 0, 0.15);
    --theme-plugin-chart-background-fallback: #ffffff;
    --theme-plugin-chart-grid-lines-fallback: #cccccc;
    --theme-plugin-code-background-fallback: #f5f5f5;
    --theme-plugin-code-border-fallback: #cccccc;
    --theme-plugin-code-text-fallback: #000000;
    --theme-plugin-table-background-fallback: #ffffff;
    --theme-plugin-table-border-fallback: #cccccc;
    --theme-plugin-comments-background-fallback: #ffffff;
    --theme-plugin-comments-border-fallback: #cccccc;
  }

  /* Light Theme Plugin Colors */
  :root.theme-light,
  html.theme-light,
  .theme-light {
    /* Color Palette Plugin */
    --theme-plugin-palette-background: #ffffff;
    --theme-plugin-palette-border: #e0e0e0;
    --theme-plugin-palette-shadow: rgba(0, 0, 0, 0.1);
    --theme-plugin-palette-tab-active-border: #1565c0;
    --theme-plugin-palette-swatch-border: rgba(0, 0, 0, 0.1);

    /* Chart Plugin */
    --theme-plugin-chart-background: #ffffff;
    --theme-plugin-chart-grid-lines: #e0e0e0;
    --theme-plugin-chart-data-color-1: #1565c0;
    --theme-plugin-chart-data-color-2: #28a745;
    --theme-plugin-chart-data-color-3: #ffc107;
    --theme-plugin-chart-data-color-4: #dc3545;
    --theme-plugin-chart-data-color-5: #17a2b8;
    --theme-plugin-chart-data-color-6: #6f42c1;

    /* Code Block Plugin */
    --theme-plugin-code-background: #f8f9fa;
    --theme-plugin-code-border: #e0e0e0;
    --theme-plugin-code-text: #333333;
    --theme-plugin-code-keyword: #d73a49;
    --theme-plugin-code-string: #032f62;
    --theme-plugin-code-comment: #6a737d;

    /* Table Plugin */
    --theme-plugin-table-background: #ffffff;
    --theme-plugin-table-border: #e0e0e0;
    --theme-plugin-table-header-background: #f8f9fa;
    --theme-plugin-table-alternate-row-background: #f8f9fa;

    /* Comments Plugin */
    --theme-plugin-comments-background: #ffffff;
    --theme-plugin-comments-border: #e0e0e0;
    --theme-plugin-comments-highlight-background: rgba(74, 134, 232, 0.1);
    --theme-plugin-comments-avatar-background: #f8f9fa;
  }

  /* Dark Theme Plugin Colors */
  :root.theme-dark,
  html.theme-dark,
  .theme-dark {
    /* Color Palette Plugin */
    --theme-plugin-palette-background: #333333;
    --theme-plugin-palette-border: #444444;
    --theme-plugin-palette-shadow: rgba(0, 0, 0, 0.3);
    --theme-plugin-palette-tab-active-border: #4fc3f7;
    --theme-plugin-palette-swatch-border: rgba(255, 255, 255, 0.1);

    /* Chart Plugin */
    --theme-plugin-chart-background: #2d2d2d;
    --theme-plugin-chart-grid-lines: #444444;
    --theme-plugin-chart-data-color-1: #4fc3f7;
    --theme-plugin-chart-data-color-2: #28a745;
    --theme-plugin-chart-data-color-3: #ffc107;
    --theme-plugin-chart-data-color-4: #dc3545;
    --theme-plugin-chart-data-color-5: #17a2b8;
    --theme-plugin-chart-data-color-6: #6f42c1;

    /* Code Block Plugin */
    --theme-plugin-code-background: #2d2d2d;
    --theme-plugin-code-border: #444444;
    --theme-plugin-code-text: #f0f0f0;
    --theme-plugin-code-keyword: #ff7b72;
    --theme-plugin-code-string: #a5d6ff;
    --theme-plugin-code-comment: #8b949e;

    /* Table Plugin */
    --theme-plugin-table-background: #2d2d2d;
    --theme-plugin-table-border: #444444;
    --theme-plugin-table-header-background: #3a3a3a;
    --theme-plugin-table-alternate-row-background: #333333;

    /* Comments Plugin */
    --theme-plugin-comments-background: #2d2d2d;
    --theme-plugin-comments-border: #444444;
    --theme-plugin-comments-highlight-background: rgba(74, 134, 232, 0.2);
    --theme-plugin-comments-avatar-background: #3a3a3a;
  }

  /* ==========================================================================
     ANIMATION AND UTILITY VARIABLES
     Animation timing, easing, and utility values for consistent motion design
     ========================================================================== */

  :root {
    /* Animation Configuration */
    --theme-animation-duration: 300ms;
    --theme-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
    --theme-animation-duration-fast: 150ms;
    --theme-animation-duration-slow: 500ms;
    --theme-animation-easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
    --theme-animation-easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
    --theme-animation-easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

    /* Typography Utilities */
    --theme-font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --theme-font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    --theme-font-size-base: 16px;
    --theme-line-height-base: 1.5;

    /* Spacing Utilities */
    --theme-spacing-xs: 4px;
    --theme-spacing-sm: 8px;
    --theme-spacing-md: 16px;
    --theme-spacing-lg: 24px;
    --theme-spacing-xl: 32px;
    --theme-spacing-2xl: 48px;

    /* Border Radius Utilities */
    --theme-border-radius-sm: 3px;
    --theme-border-radius-md: 6px;
    --theme-border-radius-lg: 8px;
    --theme-border-radius-xl: 12px;

    /* Shadow Utilities */
    --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Z-Index Utilities */
    --theme-z-index-dropdown: 1000;
    --theme-z-index-sticky: 1020;
    --theme-z-index-fixed: 1030;
    --theme-z-index-modal-backdrop: 1040;
    --theme-z-index-modal: 1050;
    --theme-z-index-popover: 1060;
    --theme-z-index-tooltip: 1070;

    /* Opacity Utilities */
    --theme-opacity-disabled: 0.6;
    --theme-opacity-hover: 0.8;
    --theme-opacity-focus: 0.9;

    /* Utility Fallbacks */
    --theme-animation-duration-fallback: 250ms;
    --theme-animation-easing-fallback: ease-in-out;
    --theme-font-family-fallback: sans-serif;
    --theme-font-family-mono-fallback: monospace;
    --theme-border-radius-md-fallback: 4px;
    --theme-shadow-md-fallback: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Light Theme Utilities (inherit defaults) */
  :root.theme-light,
  html.theme-light,
  .theme-light {
    /* Light theme uses the same utility values as defaults */
  }

  /* Dark Theme Utilities (inherit defaults with dark-specific shadows) */
  :root.theme-dark,
  html.theme-dark,
  .theme-dark {
    /* Dark theme shadows with higher opacity for better visibility */
    --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --theme-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
  }

  /* ==========================================================================
     LEGACY COMPATIBILITY VARIABLES
     Backward compatibility aliases for existing CSS that uses old variable names
     ========================================================================== */

  :root {
    /* Legacy Editor Variables (mapped to new semantic names) */
    --editor-text: var(--theme-color-text, var(--theme-color-text-fallback));
    --editor-bg: var(--theme-component-editor-background, var(--theme-component-editor-background-fallback));
    --editor-content-text: var(--theme-component-editor-text, var(--theme-component-editor-text-fallback));
    --editor-placeholder-text: var(--theme-component-editor-placeholder, #9ca3af);
    --editor-border: var(--theme-component-editor-border, var(--theme-color-border-fallback));
    --editor-shadow: var(--theme-component-editor-shadow, var(--theme-shadow-md-fallback));
    --editor-selection-bg: var(--theme-component-editor-selection-background, rgba(59, 130, 246, 0.2));
    --editor-selection-text: var(--theme-component-editor-selection-text, inherit);
    --editor-cursor-color: var(--theme-component-editor-cursor, var(--theme-component-editor-text-fallback));
    --editor-highlight-bg: var(--theme-component-editor-highlight-background, rgba(251, 191, 36, 0.3));

    /* Legacy Toolbar Variables */
    --toolbar-bg: var(--theme-component-toolbar-background, var(--theme-component-toolbar-background-fallback));
    --toolbar-border: var(--theme-component-toolbar-border, var(--theme-component-toolbar-border-fallback));
    --toolbar-button-bg: var(--theme-component-toolbar-button-background, transparent);
    --toolbar-button-text: var(--theme-component-toolbar-button-text, var(--theme-color-text-fallback));
    --toolbar-button-hover-bg: var(--theme-component-toolbar-button-hover-background, var(--theme-hover-background-fallback));
    --toolbar-button-active-bg: var(--theme-component-toolbar-button-active-background, var(--theme-active-background-fallback));
    --toolbar-button-active-text: var(--theme-component-toolbar-button-active-text, var(--theme-active-text-fallback));
    --toolbar-button-border: var(--theme-component-toolbar-button-border, var(--theme-color-border-fallback));

    /* Legacy Focus Variables */
    --focus-ring-color: var(--theme-component-focus-ring-color, var(--theme-component-focus-ring-color-fallback));
    --focus-ring-offset: var(--theme-component-focus-ring-offset, 2px);
    --focus-ring-width: var(--theme-component-focus-ring-width, 2px);
    --focus-ring-opacity: var(--theme-component-focus-ring-opacity, 1);
    --focus-outline-color: var(--theme-component-focus-outline-color, transparent);

    /* Legacy Plugin Variables */
    --palette-bg: var(--theme-plugin-palette-background, var(--theme-plugin-palette-background-fallback));
    --palette-border: var(--theme-plugin-palette-border, var(--theme-plugin-palette-border-fallback));
    --palette-shadow: var(--theme-plugin-palette-shadow, var(--theme-plugin-palette-shadow-fallback));
    --palette-tab-active-border: var(--theme-plugin-palette-tab-active-border, var(--theme-color-primary-fallback));
    --palette-swatch-border: var(--theme-plugin-palette-swatch-border, rgba(0, 0, 0, 0.1));
  }
}
