/* Theme layer for variables and theming */
@layer theme {
  /* Default theme variables (light) */
  :root {
    /* Typography - shared across themes */
    --editor-font: 16px/1.5 system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    --palette-swatch-size: 18px;
  }

  /* Light theme */
  :root.theme-light, html.theme-light, .theme-light {
    /* Colors - Light theme (essential for plugins.css) */
    --editor-text: #333333; /* Used for focus rings and palette text */

    /* Accessibility - Focus indicators */
    --focus-ring-color: #3b82f6; /* blue-500 */
    --focus-ring-offset: 2px;
    --focus-ring-width: 2px;
    --focus-ring-opacity: 1;
    --focus-outline-color: transparent;

    /* Accessibility - Keyboard feedback */
    --keyboard-feedback-bg: #ffffff;
    --keyboard-feedback-text: #333333;
    --keyboard-feedback-border: #e0e0e0;
    --keyboard-feedback-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* Clipboard - Visual feedback */
    --clipboard-feedback-bg: #ffffff;
    --clipboard-feedback-text: #333333;
    --clipboard-feedback-border: #e0e0e0;
    --clipboard-feedback-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    /* Editor Core - Container and content */
    --editor-bg: #ffffff;
    --editor-content-text: #1f2937; /* gray-800 */
    --editor-placeholder-text: #9ca3af; /* gray-400 */
    --editor-border: #e5e7eb; /* gray-200 */
    --editor-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    /* Editor Core - Selection and cursor */
    --editor-selection-bg: rgba(59, 130, 246, 0.2); /* blue-500 with opacity */
    --editor-selection-text: inherit;
    --editor-cursor-color: #1f2937; /* gray-800 */
    --editor-highlight-bg: rgba(251, 191, 36, 0.3); /* amber-400 with opacity */

    /* Editor Core - Formatting toolbar */
    --toolbar-bg: #f9fafb; /* gray-50 */
    --toolbar-border: #e5e7eb; /* gray-200 */
    --toolbar-button-bg: transparent;
    --toolbar-button-text: #374151; /* gray-700 */
    --toolbar-button-hover-bg: #f3f4f6; /* gray-100 */
    --toolbar-button-active-bg: #e5e7eb; /* gray-200 */
    --toolbar-button-active-text: #1f2937; /* gray-800 */
    --toolbar-button-border: #d1d5db; /* gray-300 */

    /* History - Undo/Redo controls */
    --history-button-bg: transparent;
    --history-button-text: #374151; /* gray-700 */
    --history-button-hover-bg: #f3f4f6; /* gray-100 */
    --history-button-active-bg: #e5e7eb; /* gray-200 */
    --history-button-disabled-bg: transparent;
    --history-button-disabled-text: #9ca3af; /* gray-400 */
    --history-indicator-bg: #ffffff;
    --history-indicator-border: #e5e7eb; /* gray-200 */
    --history-indicator-text: #6b7280; /* gray-500 */
    --history-change-highlight: rgba(59, 130, 246, 0.1); /* blue-500 with opacity */

    /* Performance - Virtual viewport and debug overlays */
    --viewport-line-bg: transparent;
    --viewport-line-text: inherit;
    --viewport-line-border: #e5e7eb; /* gray-200 */
    --performance-debug-bg: rgba(255, 255, 255, 0.95);
    --performance-debug-border: #e5e7eb; /* gray-200 */
    --performance-debug-text: #374151; /* gray-700 */
    --performance-metric-good: #10b981; /* emerald-500 */
    --performance-metric-warning: #f59e0b; /* amber-500 */
    --performance-metric-error: #ef4444; /* red-500 */

    /* Plugin UI - Color Palette */
    --palette-bg: #ffffff; /* Previously var(--editor-content-bg) */
    --palette-border: #e0e0e0; /* Previously var(--toolbar-border) */
    --palette-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Previously var(--editor-shadow) */
    --palette-tab-active-border: #4a86e8;
    --palette-swatch-border: rgba(0, 0, 0, 0.1);
  }

  /* Dark theme */
  :root.theme-dark, html.theme-dark, .theme-dark {
    --editor-text: #f0f0f0; /* Used for focus rings and palette text */

    /* Accessibility - Focus indicators */
    --focus-ring-color: #60a5fa; /* blue-400 */
    --focus-ring-offset: 2px;
    --focus-ring-width: 2px;
    --focus-ring-opacity: 1;
    --focus-outline-color: transparent;

    /* Accessibility - Keyboard feedback */
    --keyboard-feedback-bg: #2d2d2d;
    --keyboard-feedback-text: #f0f0f0;
    --keyboard-feedback-border: #444444;
    --keyboard-feedback-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    /* Clipboard - Visual feedback */
    --clipboard-feedback-bg: #2d2d2d;
    --clipboard-feedback-text: #f0f0f0;
    --clipboard-feedback-border: #444444;
    --clipboard-feedback-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);

    /* Editor Core - Container and content */
    --editor-bg: #334155; /* slate-700 */
    --editor-content-text: #f1f5f9; /* slate-100 */
    --editor-placeholder-text: #64748b; /* slate-500 */
    --editor-border: #475569; /* slate-600 */
    --editor-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);

    /* Editor Core - Selection and cursor */
    --editor-selection-bg: rgba(96, 165, 250, 0.3); /* blue-400 with opacity */
    --editor-selection-text: inherit;
    --editor-cursor-color: #f1f5f9; /* slate-100 */
    --editor-highlight-bg: rgba(251, 191, 36, 0.4); /* amber-400 with opacity */

    /* Editor Core - Formatting toolbar */
    --toolbar-bg: #1e293b; /* slate-800 */
    --toolbar-border: #475569; /* slate-600 */
    --toolbar-button-bg: transparent;
    --toolbar-button-text: #cbd5e1; /* slate-300 */
    --toolbar-button-hover-bg: #334155; /* slate-700 */
    --toolbar-button-active-bg: #475569; /* slate-600 */
    --toolbar-button-active-text: #f1f5f9; /* slate-100 */
    --toolbar-button-border: #64748b; /* slate-500 */

    /* History - Undo/Redo controls */
    --history-button-bg: transparent;
    --history-button-text: #cbd5e1; /* slate-300 */
    --history-button-hover-bg: #334155; /* slate-700 */
    --history-button-active-bg: #475569; /* slate-600 */
    --history-button-disabled-bg: transparent;
    --history-button-disabled-text: #64748b; /* slate-500 */
    --history-indicator-bg: #334155; /* slate-700 */
    --history-indicator-border: #475569; /* slate-600 */
    --history-indicator-text: #94a3b8; /* slate-400 */
    --history-change-highlight: rgba(96, 165, 250, 0.2); /* blue-400 with opacity */

    /* Performance - Virtual viewport and debug overlays */
    --viewport-line-bg: transparent;
    --viewport-line-text: inherit;
    --viewport-line-border: #475569; /* slate-600 */
    --performance-debug-bg: rgba(30, 41, 59, 0.95); /* slate-800 with opacity */
    --performance-debug-border: #475569; /* slate-600 */
    --performance-debug-text: #cbd5e1; /* slate-300 */
    --performance-metric-good: #10b981; /* emerald-500 */
    --performance-metric-warning: #f59e0b; /* amber-500 */
    --performance-metric-error: #ef4444; /* red-500 */

    /* Plugin UI - Color Palette (Dark) */
    --palette-bg: #333333; /* Previously var(--editor-content-bg) */
    --palette-border: #444444; /* Previously var(--toolbar-border) */
    --palette-shadow: 0 4px 12px rgba(0, 0, 0, 0.3); /* Previously var(--editor-shadow) */
    --palette-tab-active-border: #4a86e8;
    --palette-swatch-border: rgba(255, 255, 255, 0.1);
  }

  /* Legacy theme variables for backward compatibility */
  /* These variables are maintained for existing CSS that hasn't been migrated yet */
  /* New code should use the semantic variables from theme-variables.css */
}
