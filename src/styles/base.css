/* Base layer for core styles */
@layer base {
  /* Reset styles */
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  /* Basic layout */
  html, body {
    height: 100%;
    width: 100%;
    overflow-x: hidden;
  }

  body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px; /* Equivalent to p-4 */
    font: var(--editor-font); /* Keep using variable as it's in tailwind.config */
    @apply text-gray-800 dark:text-gray-100 bg-gray-100 dark:bg-gray-900 transition-colors duration-300 ease-in-out;
  }

  /* Ensure theme transitions are smooth */
  body, #toolbar, #editor, .editor-container, select {
    @apply transition-colors duration-300 ease-in-out;
  }

  /* Editor container */
  .editor-container {
    width: 100%;
    max-width: 800px; /* max-w-3xl or max-w-4xl depending on exact size */
    @apply mb-4 rounded-lg shadow-lg dark:shadow-xl overflow-hidden;
    container-type: inline-size;
    display: flex;
    flex-direction: column;
    position: relative;
    max-height: 80vh; /* Limit height to ensure it doesn't take over the viewport */
  }

  /* Toolbar */
  #toolbar {
    @apply flex flex-wrap gap-2 p-2 border-b border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800 sticky top-0 z-10 flex-shrink-0;
  }

  /* Editor area */
  #editor {
    min-height: 300px;
    height: 100%;
    @apply p-4 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-50 overflow-y-auto outline-none flex-grow;
    contain: content;
    position: relative;
    overscroll-behavior: contain; /* Prevent scroll chaining */
  }

  #editor:focus {
    outline: var(--focus-ring-width, 2px) solid var(--focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--focus-ring-width, 2px) var(--focus-ring-color, #3b82f6);
    outline-offset: 0;
    transition: box-shadow 150ms ease-in-out, outline 150ms ease-in-out;
  }

  /* Theme controls */
  .theme-controls {
    margin-top: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  select {
    @apply p-2 rounded border border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-50 text-sm;
  }

  /* Accessibility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus indicators - Theme-aware */
  :focus-visible {
    outline: var(--focus-ring-width, 2px) solid var(--focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--focus-ring-width, 2px) var(--focus-ring-color, #3b82f6);
    outline-offset: var(--focus-ring-offset, 2px);
    transition: box-shadow 150ms ease-in-out, outline 150ms ease-in-out;
  }

  /* Responsive toolbar */
  @container (min-width: 400px) {
    #toolbar {
      flex-direction: row;
    }
  }

  @container (max-width: 399px) {
    #toolbar {
      flex-direction: column;
    }
  }

  /* Editor placeholder style */
  #editor.placeholder::before {
    content: 'Start typing here...'; /* Default placeholder text */
    @apply absolute top-4 left-4 text-gray-400 dark:text-slate-500 italic pointer-events-none;
    /* Assumes #editor has p-4 padding and position:relative (which it does) */
  }
}

/* Print-specific styles moved from print-export.css */
@media print {
  body {
    -webkit-print-color-adjust: exact; /* Ensure backgrounds print */
    print-color-adjust: exact;
    margin: 0; /* Reset default body margins */
  }

  @page {
    margin: 1in; /* Default margin for printing */
  }

  .header, .footer { /* These classes would need to be applied to elements intended as print headers/footers */
    position: fixed;
    left: 0;
    right: 0;
    font-size: 10pt;
    color: #666;
    padding: 0 1in; /* Match page margin */
  }

  .header {
    top: 0.5in;
    text-align: center;
  }

  .footer {
    bottom: 0.5in;
    text-align: center;
  }
}
