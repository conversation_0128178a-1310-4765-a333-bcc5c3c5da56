/*
 * Utility classes and helper styles for FeatherJS
 * 
 * This file contains utility classes that provide common styling patterns
 * and helper classes that can be reused across components. These utilities
 * use theme variables to ensure consistency with the theme system.
 * 
 * Uses the 'utilities' layer for proper cascade hierarchy
 */

@layer utilities {
  /* ==========================================================================
     ACCESSIBILITY UTILITIES
     Screen reader and focus management utilities
     ========================================================================== */

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Focus utilities using theme variables */
  .focus-ring {
    outline: var(--theme-component-focus-ring-width, 2px) solid var(--theme-component-focus-outline-color, transparent);
    box-shadow: 0 0 0 var(--theme-component-focus-ring-width, 2px) var(--theme-component-focus-ring-color, #3b82f6);
    outline-offset: var(--theme-component-focus-ring-offset, 2px);
    transition: box-shadow var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out), 
                outline var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  /* ==========================================================================
     SPACING UTILITIES
     Margin and padding utilities using theme spacing variables
     ========================================================================== */

  .spacing-xs { margin: var(--theme-spacing-xs, 4px); }
  .spacing-sm { margin: var(--theme-spacing-sm, 8px); }
  .spacing-md { margin: var(--theme-spacing-md, 16px); }
  .spacing-lg { margin: var(--theme-spacing-lg, 24px); }
  .spacing-xl { margin: var(--theme-spacing-xl, 32px); }
  .spacing-2xl { margin: var(--theme-spacing-2xl, 48px); }

  .padding-xs { padding: var(--theme-spacing-xs, 4px); }
  .padding-sm { padding: var(--theme-spacing-sm, 8px); }
  .padding-md { padding: var(--theme-spacing-md, 16px); }
  .padding-lg { padding: var(--theme-spacing-lg, 24px); }
  .padding-xl { padding: var(--theme-spacing-xl, 32px); }
  .padding-2xl { padding: var(--theme-spacing-2xl, 48px); }

  /* ==========================================================================
     BORDER UTILITIES
     Border radius utilities using theme variables
     ========================================================================== */

  .rounded-sm { border-radius: var(--theme-border-radius-sm, 3px); }
  .rounded-md { border-radius: var(--theme-border-radius-md, 6px); }
  .rounded-lg { border-radius: var(--theme-border-radius-lg, 8px); }
  .rounded-xl { border-radius: var(--theme-border-radius-xl, 12px); }

  /* ==========================================================================
     SHADOW UTILITIES
     Box shadow utilities using theme variables
     ========================================================================== */

  .shadow-sm { box-shadow: var(--theme-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05)); }
  .shadow-md { box-shadow: var(--theme-shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)); }
  .shadow-lg { box-shadow: var(--theme-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)); }
  .shadow-xl { box-shadow: var(--theme-shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)); }

  /* ==========================================================================
     ANIMATION UTILITIES
     Animation and transition utilities using theme variables
     ========================================================================== */

  .transition-fast {
    transition: all var(--theme-animation-duration-fast, 150ms) var(--theme-animation-easing, ease-in-out);
  }

  .transition-normal {
    transition: all var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  .transition-slow {
    transition: all var(--theme-animation-duration-slow, 500ms) var(--theme-animation-easing, ease-in-out);
  }

  .transition-colors {
    transition: color var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                background-color var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out),
                border-color var(--theme-animation-duration, 300ms) var(--theme-animation-easing, ease-in-out);
  }

  /* ==========================================================================
     OPACITY UTILITIES
     Opacity utilities using theme variables
     ========================================================================== */

  .opacity-disabled { opacity: var(--theme-opacity-disabled, 0.6); }
  .opacity-hover { opacity: var(--theme-opacity-hover, 0.8); }
  .opacity-focus { opacity: var(--theme-opacity-focus, 0.9); }

  /* ==========================================================================
     Z-INDEX UTILITIES
     Z-index utilities using theme variables
     ========================================================================== */

  .z-dropdown { z-index: var(--theme-z-index-dropdown, 1000); }
  .z-sticky { z-index: var(--theme-z-index-sticky, 1020); }
  .z-fixed { z-index: var(--theme-z-index-fixed, 1030); }
  .z-modal-backdrop { z-index: var(--theme-z-index-modal-backdrop, 1040); }
  .z-modal { z-index: var(--theme-z-index-modal, 1050); }
  .z-popover { z-index: var(--theme-z-index-popover, 1060); }
  .z-tooltip { z-index: var(--theme-z-index-tooltip, 1070); }

  /* ==========================================================================
     TYPOGRAPHY UTILITIES
     Typography utilities using theme variables
     ========================================================================== */

  .font-mono { font-family: var(--theme-font-family-mono, monospace); }
  .font-sans { font-family: var(--theme-font-family, sans-serif); }

  .text-base { 
    font-size: var(--theme-font-size-base, 16px);
    line-height: var(--theme-line-height-base, 1.5);
  }

  /* ==========================================================================
     THEME COLOR UTILITIES
     Color utilities using semantic theme variables
     ========================================================================== */

  .text-primary { color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc)); }
  .text-secondary { color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666)); }
  .text-success { color: var(--theme-color-success, var(--theme-color-success-fallback, #008000)); }
  .text-warning { color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00)); }
  .text-error { color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000)); }
  .text-info { color: var(--theme-color-info, var(--theme-color-info-fallback, #0099cc)); }

  .bg-primary { background-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc)); }
  .bg-secondary { background-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666)); }
  .bg-success { background-color: var(--theme-color-success, var(--theme-color-success-fallback, #008000)); }
  .bg-warning { background-color: var(--theme-color-warning, var(--theme-color-warning-fallback, #ff8c00)); }
  .bg-error { background-color: var(--theme-color-error, var(--theme-color-error-fallback, #cc0000)); }
  .bg-info { background-color: var(--theme-color-info, var(--theme-color-info-fallback, #0099cc)); }

  .bg-surface { background-color: var(--theme-color-surface, var(--theme-color-surface-fallback, #f5f5f5)); }
  .bg-background { background-color: var(--theme-color-background, var(--theme-color-background-fallback, #ffffff)); }

  .border-primary { border-color: var(--theme-color-primary, var(--theme-color-primary-fallback, #0066cc)); }
  .border-secondary { border-color: var(--theme-color-secondary, var(--theme-color-secondary-fallback, #666666)); }
  .border-default { border-color: var(--theme-color-border, var(--theme-color-border-fallback, #cccccc)); }

  /* ==========================================================================
     INTERACTIVE STATE UTILITIES
     Hover, focus, and active state utilities using theme variables
     ========================================================================== */

  .hover-bg:hover { background-color: var(--theme-hover-background, var(--theme-hover-background-fallback, #f0f0f0)); }
  .hover-border:hover { border-color: var(--theme-hover-border, var(--theme-hover-border-fallback, #cccccc)); }
  .hover-text:hover { color: var(--theme-hover-text, var(--theme-hover-text-fallback, #000000)); }

  .focus-bg:focus { background-color: var(--theme-focus-background, var(--theme-focus-background-fallback, #ffffff)); }
  .focus-border:focus { border-color: var(--theme-focus-border, var(--theme-focus-border-fallback, #0066cc)); }

  .active-bg:active { background-color: var(--theme-active-background, var(--theme-active-background-fallback, #e0e0e0)); }
  .active-border:active { border-color: var(--theme-active-border, var(--theme-active-border-fallback, #cccccc)); }
  .active-text:active { color: var(--theme-active-text, var(--theme-active-text-fallback, #000000)); }

  .disabled-bg:disabled { background-color: var(--theme-disabled-background, var(--theme-disabled-background-fallback, #f0f0f0)); }
  .disabled-border:disabled { border-color: var(--theme-disabled-border, var(--theme-disabled-border-fallback, #e0e0e0)); }
  .disabled-text:disabled { color: var(--theme-disabled-text, var(--theme-disabled-text-fallback, #999999)); }
}
