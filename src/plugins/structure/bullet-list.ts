import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Bullet list plugin configuration
 */
const config: PluginConfig = {
  id: 'bullet-list',
  name: 'Bullet List',
  description: 'Create and format bullet lists',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'bullet-list',
      command: 'bulletList',
      icon: '•',
      label: 'Bullet List',
      tooltip: 'Bullet List (Ctrl/⌘+Shift+8)',
      group: 'structure',
      ariaLabel: 'Create bullet list',
    }
  ],
  shortcuts: [
    {
      command: 'bulletList',
      key: '8',
      ctrlKey: true,
      shiftKey: true,
      description: 'Create bullet list'
    }
  ]
};

/**
 * Bullet list plugin implementation
 * Creates and manages unordered lists
 */
export class BulletListPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the bullet list command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'bulletList' && this.editor) {
      this.toggleBulletList();
    }
  }
  
  /**
   * Toggle bullet list formatting for the current selection or block
   */
  private toggleBulletList(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Find the current context - are we already in a list?
    const listContext = this.getListContext(range);
    
    if (listContext.inBulletList) {
      // If we're already in a bullet list, convert it back to normal paragraphs
      this.convertListToParagraphs(listContext.listElement);
    } else if (listContext.inNumberedList) {
      // If we're in a numbered list, convert it to a bullet list
      this.convertNumberedToBulletList(listContext.listElement);
    } else {
      // Otherwise, create a new bullet list
      this.createBulletList(range);
    }
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Get the current list context based on a selection range
   * @param range The current selection range
   * @returns Information about the current list context
   */
  private getListContext(range: Range): { 
    inBulletList: boolean; 
    inNumberedList: boolean; 
    listElement: HTMLElement | null;
  } {
    let container = range.commonAncestorContainer;
    
    // Navigate up to element node if we're in a text node
    if (container.nodeType === Node.TEXT_NODE) {
      container = container.parentElement as Node;
    }
    
    // Find the containing list, if any
    let listItem: Element | null = null;
    let listElement: HTMLElement | null = null;
    
    if (container instanceof HTMLElement) {
      listItem = container.closest('li');
      if (listItem) {
        listElement = listItem.closest('ul, ol') as HTMLElement;
      }
    }
    
    return {
      inBulletList: !!listElement && listElement.tagName === 'UL',
      inNumberedList: !!listElement && listElement.tagName === 'OL',
      listElement
    };
  }
  
  /**
   * Create a new bullet list from the current selection
   * @param range The current selection range
   */
  private createBulletList(range: Range): void {
    if (!this.editor) return;
    
    // Find the block elements in the selection
    const blocks = this.getBlocksInRange(range);
    if (blocks.length === 0) return;
    
    // Create a new list element
    const ulElement = document.createElement('ul');
    
    // Process each block
    blocks.forEach(block => {
      const li = document.createElement('li');
      li.innerHTML = block.innerHTML;
      ulElement.appendChild(li);
      
      // Remove the original block
      if (block.parentNode) {
        block.parentNode.removeChild(block);
      }
    });
    
    // Insert the new list at the start position of the first block
    const firstBlock = blocks[0];
    if (firstBlock.parentNode) {
      firstBlock.parentNode.insertBefore(ulElement, firstBlock);
    } else {
      // Fallback - append to editor
      this.editor.getElement().appendChild(ulElement);
    }
    
    // Set selection to the first list item
    const firstLi = ulElement.firstElementChild;
    if (firstLi) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        const newRange = document.createRange();
        newRange.selectNodeContents(firstLi);
        selection.addRange(newRange);
      }
    }
  }
  
  /**
   * Convert a list to normal paragraphs
   * @param listElement The list element to convert
   */
  private convertListToParagraphs(listElement: HTMLElement | null): void {
    if (!listElement || !listElement.parentNode) return;
    
    // Get all list items
    const items = Array.from(listElement.querySelectorAll('li'));
    
    // Create a document fragment to hold the paragraphs
    const fragment = document.createDocumentFragment();
    
    // Convert each list item to a paragraph
    items.forEach(item => {
      const p = document.createElement('p');
      p.innerHTML = item.innerHTML;
      fragment.appendChild(p);
    });
    
    // Replace the list with the new paragraphs
    listElement.parentNode.replaceChild(fragment, listElement);
  }
  
  /**
   * Convert a numbered list to a bullet list
   * @param listElement The numbered list element to convert
   */
  private convertNumberedToBulletList(listElement: HTMLElement | null): void {
    if (!listElement || listElement.tagName !== 'OL' || !listElement.parentNode) return;
    
    // Create a new bullet list
    const ulElement = document.createElement('ul');
    
    // Copy all the list items
    Array.from(listElement.children).forEach(child => {
      ulElement.appendChild(child.cloneNode(true));
    });
    
    // Replace the numbered list with the bullet list
    listElement.parentNode.replaceChild(ulElement, listElement);
  }
  
  /**
   * Get block level elements in the current selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      // If we didn't find a block element, create a paragraph around the selection
      const paragraph = document.createElement('p');
      range.surroundContents(paragraph);
      return [paragraph];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<BulletListPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new BulletListPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: BulletListPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
