import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Text alignment plugin configuration
 */
const config: PluginConfig = {
  id: 'alignment',
  name: 'Text Alignment',
  description: 'Control text alignment (left, center, right, justify)',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'align',
      command: 'align',
      icon: '≡',
      label: 'Align',
      tooltip: 'Text Alignment',
      group: 'structure',
      ariaLabel: 'Text alignment options',
      type: 'dropdown'
    }
  ],
  shortcuts: [
    {
      command: 'alignLeft',
      key: 'l',
      ctrlKey: true,
      shiftKey: true,
      description: 'Align text left'
    },
    {
      command: 'alignCenter',
      key: 'e',
      ctrlKey: true,
      shiftKey: true,
      description: 'Align text center'
    },
    {
      command: 'alignRight',
      key: 'r',
      ctrlKey: true,
      shiftKey: true,
      description: 'Align text right'
    },
    {
      command: 'alignJustify',
      key: 'j',
      ctrlKey: true,
      shiftKey: true,
      description: 'Justify text'
    }
  ]
};

/**
 * Alignment options enumeration
 */
enum AlignmentType {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
  JUSTIFY = 'justify'
}

/**
 * Text alignment plugin implementation
 * Controls paragraph text alignment
 */
export class AlignmentPlugin extends BasePlugin {
  private dropdown: HTMLElement | null = null;
  private isDropdownOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Handle alignment commands
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (!this.editor) return;
    
    // Map commands to alignment types
    const alignmentMap: Record<string, AlignmentType> = {
      'alignLeft': AlignmentType.LEFT,
      'alignCenter': AlignmentType.CENTER,
      'alignRight': AlignmentType.RIGHT,
      'alignJustify': AlignmentType.JUSTIFY
    };
    
    if (alignmentMap[_command]) {
      this.applyAlignment(alignmentMap[_command]);
    } else if (_command === 'align') {
      this.toggleAlignmentDropdown();
    }
  }
  
  /**
   * Toggle the alignment dropdown menu
   */
  private toggleAlignmentDropdown(): void {
    if (this.isDropdownOpen) {
      this.closeAlignmentDropdown();
    } else {
      this.openAlignmentDropdown();
    }
  }
  
  /**
   * Open the alignment dropdown menu
   */
  private openAlignmentDropdown(): void {
    if (!this.editor || this.isDropdownOpen) return;
    
    // Create dropdown if it doesn't exist
    if (!this.dropdown) {
      this.dropdown = document.createElement('div');
      this.dropdown.className = 'absolute bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 shadow-md p-2 z-[1050] min-w-[150px] rounded';
      this.dropdown.setAttribute('role', 'menu');
      this.dropdown.setAttribute('aria-label', 'Alignment options');

      // Add alignment options
      const options = [
        { value: AlignmentType.LEFT, label: 'Left Align', icon: '⫷' },
        { value: AlignmentType.CENTER, label: 'Center', icon: '≡' },
        { value: AlignmentType.RIGHT, label: 'Right Align', icon: '⫸' },
        { value: AlignmentType.JUSTIFY, label: 'Justify', icon: '⫸⫷' }
      ];
      
      options.forEach(option => {
        const menuItem = document.createElement('button');
        menuItem.type = 'button';
        menuItem.className = 'flex items-center w-full py-2 px-3 bg-transparent text-left cursor-pointer text-gray-800 dark:text-slate-200 rounded-sm hover:bg-gray-100 dark:hover:bg-slate-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-slate-700';
        menuItem.innerHTML = `<span class="inline-block w-6 mr-2 text-center font-bold">${option.icon}</span> ${option.label}`;
        menuItem.setAttribute('role', 'menuitem');
        menuItem.setAttribute('data-alignment', option.value);
        menuItem.addEventListener('click', () => {
          this.applyAlignment(option.value);
          this.closeAlignmentDropdown();
        });
        this.dropdown?.appendChild(menuItem);
      });

      // Close on outside click
      this.handleOutsideClick = this.handleOutsideClick.bind(this);
      document.addEventListener('click', this.handleOutsideClick);
    }
    
    // Find the toolbar button and position dropdown below it
    const toolbarButton = this.toolbarButtons.find(btn => 
      btn.getAttribute('data-command') === 'align');
    
    if (toolbarButton) {
      const rect = toolbarButton.getBoundingClientRect();
      this.dropdown.style.position = 'absolute';
      this.dropdown.style.top = `${window.scrollY + rect.bottom + 5}px`;
      this.dropdown.style.left = `${window.scrollX + rect.left}px`;

      document.body.appendChild(this.dropdown);
      this.isDropdownOpen = true;
      toolbarButton.setAttribute('aria-expanded', 'true');
    } else {
        console.warn('Alignment toolbar button not found for dropdown positioning.');
    }
  }
  
  /**
   * Close the alignment dropdown menu
   */
  private closeAlignmentDropdown(): void {
    if (this.dropdown && this.isDropdownOpen) {
      document.body.removeChild(this.dropdown);
      this.isDropdownOpen = false;
    }
  }
  
  /**
   * Handle clicks outside the dropdown to close it
   */
  private handleOutsideClick(event: MouseEvent): void {
    if (this.dropdown && !this.dropdown.contains(event.target as Node)) {
      // Check if the click was on the toolbar button itself
      const toolbarButton = this.toolbarButtons.find(btn =>
          btn.getAttribute('data-command') === 'align');
      if (!toolbarButton || !toolbarButton.contains(event.target as Node)) {
          this.closeAlignmentDropdown();
      }
    }
  }
  
  /**
   * Apply alignment to selected blocks
   * @param alignment The alignment type to apply
   */
  private applyAlignment(alignment: AlignmentType): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Get all block elements in the selection
    const blocks = this.getBlocksInRange(range);
    
    // Apply alignment to each block
    blocks.forEach(block => {
      block.style.textAlign = alignment;
      
      // Mark with a data attribute for potential future operations
      block.setAttribute('data-text-align', alignment);
    });
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Get all block elements within the selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      return [];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
  
  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dropdown if open
    if (this.isDropdownOpen) {
      this.closeAlignmentDropdown();
    }
    
    // Remove outside click listener
    document.removeEventListener('click', this.handleOutsideClick);
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<AlignmentPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new AlignmentPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: AlignmentPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
