import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Code block plugin configuration
 */
const config: PluginConfig = {
  id: 'code-block',
  name: 'Code Block',
  description: 'Insert formatted code blocks with syntax highlighting',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'code-block',
      command: 'codeBlock',
      icon: '{ }',
      label: 'Code Block',
      tooltip: 'Code Block (Ctrl/⌘+Shift+`)',
      group: 'structure',
      ariaLabel: 'Insert code block',
    }
  ],
  shortcuts: [
    {
      command: 'codeBlock',
      key: '`',
      ctrlKey: true,
      shiftKey: true,
      description: 'Insert code block'
    }
  ]
};

/**
 * Popular programming languages for syntax highlighting
 */
const PROGRAMMING_LANGUAGES = [
  { label: 'Plain Text', value: 'text' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' },
  { label: 'Python', value: 'python' },
  { label: 'Java', value: 'java' },
  { label: 'C++', value: 'cpp' },
  { label: 'C#', value: 'csharp' },
  { label: 'PHP', value: 'php' },
  { label: 'Ruby', value: 'ruby' },
  { label: 'Go', value: 'go' },
  { label: 'Swift', value: 'swift' },
  { label: 'Kotlin', value: 'kotlin' },
  { label: 'Rust', value: 'rust' },
  { label: 'SQL', value: 'sql' },
  { label: 'JSON', value: 'json' },
  { label: 'XML', value: 'xml' },
  { label: 'Markdown', value: 'markdown' },
  { label: 'Shell', value: 'shell' }
];

/**
 * Code block plugin implementation
 * Creates fenced code blocks with language selection
 */
export class CodeBlockPlugin extends BasePlugin {
  private languageSelector: HTMLElement | null = null;
  private isLanguageSelectorOpen = false;

  constructor() {
    super(config);
  }

  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }

  /**
   * Handle the code block command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'codeBlock' && this.editor) {
      this.insertCodeBlock();
    }
  }

  /**
   * Insert a code block at the current selection
   */
  private insertCodeBlock(): void {
    if (!this.editor) return;

    const selection = window.getSelection();
    if (!selection) return;

    const range = selection.getRangeAt(0);

    // Capture selected text if any
    const selectedText = range.toString();

    // Delete the selected content
    range.deleteContents();

    // Create code block container
    const codeBlock = document.createElement('div');
    codeBlock.className = 'feather-code-block relative font-mono bg-gray-100 dark:bg-slate-800 border border-gray-300 dark:border-slate-700 rounded my-4 p-4 overflow-x-auto text-gray-900 dark:text-slate-200';
    codeBlock.setAttribute('data-language', 'text');

    // Apply theme classes to the code block
    const htmlElement = document.documentElement;
    const storedTheme = localStorage.getItem('editor-theme');
    if (htmlElement.classList.contains('dark') || storedTheme === 'dark') {
      codeBlock.classList.add('dark');
    }
    if (storedTheme) {
      codeBlock.classList.add(`theme-${storedTheme}`);
    }

    // Create language label
    const languageLabel = document.createElement('div');
    languageLabel.className = 'absolute top-0 right-0 bg-gray-200 dark:bg-slate-700 text-gray-800 dark:text-slate-300 rounded-tr-sm rounded-bl-sm px-2 py-1 text-sm font-sans cursor-pointer select-none transition-colors duration-200 hover:bg-gray-300 dark:hover:bg-slate-600';
    languageLabel.textContent = 'Plain Text';
    languageLabel.setAttribute('role', 'button');
    languageLabel.setAttribute('aria-label', 'Change code language');
    languageLabel.contentEditable = 'false';

    // Add click handler to language label
    languageLabel.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
      this.showLanguageSelector(codeBlock, languageLabel);
    });

    // Create pre element for code
    const preElement = document.createElement('pre');
    preElement.className = 'whitespace-pre tab-2';

    // Create code element
    const codeElement = document.createElement('code');
    codeElement.className = 'block w-full';

    // Add selected text or placeholder
    codeElement.textContent = selectedText || 'Write your code here...';

    // Assemble the structure
    preElement.appendChild(codeElement);
    codeBlock.appendChild(languageLabel);
    codeBlock.appendChild(preElement);

    // Insert the code block
    range.insertNode(codeBlock);

    // Create a paragraph after the code block if it's at the end
    const isLastNode = !codeBlock.nextSibling;
    if (isLastNode) {
      const p = document.createElement('p');
      p.innerHTML = '<br>'; // Empty paragraph with a break to allow typing

      if (codeBlock.parentNode) {
        codeBlock.parentNode.insertBefore(p, codeBlock.nextSibling);
      }
    }

    // Set selection inside the code element
    selection.removeAllRanges();
    const newRange = document.createRange();
    newRange.selectNodeContents(codeElement);
    selection.addRange(newRange);

    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }

  /**
   * Show the language selector dropdown
   * @param codeBlock The code block element
   * @param languageLabel The language label element
   */
  private showLanguageSelector(codeBlock: HTMLElement, languageLabel: HTMLElement): void {
    if (this.isLanguageSelectorOpen) {
      this.closeLanguageSelector();
      return;
    }

    // Create language selector if it doesn't exist
    if (!this.languageSelector) {
      this.languageSelector = document.createElement('div');
      this.languageSelector.className = 'absolute bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded shadow-md max-h-[200px] overflow-y-auto z-[1000] py-1';

      // Add language options
      PROGRAMMING_LANGUAGES.forEach(lang => {
        const button = document.createElement('button');
        button.className = 'block w-full text-left px-3 py-1 border-none bg-transparent cursor-pointer text-gray-800 dark:text-slate-200 whitespace-nowrap hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-900 dark:hover:text-slate-50 focus:bg-gray-100 dark:focus:bg-slate-700 focus:text-gray-900 dark:focus:text-slate-50 focus:outline-none';
        button.textContent = lang.label;
        button.setAttribute('data-language', lang.value);
        button.addEventListener('click', () => {
          this.setCodeLanguage(codeBlock, languageLabel, lang.value, lang.label);
          this.closeLanguageSelector();
        });
        this.languageSelector?.appendChild(button);
      });

      // Close on outside click
      document.addEventListener('click', this.handleOutsideClick);
    }

    // Position the language selector
    const rect = languageLabel.getBoundingClientRect();
    this.languageSelector.style.position = 'absolute';
    this.languageSelector.style.top = `${rect.bottom}px`;
    this.languageSelector.style.left = `${rect.left}px`;
    this.languageSelector.style.minWidth = `${Math.max(rect.width * 2, 150)}px`;

    // Add to document
    document.body.appendChild(this.languageSelector);
    this.isLanguageSelectorOpen = true;
  }

  /**
   * Close the language selector dropdown
   */
  private closeLanguageSelector(): void {
    if (this.languageSelector && this.isLanguageSelectorOpen) {
      document.body.removeChild(this.languageSelector);
      this.isLanguageSelectorOpen = false;
    }
  }

  /**
   * Handle clicks outside the dropdown to close it
   */
  private handleOutsideClick = (event: MouseEvent): void => {
    if (this.isLanguageSelectorOpen &&
        this.languageSelector &&
        !this.languageSelector.contains(event.target as Node)) {
      this.closeLanguageSelector();
    }
  };

  /**
   * Set the language for a code block
   * @param codeBlock The code block element
   * @param languageLabel The language label element
   * @param languageValue The language value/identifier
   * @param languageText The display text for the language
   */
  private setCodeLanguage(
    codeBlock: HTMLElement,
    languageLabel: HTMLElement,
    languageValue: string,
    languageText: string
  ): void {
    // Update the language attribute and label
    codeBlock.setAttribute('data-language', languageValue);
    languageLabel.textContent = languageText;

    // Update the code element's class for potential syntax highlighting
    const codeElement = codeBlock.querySelector('code');
    if (codeElement) {
      // Remove any existing language classes
      const classesToRemove: string[] = [];
      for (let i = 0; i < codeElement.classList.length; i++) {
        const className = codeElement.classList[i];
        if (className.startsWith('language-')) {
          classesToRemove.push(className);
        }
      }
      classesToRemove.forEach(cls => codeElement.classList.remove(cls));

      // Add the new language class
      codeElement.classList.add(`language-${languageValue}`);
    }

    // Trigger input event for history
    if (this.editor) {
      this.editor.getElement().dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }

  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();

    // Close selector if open
    if (this.isLanguageSelectorOpen) {
      this.closeLanguageSelector();
    }

    // Remove outside click listener
    document.removeEventListener('click', this.handleOutsideClick);
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<CodeBlockPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new CodeBlockPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: CodeBlockPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
