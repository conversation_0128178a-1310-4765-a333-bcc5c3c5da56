import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Horizontal rule plugin configuration
 */
const config: PluginConfig = {
  id: 'horizontal-rule',
  name: 'Horizontal Rule',
  description: 'Insert a horizontal divider line',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'horizontal-rule',
      command: 'horizontalRule',
      icon: '―',
      label: 'HR',
      tooltip: 'Horizontal Rule (Ctrl/⌘+Shift+H)',
      group: 'structure',
      ariaLabel: 'Insert horizontal rule',
    }
  ],
  shortcuts: [
    {
      command: 'horizontalRule',
      key: 'h',
      ctrlKey: true,
      shiftKey: true,
      description: 'Insert horizontal rule'
    }
  ]
};

/**
 * Horizontal rule plugin implementation
 * Inserts <hr> elements
 */
export class HorizontalRulePlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the horizontal rule command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'horizontalRule' && this.editor) {
      this.insertHorizontalRule();
    }
  }
  
  /**
   * Insert a horizontal rule at the current selection
   */
  private insertHorizontalRule(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Delete any selected content
    range.deleteContents();
    
    // Create a horizontal rule element
    const hr = document.createElement('hr');
    hr.className = 'w-full border-t border-gray-300 dark:border-gray-500 my-6';
    
    // Insert the horizontal rule
    range.insertNode(hr);
    
    // Create a paragraph after the horizontal rule for the cursor position
    const p = document.createElement('p');
    p.innerHTML = '<br>'; // Empty paragraph with a break to allow typing
    
    // Insert the paragraph after the HR
    if (hr.parentNode) {
      hr.parentNode.insertBefore(p, hr.nextSibling);
      
      // Move cursor to the new paragraph
      selection.removeAllRanges();
      const newRange = document.createRange();
      newRange.setStart(p, 0);
      newRange.collapse(true);
      selection.addRange(newRange);
    }
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<HorizontalRulePlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new HorizontalRulePlugin();
    instance.init(editor);
    return instance;
  },
  (instance: HorizontalRulePlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
