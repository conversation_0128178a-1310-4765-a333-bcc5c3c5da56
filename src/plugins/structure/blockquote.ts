import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Blockquote plugin configuration
 */
const config: PluginConfig = {
  id: 'blockquote',
  name: 'Blockquote',
  description: 'Create and format blockquote sections',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'blockquote',
      command: 'blockquote',
      icon: '❝❞',
      label: 'Blockquote',
      tooltip: 'Blockquote (Ctrl/⌘+Shift+B)',
      group: 'structure',
      ariaLabel: 'Format as blockquote',
    }
  ],
  shortcuts: [
    {
      command: 'blockquote',
      key: 'b',
      ctrlKey: true,
      shiftKey: true,
      description: 'Format as blockquote'
    }
  ]
};

/**
 * Blockquote plugin implementation
 * Creates and manages blockquote formatting
 */
export class BlockquotePlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the blockquote command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'blockquote' && this.editor) {
      this.toggleBlockquote();
    }
  }
  
  /**
   * Toggle blockquote formatting for the current selection or block
   */
  private toggleBlockquote(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Check if we're already in a blockquote
    const blockquoteElement = this.findBlockquoteParent(range);
    
    if (blockquoteElement) {
      // If already in a blockquote, convert back to paragraphs
      this.convertBlockquoteToParagraphs(blockquoteElement);
    } else {
      // If not in a blockquote, convert blocks to a blockquote
      this.createBlockquote(range);
    }
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Find blockquote parent if the selection is within one
   * @param range The current selection range
   * @returns The blockquote element or null
   */
  private findBlockquoteParent(range: Range): HTMLElement | null {
    let container = range.commonAncestorContainer;
    
    // Navigate up to element node if we're in a text node
    if (container.nodeType === Node.TEXT_NODE) {
      container = container.parentElement as Node;
    }
    
    // Find the containing blockquote, if any
    if (container instanceof HTMLElement) {
      const blockquote = container.closest('blockquote');
      return blockquote as HTMLElement;
    }
    
    return null;
  }
  
  /**
   * Create a new blockquote from the current selection
   * @param range The current selection range
   */
  private createBlockquote(range: Range): void {
    if (!this.editor) return;
    
    // Get all block elements in the selection
    const blocks = this.getBlocksInRange(range);
    if (blocks.length === 0) return;
    
    // Create a new blockquote element
    const blockquote = document.createElement('blockquote');
    blockquote.className = 'border-l-4 border-gray-300 dark:border-gray-500 mx-0 pl-4 text-gray-700 dark:text-gray-300 italic my-2';
    
    // Process each block
    blocks.forEach(block => {
      // Clone the block's content into the blockquote
      const content = block.innerHTML;
      
      // For multi-block selections, use paragraphs for each block
      const p = document.createElement('p');
      p.innerHTML = content;
      blockquote.appendChild(p);
      
      // Remove the original block
      if (block.parentNode) {
        block.parentNode.removeChild(block);
      }
    });
    
    // Insert the blockquote at the start position
    const firstBlock = blocks[0];
    if (firstBlock.parentNode) {
      firstBlock.parentNode.insertBefore(blockquote, firstBlock);
    } else {
      // Fallback - append to editor
      this.editor.getElement().appendChild(blockquote);
    }
    
    // Set selection inside the blockquote
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      const newRange = document.createRange();
      
      // If there's at least one paragraph, select it
      if (blockquote.firstElementChild) {
        newRange.selectNodeContents(blockquote.firstElementChild);
      } else {
        newRange.selectNodeContents(blockquote);
      }
      
      selection.addRange(newRange);
    }
  }
  
  /**
   * Convert a blockquote to normal paragraphs
   * @param blockquote The blockquote element to convert
   */
  private convertBlockquoteToParagraphs(blockquote: HTMLElement): void {
    if (!blockquote || !blockquote.parentNode) return;
    
    // Get all paragraphs within the blockquote or create one if empty
    let paragraphs = Array.from(blockquote.querySelectorAll('p'));
    
    // If no paragraphs found, wrap the content in a paragraph
    if (paragraphs.length === 0) {
      const p = document.createElement('p');
      p.innerHTML = blockquote.innerHTML;
      paragraphs = [p];
    }
    
    // Create a document fragment to hold the paragraphs
    const fragment = document.createDocumentFragment();
    
    // Add each paragraph to the fragment
    paragraphs.forEach(p => {
      const newP = document.createElement('p');
      newP.innerHTML = p.innerHTML;
      fragment.appendChild(newP);
    });
    
    // Replace the blockquote with the paragraphs
    blockquote.parentNode.replaceChild(fragment, blockquote);
  }
  
  /**
   * Get all block elements within the selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      // If we didn't find a block element, create a paragraph around the selection
      const paragraph = document.createElement('p');
      range.surroundContents(paragraph);
      return [paragraph];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<BlockquotePlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new BlockquotePlugin();
    instance.init(editor);
    return instance;
  },
  (instance: BlockquotePlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
