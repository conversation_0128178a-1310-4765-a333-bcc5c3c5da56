import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Line height plugin configuration
 */
const config: PluginConfig = {
  id: 'line-height',
  name: 'Line Height',
  description: 'Control line spacing for paragraphs',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'line-height',
      command: 'lineHeight',
      icon: '↕',
      label: 'Line Height',
      tooltip: 'Line Height',
      group: 'structure',
      ariaLabel: 'Change line height',
      type: 'dropdown'
    }
  ],
  shortcuts: [] // No shortcuts for this plugin
};

/**
 * Line height options with labels and values
 */
const LINE_HEIGHT_OPTIONS = [
  { label: 'Single (1.0)', value: '1.0' },
  { label: 'Tight (1.2)', value: '1.2' },
  { label: 'Standard (1.5)', value: '1.5' },
  { label: 'Relaxed (1.8)', value: '1.8' },
  { label: 'Double (2.0)', value: '2.0' }
];

/**
 * Line height plugin implementation
 * Controls paragraph line height/spacing
 */
export class LineHeightPlugin extends BasePlugin {
  private dropdown: HTMLElement | null = null;
  private isDropdownOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Handle line height commands
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'lineHeight' && this.editor) {
      this.toggleLineHeightDropdown();
    } else if (_command.startsWith('lineHeight-') && this.editor) {
      // Extract the line height value from the command
      const heightValue = _command.replace('lineHeight-', '');
      this.applyLineHeight(heightValue);
    }
  }
  
  /**
   * Toggle the line height dropdown menu
   */
  private toggleLineHeightDropdown(): void {
    if (this.isDropdownOpen) {
      this.closeLineHeightDropdown();
    } else {
      this.openLineHeightDropdown();
    }
  }
  
  /**
   * Open the line height dropdown menu
   */
  private openLineHeightDropdown(): void {
    if (!this.editor || this.isDropdownOpen) return;
    
    // Create dropdown if it doesn't exist
    if (!this.dropdown) {
      this.dropdown = document.createElement('div');
      this.dropdown.className = 'absolute bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded shadow-md min-w-[150px] max-h-[250px] overflow-y-auto z-[1000] py-1';
      this.dropdown.setAttribute('role', 'menu');
      this.dropdown.setAttribute('aria-label', 'Line height options');
      
      // Add line height options
      const buttonBaseClass = 'block w-full text-left px-[10px] py-[5px] border-none bg-transparent cursor-pointer text-gray-800 dark:text-slate-200 whitespace-nowrap hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-900 dark:hover:text-slate-50 focus:bg-gray-100 dark:focus:bg-slate-700 focus:text-gray-900 dark:focus:text-slate-50 focus:outline-none';
      LINE_HEIGHT_OPTIONS.forEach(option => {
        const menuItem = document.createElement('button');
        menuItem.className = buttonBaseClass;
        menuItem.textContent = option.label;
        menuItem.setAttribute('role', 'menuitem');
        menuItem.setAttribute('data-value', option.value);
        menuItem.addEventListener('click', () => {
          this.applyLineHeight(option.value);
          this.closeLineHeightDropdown();
        });
        this.dropdown?.appendChild(menuItem);
      });
      
      // Close on outside click
      document.addEventListener('click', this.handleOutsideClick);
    }
    
    // Find the toolbar button and position dropdown below it
    const toolbarButton = this.toolbarButtons.find(btn => 
      btn.getAttribute('data-command') === 'lineHeight');
    
    if (toolbarButton) {
      const rect = toolbarButton.getBoundingClientRect();
      this.dropdown.style.position = 'absolute'; // Already set by Tailwind class, but explicit for clarity
      this.dropdown.style.top = `${window.scrollY + rect.bottom + 2}px`; // Position below button with a small gap
      this.dropdown.style.left = `${window.scrollX + rect.left}px`;
      // z-index is handled by Tailwind class 'z-[1000]'
      
      // Add dropdown to document
      document.body.appendChild(this.dropdown);
      this.isDropdownOpen = true;
      toolbarButton.setAttribute('aria-expanded', 'true'); // For accessibility
    } else {
      console.warn('LineHeight toolbar button not found for dropdown positioning.');
    }
  }
  
  /**
   * Close the line height dropdown menu
   */
  private closeLineHeightDropdown(): void {
    if (this.dropdown && this.isDropdownOpen) {
      document.body.removeChild(this.dropdown);
      this.isDropdownOpen = false;
    }
  }
  
  /**
   * Handle clicks outside the dropdown to close it
   * @param event The mouse event
   */
  private handleOutsideClick = (event: MouseEvent): void => {
    if (!this.isDropdownOpen || !this.dropdown) return;
    
    // Check if click is inside the dropdown or the toolbar button
    const clickedElement = event.target as Node;
    const toolbarButton = this.toolbarButtons.find(btn => 
      btn.getAttribute('data-command') === 'lineHeight');

    if (this.dropdown && !this.dropdown.contains(clickedElement) &&
        (!toolbarButton || !toolbarButton.contains(clickedElement))) {
      // Click was outside the dropdown and not on the toolbar button that opened it
      this.closeLineHeightDropdown();
    }
  };
  
  /**
   * Apply line height to selected blocks
   * @param lineHeight The line height value to apply
   */
  private applyLineHeight(lineHeight: string): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Get all block elements in the selection
    const blocks = this.getBlocksInRange(range);
    
    // Apply line height to each block
    blocks.forEach(block => {
      block.style.lineHeight = lineHeight;
      
      // Mark with a data attribute for potential future operations
      block.setAttribute('data-line-height', lineHeight);
    });
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Get all block elements within the selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      return [];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
  
  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dropdown if open
    if (this.isDropdownOpen) {
      this.closeLineHeightDropdown();
    }
    
    // Remove outside click listener
    document.removeEventListener('click', this.handleOutsideClick);
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<LineHeightPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new LineHeightPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: LineHeightPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
