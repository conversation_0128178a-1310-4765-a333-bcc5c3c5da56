import type { Editor, Plugin } from '../../types';
import { createPlugin } from '../plugin-factory';

// Import all structure plugins
import headingsPlugin from './headings';
import bulletListPlugin from './bullet-list';
import numberedListPlugin from './numbered-list';
import checklistPlugin from './checklist';
import indentPlugin from './indent';
import outdentPlugin from './outdent';
import alignmentPlugin from './alignment';
import lineHeightPlugin from './line-height';
import blockquotePlugin from './blockquote';
import horizontalRulePlugin from './horizontal-rule';
import codeBlockPlugin from './code-block';
import tablePlugin from './table';
import collapsiblePlugin from './collapsible';

// This file serves as the entry point for all structure plugins
// Each plugin is exported individually to ensure tree-shaking works properly

// Export individual plugins
export { 
  headingsPlugin,
  bulletListPlugin,
  numberedListPlugin,
  checklistPlugin,
  indentPlugin,
  outdentPlugin,
  alignmentPlugin,
  lineHeightPlugin,
  blockquotePlugin,
  horizontalRulePlugin,
  codeBlockPlugin,
  tablePlugin,
  collapsiblePlugin
};

// Export a combined plugin group for convenience
export const StructurePlugins: Plugin[] = [
  headingsPlugin,
  bulletListPlugin,
  numberedListPlugin,
  checklistPlugin,
  indentPlugin,
  outdentPlugin,
  alignmentPlugin,
  lineHeightPlugin,
  blockquotePlugin,
  horizontalRulePlugin,
  codeBlockPlugin,
  tablePlugin,
  collapsiblePlugin
];

// Create a group plugin that registers all structure plugins
const groupPlugin: Plugin = createPlugin<Plugin[]>(
  'structure-group',
  (editor: Editor) => {
    // Initialize all plugins
    const plugins = StructurePlugins.map(plugin => {
      plugin.init(editor);
      return plugin;
    });
    return plugins;
  },
  (plugins: Plugin[]) => {
    // Clean up all plugins
    plugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
);

// Default export for simplified registration
export default groupPlugin;
