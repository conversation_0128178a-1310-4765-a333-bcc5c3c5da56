import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Checklist plugin configuration
 */
const config: PluginConfig = {
  id: 'checklist',
  name: 'Checklist',
  description: 'Create and manage task lists with checkboxes',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'checklist',
      command: 'checklist',
      icon: '☑',
      label: 'Checklist',
      tooltip: 'Checklist (Ctrl/⌘+Shift+9)',
      group: 'structure',
      ariaLabel: 'Create task list',
    }
  ],
  shortcuts: [
    {
      command: 'checklist',
      key: '9',
      ctrlKey: true,
      shiftKey: true,
      description: 'Create task list'
    }
  ]
};

/**
 * Checklist plugin implementation
 * Creates and manages task lists with checkboxes
 */
export class ChecklistPlugin extends BasePlugin {
  // Store a map of click handlers for checkbox elements
  private checkboxHandlers = new Map<HTMLElement, (event: MouseEvent) => void>();
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the checklist plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Set up global checkbox click handler
    editor.getElement().addEventListener('click', this.handleEditorClick);
    
    // Setup mutation observer to handle dynamically added checkboxes
    this.setupMutationObserver();
  }
  
  /**
   * Handle the checklist command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'checklist' && this.editor) {
      this.toggleChecklist();
    }
  }
  
  /**
   * Toggle checklist formatting for the current selection or block
   */
  private toggleChecklist(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Find the current context - are we already in a list?
    const listContext = this.getListContext(range);
    
    if (listContext.inChecklist) {
      // If we're already in a checklist, convert it back to normal paragraphs
      this.convertListToParagraphs(listContext.listElement);
    } else if (listContext.inList) {
      // If we're in a regular list, convert it to a checklist
      this.convertListToChecklist(listContext.listElement);
    } else {
      // Otherwise, create a new checklist
      this.createChecklist(range);
    }
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Get the current list context based on a selection range
   * @param range The current selection range
   * @returns Information about the current list context
   */
  private getListContext(range: Range): { 
    inList: boolean; 
    inChecklist: boolean; 
    listElement: HTMLElement | null;
  } {
    let container = range.commonAncestorContainer;
    
    // Navigate up to element node if we're in a text node
    if (container.nodeType === Node.TEXT_NODE) {
      container = container.parentElement as Node;
    }
    
    // Find the containing list, if any
    let listItem: Element | null = null;
    let listElement: HTMLElement | null = null;
    
    if (container instanceof HTMLElement) {
      listItem = container.closest('li');
      if (listItem) {
        listElement = listItem.closest('ul, ol') as HTMLElement;
      }
    }
    
    return {
      inList: !!listElement,
      inChecklist: !!listElement && listElement.classList.contains('feather-task-list'),
      listElement
    };
  }
  
  /**
   * Create a new checklist from the current selection
   * @param range The current selection range
   */
  private createChecklist(range: Range): void {
    if (!this.editor) return;
    
    // Find the block elements in the selection
    const blocks = this.getBlocksInRange(range);
    if (blocks.length === 0) return;
    
    // Create a new list element
    const ulElement = document.createElement('ul');
    ulElement.className = 'list-none pl-0'; // Tailwind for ul
    
    // Process each block
    blocks.forEach(block => {
      const li = this.createChecklistItem(block.innerHTML);
      ulElement.appendChild(li);
      
      // Remove the original block
      if (block.parentNode) {
        block.parentNode.removeChild(block);
      }
    });
    
    // Insert the new list at the start position of the first block
    const firstBlock = blocks[0];
    if (firstBlock.parentNode) {
      firstBlock.parentNode.insertBefore(ulElement, firstBlock);
    } else {
      // Fallback - append to editor
      this.editor.getElement().appendChild(ulElement);
    }
    
    // Set selection to the first list item content
    const firstLi = ulElement.firstElementChild;
    if (firstLi) {
      const contentElement = firstLi.querySelector('.feather-task-content');
      if (contentElement) {
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          const newRange = document.createRange();
          newRange.selectNodeContents(contentElement);
          selection.addRange(newRange);
        }
      }
    }
  }
  
  /**
   * Create a checklist item with checkbox
   * @param content The content for the list item
   * @returns A list item element with checkbox and content
   */
  private createChecklistItem(content: string, isChecked = false): HTMLLIElement {
    const li = document.createElement('li');
    li.className = 'flex items-start mb-2'; // Tailwind for li

    const label = document.createElement('label');
    label.className = 'flex items-start cursor-pointer w-full';

    const checkboxInput = document.createElement('input');
    checkboxInput.type = 'checkbox';
    checkboxInput.className = 'sr-only peer feather-task-checkbox'; // Added feather-task-checkbox for existing logic
    checkboxInput.checked = isChecked;
    checkboxInput.contentEditable = 'false';
    checkboxInput.setAttribute('role', 'checkbox');
    checkboxInput.setAttribute('aria-checked', String(isChecked));

    const visualCheckbox = document.createElement('div');
    visualCheckbox.className = 'w-5 h-5 border border-gray-300 dark:border-gray-600 rounded-sm mr-2 mt-1 flex-shrink-0 flex items-center justify-center peer-checked:bg-blue-500 peer-checked:border-blue-500 dark:peer-checked:bg-blue-500 dark:peer-checked:border-blue-500 transition-colors duration-150';
    
    const checkmarkSVG = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    checkmarkSVG.setAttribute('class', 'hidden peer-checked:block w-3 h-3 text-white fill-current');
    checkmarkSVG.setAttribute('viewBox', '0 0 20 20');
    checkmarkSVG.innerHTML = '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />';
    visualCheckbox.appendChild(checkmarkSVG);

    const contentElement = document.createElement('div');
    contentElement.className = 'feather-task-content flex-grow';
    contentElement.innerHTML = content;
    if (isChecked) {
      contentElement.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
    }
    
    label.appendChild(checkboxInput);
    label.appendChild(visualCheckbox);
    label.appendChild(contentElement);
    li.appendChild(label);
    
    return li;
  }
  
  /**
   * Convert a list to normal paragraphs
   * @param listElement The list element to convert
   */
  private convertListToParagraphs(listElement: HTMLElement | null): void {
    if (!listElement || !listElement.parentNode) return;
    
    // Get all list items
    const items = Array.from(listElement.querySelectorAll('li'));
    
    // Create a document fragment to hold the paragraphs
    const fragment = document.createDocumentFragment();
    
    // Convert each list item to a paragraph
    items.forEach(item => {
      const p = document.createElement('p');
      // If it's a checklist item, get the content from the content div
      const contentDiv = item.querySelector('.feather-task-content');
      p.innerHTML = contentDiv ? contentDiv.innerHTML : item.innerHTML;
      fragment.appendChild(p);
    });
    
    // Replace the list with the new paragraphs
    listElement.parentNode.replaceChild(fragment, listElement);
  }
  
  /**
   * Convert a regular list to a checklist
   * @param listElement The list element to convert
   */
  private convertListToChecklist(listElement: HTMLElement | null): void {
    if (!listElement || !listElement.parentNode) return;
    
    // Add the checklist class
    listElement.className = 'list-none pl-0'; // Tailwind for ul
    
    // Convert each list item to a checklist item
    const items = Array.from(listElement.querySelectorAll('li'));
    items.forEach(item => {
      // Preserve existing content, check if it's already a checklist item
      const existingCheckbox = item.querySelector('input[type="checkbox"].feather-task-checkbox') as HTMLInputElement;
      const existingContentDiv = item.querySelector('.feather-task-content');
      const contentHTML = existingContentDiv ? existingContentDiv.innerHTML : item.innerHTML;
      const isChecked = existingCheckbox ? existingCheckbox.checked : false;

      // Rebuild the item with the new structure
      item.innerHTML = ''; // Clear existing content
      const newChecklistItemStructure = this.createChecklistItem(contentHTML, isChecked);
      // Append children of the new li (which is the label) to the existing item
      while (newChecklistItemStructure.firstChild) {
        item.appendChild(newChecklistItemStructure.firstChild);
      }
      // Ensure the li itself has the correct classes if it was a plain li before
      item.className = 'flex items-start mb-2';
    });
  }
  
  /**
   * Handle clicks within the editor
   * @param event The click event
   */
  private handleEditorClick = (event: MouseEvent): void => {
    const target = event.target as HTMLElement;
    let checkboxInput: HTMLInputElement | null = null;

    // Check if the visual checkbox div or the input itself (if somehow clicked) was targeted
    if (target.classList.contains('peer') && target.matches('input[type="checkbox"].feather-task-checkbox')) {
      checkboxInput = target as HTMLInputElement;
    } else {
      // Check if a parent label was clicked, then find the peer input
      const label = target.closest('label');
      if (label) {
        checkboxInput = label.querySelector('input[type="checkbox"].feather-task-checkbox.peer') as HTMLInputElement;
      }
    }

    if (checkboxInput) {
      event.preventDefault(); // Prevent default if we are handling it
      
      // Important: Read current state *before* changing it
      const wasChecked = checkboxInput.checked; 
      
      // Toggle checkbox state
      checkboxInput.checked = !wasChecked;
      checkboxInput.setAttribute('aria-checked', String(!wasChecked));
      
      const listItem = checkboxInput.closest('li');
      if (listItem) {
        const contentElement = listItem.querySelector('.feather-task-content');
        if (contentElement) {
          if (!wasChecked) { // Item is now checked (completed)
            contentElement.classList.add('line-through', 'text-gray-500', 'dark:text-gray-400');
          } else { // Item is now unchecked
            contentElement.classList.remove('line-through', 'text-gray-500', 'dark:text-gray-400');
          }
        }
      }
      
      // Add guard clause for editor existence
      if (!this.editor) {
        console.warn('ChecklistPlugin: Editor instance not available in click handler.');
        return;
      }
      
      // Trigger input event for history
      this.editor.getElement().dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  };
  
  /**
   * Set up mutation observer to handle dynamically added checkboxes
   */
  private setupMutationObserver(): void {
    if (!this.editor) return;
    
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        // Check if nodes were added
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Look for newly added checkboxes
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              const checkboxes = element.querySelectorAll('.feather-task-checkbox');
              
              // Make checkboxes not directly editable
              checkboxes.forEach(checkbox => {
                if (checkbox instanceof HTMLElement) {
                  checkbox.contentEditable = 'false';
                  checkbox.setAttribute('role', 'checkbox');
                  checkbox.setAttribute('aria-checked', 'false');
                }
              });
            }
          });
        }
      });
    });
    
    // Start observing the editor element
    observer.observe(this.editor.getElement(), {
      childList: true,
      subtree: true,
    });
  }
  
  /**
   * Get block level elements in the current selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      // If we didn't find a block element, create a paragraph around the selection
      const paragraph = document.createElement('p');
      range.surroundContents(paragraph);
      return [paragraph];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
  
  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Remove editor click listener
    this.editor?.getElement().removeEventListener('click', this.handleEditorClick);
    
    // Clean up checkbox handlers
    this.checkboxHandlers.forEach((handler, element) => {
      element.removeEventListener('click', handler);
    });
    this.checkboxHandlers.clear();
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<ChecklistPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new ChecklistPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: ChecklistPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
