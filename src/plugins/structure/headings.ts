import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Headings plugin configuration
 */
const config: PluginConfig = {
  id: 'headings',
  name: 'Headings',
  description: 'Apply heading formatting to text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'headings',
      command: 'headings',
      icon: 'H1▼',
      label: 'Headings',
      tooltip: 'Headings (Ctrl/⌘+Alt+1-6)',
      group: 'structure',
      ariaLabel: 'Apply heading formatting',
      type: 'dropdown'
    }
  ],
  shortcuts: [
    {
      command: 'h1',
      key: '1',
      ctrlKey: true,
      altKey: true,
      description: 'Format as H1'
    },
    {
      command: 'h2',
      key: '2',
      ctrlKey: true,
      altKey: true,
      description: 'Format as H2'
    },
    {
      command: 'h3',
      key: '3',
      ctrlKey: true,
      altKey: true,
      description: 'Format as H3'
    },
    {
      command: 'h4',
      key: '4',
      ctrlKey: true,
      altKey: true,
      description: 'Format as H4'
    },
    {
      command: 'h5',
      key: '5',
      ctrlKey: true,
      altKey: true,
      description: 'Format as H5'
    },
    {
      command: 'h6',
      key: '6',
      ctrlKey: true,
      altKey: true,
      description: 'Format as H6'
    }
  ]
};

/**
 * Headings plugin implementation
 * Allows applying h1-h6 formatting to selected text
 */
export class HeadingsPlugin extends BasePlugin {
  private dropdown: HTMLElement | null = null;
  private isDropdownOpen = false;

  constructor() {
    super(config);
  }
  
  /**
   * Handle heading commands (h1-h6)
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (!this.editor) return;
    
    // Map command to heading level
    const headingMap: Record<string, string> = {
      'h1': 'h1',
      'h2': 'h2',
      'h3': 'h3',
      'h4': 'h4',
      'h5': 'h5',
      'h6': 'h6'
    };
    
    if (headingMap[_command]) {
      this.applyHeadingFormat(headingMap[_command]);
    } else if (_command === 'headings') {
      this.toggleHeadingDropdown();
    }
  }
  
  /**
   * Toggle the heading dropdown menu
   */
  private toggleHeadingDropdown(): void {
    if (this.isDropdownOpen) {
      this.closeHeadingDropdown();
    } else {
      this.openHeadingDropdown();
    }
  }
  
  /**
   * Open the heading dropdown menu
   */
  private openHeadingDropdown(): void {
    if (!this.editor || this.isDropdownOpen) return;
    
    // Create dropdown if it doesn't exist
    if (!this.dropdown) {
      this.dropdown = document.createElement('div');
      this.dropdown.className = 'absolute bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded shadow-md min-w-[150px] max-h-[250px] overflow-y-auto z-[1000] py-1';
      this.dropdown.setAttribute('role', 'menu');
      this.dropdown.setAttribute('aria-label', 'Heading options');
      
      // Add heading options
      const headings = ['Paragraph', 'Heading 1', 'Heading 2', 'Heading 3', 'Heading 4', 'Heading 5', 'Heading 6'];
      const elements = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
      
      const baseButtonClass = 'block w-full text-left px-[10px] py-[5px] border-none bg-transparent cursor-pointer text-gray-800 dark:text-slate-200 whitespace-nowrap hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-900 dark:hover:text-slate-50 focus:bg-gray-100 dark:focus:bg-slate-700 focus:text-gray-900 dark:focus:text-slate-50 focus:outline-none';

      headings.forEach((heading, index) => {
        const option = document.createElement('button');
        option.textContent = heading;
        option.setAttribute('role', 'menuitem');
        
        const element = elements[index];
        option.setAttribute('data-element', element);
        option.setAttribute('aria-label', `Format as ${heading}`);

        let previewClass = '';
        switch (element) {
          case 'p': previewClass = 'text-base font-normal'; break;
          case 'h1': previewClass = 'text-2xl font-bold'; break;
          case 'h2': previewClass = 'text-xl font-bold'; break;
          case 'h3': previewClass = 'text-lg font-bold'; break;
          case 'h4': previewClass = 'text-base font-bold'; break;
          case 'h5': previewClass = 'text-sm font-bold'; break;
          case 'h6': previewClass = 'text-xs font-bold'; break;
        }
        option.className = `${baseButtonClass} ${previewClass}`;

        option.addEventListener('click', () => {
          this.applyHeadingFormat(elements[index]);
          this.closeHeadingDropdown();
        });
        this.dropdown?.appendChild(option);
      });
      
      // Close on outside click
      document.addEventListener('click', this.handleOutsideClick);
    }
    
    // Find the toolbar button and position dropdown below it
    const toolbarButton = this.toolbarButtons.find(btn => 
      btn.getAttribute('data-command') === 'headings');
    
    if (toolbarButton) {
      // Remove inline styles for positioning
      /*
      this.dropdown.style.position = 'absolute';
      this.dropdown.style.top = `${toolbarButton.offsetTop + toolbarButton.offsetHeight}px`;
      this.dropdown.style.left = `${toolbarButton.offsetLeft}px`;
      */
      // Add dropdown to document
      document.body.appendChild(this.dropdown);
      this.isDropdownOpen = true;
    }
  }
  
  /**
   * Close the heading dropdown menu
   */
  private closeHeadingDropdown(): void {
    if (this.dropdown && this.isDropdownOpen) {
      document.body.removeChild(this.dropdown);
      this.isDropdownOpen = false;
    }
  }
  
  /**
   * Handle clicks outside the dropdown to close it
   */
  private handleOutsideClick = (event: MouseEvent): void => {
    if (this.isDropdownOpen && 
        this.dropdown && 
        !this.dropdown.contains(event.target as Node)) {
      this.closeHeadingDropdown();
    }
  };
  
  /**
   * Apply heading formatting to the current selection or block
   * @param elementType The HTML element type to apply (h1-h6 or p)
   */
  private applyHeadingFormat(elementType: string): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Find the containing block element
    let blockElement = range.commonAncestorContainer;
    if (blockElement.nodeType === Node.TEXT_NODE) {
      blockElement = blockElement.parentElement as Node;
    }
    
    // Traverse up to find the closest block element
    while (blockElement && 
           blockElement.parentElement && 
           blockElement.parentElement !== this.editor.getElement() && 
           !['P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'DIV'].includes(blockElement.nodeName)) {
      blockElement = blockElement.parentElement as Node;
    }
    
    if (!blockElement) return;
    
    // Create the new heading element
    const newElement = document.createElement(elementType);
    // Ensure blockElement is an HTMLElement before accessing innerHTML
    if (blockElement instanceof HTMLElement && newElement instanceof HTMLElement) {
      newElement.innerHTML = blockElement.innerHTML;
    }
    
    // Replace the old element with the new heading
    blockElement.parentNode?.replaceChild(newElement, blockElement);
    
    // Restore selection
    selection.removeAllRanges();
    const newRange = document.createRange();
    if (newElement.firstChild) {
      newRange.selectNodeContents(newElement);
      selection.addRange(newRange);
    }
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Clean up event listeners and dropdown when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    if (this.isDropdownOpen) {
      this.closeHeadingDropdown();
    }
    
    document.removeEventListener('click', this.handleOutsideClick);
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<HeadingsPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new HeadingsPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: HeadingsPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
