import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Outdent plugin configuration
 */
const config: PluginConfig = {
  id: 'outdent',
  name: 'Outdent',
  description: 'Decrease indentation of text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'outdent',
      command: 'outdent',
      icon: '⬅',
      label: 'Outdent',
      tooltip: 'Outdent (Shift+Tab)',
      group: 'structure',
      ariaLabel: 'Decrease indentation',
    }
  ],
  shortcuts: [
    {
      command: 'outdent',
      key: 'Tab',
      shiftKey: true,
      description: 'Decrease indentation'
    }
  ]
};

/**
 * Outdent plugin implementation
 * Decreases indentation level of selected blocks
 */
export class OutdentPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin and override shift+tab behavior
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Prevent default shift+tab behavior and use our outdent instead
    editor.getElement().addEventListener('keydown', this.handleShiftTabKey);
  }
  
  /**
   * Handle the shift+tab key to outdent instead of tabbing backward
   * @param event The keyboard event
   */
  private handleShiftTabKey = (event: KeyboardEvent): void => {
    // Only handle Tab key with Shift modifier
    if (event.key === 'Tab' && event.shiftKey && !event.ctrlKey && !event.altKey && !event.metaKey) {
      event.preventDefault();
      this.outdent();
    }
  };
  
  /**
   * Handle the outdent command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'outdent' && this.editor) {
      this.outdent();
    }
  }
  
  /**
   * Decrease indentation of selected blocks
   */
  private outdent(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const range = selection.getRangeAt(0);
    
    // Get all block elements in the selection
    const blocks = this.getBlocksInRange(range);
    
    // Apply outdentation to each block
    blocks.forEach(block => {
      this.outdentBlock(block);
    });
    
    // Trigger input event for history
    this.editor.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }
  
  /**
   * Outdent a single block element
   * @param block The block element to outdent
   */
  private outdentBlock(block: HTMLElement): void {
    const currentLevel = parseInt(block.getAttribute('data-indent-level') || '0', 10);

    if (currentLevel > 0) {
      // Decrease the level
      const newLevel = currentLevel - 1;
      if (newLevel > 0) {
        block.setAttribute('data-indent-level', newLevel.toString());
      } else {
        block.removeAttribute('data-indent-level');
      }
    }
  }
  
  /**
   * Get all block elements within the selection range
   * @param range The current selection range
   * @returns Array of block elements
   */
  private getBlocksInRange(range: Range): HTMLElement[] {
    if (!this.editor) return [];
    
    // If range is collapsed, get the closest block element
    if (range.collapsed) {
      let node = range.startContainer;
      if (node.nodeType === Node.TEXT_NODE) {
        node = node.parentElement as Node;
      }
      
      // Find the closest block element
      while (node && 
             node !== this.editor.getElement() && 
             !this.isBlockElement(node as HTMLElement)) {
        node = node.parentElement as Node;
      }
      
      // If we found a valid block element, return it
      if (node && node !== this.editor.getElement()) {
        return [node as HTMLElement];
      }
      
      return [];
    }
    
    // For non-collapsed ranges, we need to get all block elements within the range
    const blocks: HTMLElement[] = [];
    
    // If the range spans multiple blocks, find all block elements
    const treeWalker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Node) => {
          const element = node as HTMLElement;
          if (this.isBlockElement(element) && this.isElementInRange(element, range)) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        }
      }
    );
    
    let currentNode = treeWalker.nextNode();
    while (currentNode) {
      blocks.push(currentNode as HTMLElement);
      currentNode = treeWalker.nextNode();
    }
    
    // If no blocks found, try to work with the common ancestor
    if (blocks.length === 0) {
      let ancestor = range.commonAncestorContainer;
      if (ancestor.nodeType === Node.TEXT_NODE) {
        ancestor = ancestor.parentElement as Node;
      }
      
      // Find the closest block element
      while (ancestor && 
             ancestor !== this.editor.getElement() && 
             !this.isBlockElement(ancestor as HTMLElement)) {
        ancestor = ancestor.parentElement as Node;
      }
      
      if (ancestor && ancestor !== this.editor.getElement()) {
        blocks.push(ancestor as HTMLElement);
      }
    }
    
    return blocks;
  }
  
  /**
   * Check if a node is a block-level element
   * @param element The element to check
   * @returns True if the element is a block element
   */
  private isBlockElement(element: HTMLElement): boolean {
    // Common block elements
    const blockTags = [
      'ADDRESS', 'ARTICLE', 'ASIDE', 'BLOCKQUOTE', 'DETAILS', 'DIALOG', 
      'DD', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE', 'FOOTER',
      'FORM', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER', 'HGROUP', 'HR',
      'LI', 'MAIN', 'NAV', 'OL', 'P', 'PRE', 'SECTION', 'TABLE', 'UL'
    ];
    
    return blockTags.includes(element.tagName);
  }
  
  /**
   * Check if an element is fully or partially within a range
   * @param element The element to check
   * @param range The range to check against
   * @returns True if the element intersects with the range
   */
  private isElementInRange(element: HTMLElement, range: Range): boolean {
    const nodeRange = document.createRange();
    nodeRange.selectNode(element);
    
    return range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0 &&
           range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0;
  }
  
  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Remove the shift+tab key handler
    this.editor?.getElement().removeEventListener('keydown', this.handleShiftTabKey);
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<OutdentPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new OutdentPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: OutdentPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
