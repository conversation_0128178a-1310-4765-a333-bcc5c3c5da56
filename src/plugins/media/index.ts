import type { Editor, Plugin } from '../../types';
import { createPlugin } from '../plugin-factory';

// Import all media plugins
import linkPlugin from './link';
import imagePlugin from './image';
import videoPlugin from './video';
import audioPlugin from './audio';
import attachmentPlugin from './attachment';
import emojiPlugin from './emoji';
import specialCharPlugin from './special-char';
import chartPlugin from './chart';
import mathPlugin from './math';
import snippetPlugin from './snippet';

// This file serves as the entry point for all media plugins
// Each plugin is exported individually to ensure tree-shaking works properly

// Export individual plugins
export { 
  linkPlugin,
  imagePlugin,
  videoPlugin,
  audioPlugin,
  attachmentPlugin,
  emojiPlugin,
  specialCharPlugin,
  chartPlugin,
  mathPlugin,
  snippetPlugin
};

// Export a combined plugin group for convenience
export const MediaPlugins: Plugin[] = [
  linkPlugin,
  imagePlugin,
  videoPlugin,
  audioPlugin,
  attachmentPlugin,
  emojiPlugin,
  specialCharPlugin,
  chartPlugin,
  mathPlugin,
  snippetPlugin
];

// Create a group plugin that registers all media plugins
// This allows for easily registering all media plugins at once
const groupPlugin: Plugin = createPlugin(
  'media-group',
  (editor: Editor) => {
    // Initialize all media plugins
    MediaPlugins.forEach(plugin => {
      plugin.init(editor);
    });
    
    // Return null since there's no instance to track
    return null;
  },
  () => {
    // Clean up all media plugins
    MediaPlugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
);

// Default export for convenience
export default groupPlugin;
