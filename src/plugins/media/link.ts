import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Link plugin configuration
 */
const config: PluginConfig = {
  id: 'link',
  name: '<PERSON>',
  description: 'Insert and manage hyperlinks',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'link',
      command: 'link',
      icon: '🔗',
      label: 'Link',
      tooltip: 'Insert link (Ctrl/⌘+K)',
      group: 'media',
      ariaLabel: 'Insert or edit link',
    }
  ],
  shortcuts: [
    {
      command: 'link',
      key: 'k',
      ctrlKey: true,
      description: 'Insert or edit link'
    }
  ]
};

/**
 * Link plugin implementation
 * Adds ability to insert, edit, and remove hyperlinks
 */
export class LinkPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin and add CSS
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Add event listener for clicks on links
    if (this.editor) {
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.addEventListener('click', this.handleLinkClick);
      }
    }
  }
  
  /**
   * Handle the link command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'link' && this.editor) {
      this.handleLinkAction();
    } else if (_command === 'unlink' && this.editor) {
      this.removeLink();
    }
  }
  
  /**
   * Handle link insertion or editing
   */
  private handleLinkAction(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    // Check if we're editing an existing link
    const existingLink = this.findLinkInSelection(selection);
    
    if (existingLink) {
      // Edit the existing link
      this.openLinkDialog(
        existingLink.getAttribute('href') || '', 
        existingLink.textContent || '',
        existingLink.getAttribute('target') || '_self',
        existingLink
      );
    } else {
      // Insert a new link
      const selectedText = selection.toString();
      this.openLinkDialog('', selectedText);
    }
  }
  
  /**
   * Find if the current selection is within or contains a link
   * @param selection The current selection
   * @returns The link element or null
   */
  private findLinkInSelection(selection: Selection): HTMLAnchorElement | null {
    if (!selection.rangeCount) return null;
    
    const range = selection.getRangeAt(0);
    
    // Check if a node in the selection is a link
    let node = range.commonAncestorContainer;
    
    // If the node is a text node, get its parent
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentNode as Node;
    }
    
    // Check if the node or any of its parents is a link
    while (node && node !== this.editor?.getElement()) {
      if (node.nodeName === 'A') {
        return node as HTMLAnchorElement;
      }
      node = node.parentNode as Node;
    }
    
    return null;
  }
  
  /**
   * Open the link dialog
   * @param url The URL for the link
   * @param text The display text
   * @param target The target attribute value (_self, _blank)
   * @param existingLink The existing link element if we're editing
   */
  private openLinkDialog(
    url: string,
    text: string,
    target: string = '_self',
    existingLink: HTMLAnchorElement | null = null
  ): void {
    if (this.isDialogOpen) {
      this.closeLinkDialog();
    }
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/30 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 min-w-[400px] max-w-[80vw] z-[9999]'; // Tailwind for dialog
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'link-dialog-title');
    
    // Create dialog content programmatically
    const dialogTitle = document.createElement('h3');
    dialogTitle.id = 'link-dialog-title';
    dialogTitle.className = 'text-lg font-semibold mt-0 mb-4 text-gray-900 dark:text-slate-100';
    dialogTitle.textContent = existingLink ? 'Edit Link' : 'Insert Link';
    this.dialog.appendChild(dialogTitle);

    const form = document.createElement('form');
    form.className = 'flex flex-col gap-3';

    const createField = (labelText: string, inputId: string, inputType: string, placeholder: string, value: string) => {
      const div = document.createElement('div');
      const label = document.createElement('label') as HTMLLabelElement;
      label.htmlFor = inputId;
      label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
      label.textContent = labelText;
      const input = document.createElement('input');
      input.type = inputType; input.id = inputId;
      input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 text-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
      input.placeholder = placeholder; input.value = value;
      div.appendChild(label); div.appendChild(input);
      return div;
    };

    form.appendChild(createField('URL', 'link-url', 'url', 'https://example.com', url));
    form.appendChild(createField('Display Text', 'link-text', 'text', 'Link text', text));

    const targetDiv = document.createElement('div');
    const targetLabel = document.createElement('label') as HTMLLabelElement;
    targetLabel.htmlFor = 'link-target';
    targetLabel.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    targetLabel.textContent = 'Open in';
    const targetSelect = document.createElement('select');
    targetSelect.id = 'link-target';
    targetSelect.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 text-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    const optionSelf = document.createElement('option');
    optionSelf.value = '_self'; optionSelf.textContent = 'Same window';
    if (target === '_self') optionSelf.selected = true;
    const optionBlank = document.createElement('option');
    optionBlank.value = '_blank'; optionBlank.textContent = 'New window';
    if (target === '_blank') optionBlank.selected = true;
    targetSelect.appendChild(optionSelf); targetSelect.appendChild(optionBlank);
    targetDiv.appendChild(targetLabel); targetDiv.appendChild(targetSelect);
    form.appendChild(targetDiv);

    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'flex justify-end gap-2 mt-4';

    if (existingLink) {
      const removeButtonElem = document.createElement('button');
      removeButtonElem.type = 'button'; removeButtonElem.id = 'link-remove-button';
      removeButtonElem.className = 'py-2 px-4 rounded text-sm font-medium bg-red-500 hover:bg-red-600 text-white border-red-500 hover:border-red-600';
      removeButtonElem.textContent = 'Remove Link';
      buttonsContainer.appendChild(removeButtonElem);
    }

    const cancelButtonElem = document.createElement('button');
    cancelButtonElem.type = 'button'; cancelButtonElem.id = 'link-cancel-button';
    cancelButtonElem.className = 'py-2 px-4 rounded text-sm font-medium bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200 border border-gray-300 dark:border-slate-500';
    cancelButtonElem.textContent = 'Cancel';
    buttonsContainer.appendChild(cancelButtonElem);

    const saveButtonElem = document.createElement('button');
    saveButtonElem.type = 'button'; saveButtonElem.id = 'link-save-button';
    saveButtonElem.className = 'py-2 px-4 rounded text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700';
    saveButtonElem.textContent = 'Save';
    buttonsContainer.appendChild(saveButtonElem);
    
    form.appendChild(buttonsContainer);
    this.dialog.appendChild(form);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Focus the URL field
    const urlInput = this.dialog.querySelector('#link-url') as HTMLInputElement;
    urlInput.focus();
    
    // Add event listeners
    const cancelButton = this.dialog.querySelector('#link-cancel-button');
    const saveButton = this.dialog.querySelector('#link-save-button');
    const removeButton = this.dialog.querySelector('#link-remove-button');
    
    cancelButton?.addEventListener('click', () => this.closeLinkDialog());
    backdrop.addEventListener('click', () => this.closeLinkDialog());
    
    saveButton?.addEventListener('click', () => {
      const urlValue = (this.dialog?.querySelector('#link-url') as HTMLInputElement)?.value || '';
      const textValue = (this.dialog?.querySelector('#link-text') as HTMLInputElement)?.value || '';
      const targetValue = (this.dialog?.querySelector('#link-target') as HTMLSelectElement)?.value || '_self';
      
      if (urlValue) {
        if (existingLink) {
          // Update existing link
          this.updateLink(existingLink, urlValue, textValue, targetValue);
        } else {
          // Create new link
          this.insertLink(urlValue, textValue, targetValue);
        }
      }
      
      this.closeLinkDialog();
    });
    
    if (removeButton) {
      removeButton.addEventListener('click', () => {
        if (existingLink) {
          this.removeSpecificLink(existingLink);
        }
        this.closeLinkDialog();
      });
    }
    
    // Handle form submission by using the 'form' variable defined in this scope
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      const saveBtn = saveButtonElem as HTMLButtonElement; // Use saveButtonElem which is already typed as HTMLButtonElement
      saveBtn?.click();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeLinkDialog();
    }
  };
  
  /**
   * Close the link dialog
   */
  private closeLinkDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog and backdrop
    const backdrop = document.querySelector('.feather-link-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Insert a new link
   * @param url The URL for the link
   * @param text The display text
   * @param target The target attribute value
   */
  private insertLink(url: string, text: string, target: string): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    
    // Create the link
    const linkElement = document.createElement('a');
    linkElement.href = url;
    linkElement.className = 'text-blue-600 dark:text-blue-400 underline cursor-pointer';
    linkElement.target = target;
    linkElement.rel = target === '_blank' ? 'noopener noreferrer' : '';
    linkElement.textContent = text || url;
    
    // Delete any selected content
    range.deleteContents();
    
    // Insert the link
    range.insertNode(linkElement);
    
    // Move cursor after the link
    selection.removeAllRanges();
    const newRange = document.createRange();
    newRange.setStartAfter(linkElement);
    newRange.collapse(true);
    selection.addRange(newRange);
    
    // Trigger input event for history
    const editorElement = this.editor!.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Update an existing link
   * @param linkElement The link element to update
   * @param url The new URL
   * @param text The new display text
   * @param target The new target attribute value
   */
  private updateLink(
    linkElement: HTMLAnchorElement, 
    url: string, 
    text: string, 
    target: string
  ): void {
    if (!this.editor) return;
    
    linkElement.href = url;
    if (text) {
      linkElement.textContent = text;
    }
    linkElement.target = target;
    linkElement.rel = target === '_blank' ? 'noopener noreferrer' : '';
    
    // Trigger input event for history
    const editorElement = this.editor!.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Remove a specific link
   * @param linkElement The link element to remove
   */
  private removeSpecificLink(linkElement: HTMLAnchorElement): void {
    if (!this.editor) return;
    
    // Get the link's text content
    const textContent = linkElement.textContent;
    
    // Create a text node with the content
    const textNode = document.createTextNode(textContent || '');
    
    // Replace the link with the text node
    if (linkElement.parentNode) {
      linkElement.parentNode.replaceChild(textNode, linkElement);
      
      // Set selection after the inserted text
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        const range = document.createRange();
        range.setStartAfter(textNode);
        range.collapse(true);
        selection.addRange(range);
      }
      
      // Trigger input event for history
      const editorElement = this.editor!.getElement();
      if (editorElement) {
        editorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
      }
    }
  }
  
  /**
   * Remove the link in the current selection
   */
  private removeLink(): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection) return;
    
    const link = this.findLinkInSelection(selection);
    if (link) {
      this.removeSpecificLink(link);
    }
  }
  
  /**
   * Handle clicks on links within the editor
   */
  private handleLinkClick = (event: MouseEvent): void => {
    if (!this.editor) return;
    
    // Check if a link was clicked
    const target = event.target as Node;
    let linkElement: HTMLElement | null = null;
    
    if (target.nodeName === 'A') {
      linkElement = target as HTMLElement;
    } else if (target.parentElement?.nodeName === 'A') {
      linkElement = target.parentElement;
    }
    
    if (linkElement) {
      // If Ctrl/Cmd key is pressed, let the browser handle navigation
      if (event.ctrlKey || event.metaKey) {
        return;
      }
      
      // Otherwise, prevent default and handle the link click
      event.preventDefault();
      
      // If the user is holding Alt/Option, edit the link
      if (event.altKey) {
        // Select the link
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          const range = document.createRange();
          range.selectNode(linkElement);
          selection.addRange(range);
          
          // Open the edit dialog
          this.handleLinkAction();
        }
      }
    }
  };
  
  /**
   * Clean up event listeners when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeLinkDialog();
    }
    
    // Remove link click handler
    if (this.editor) {
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.removeEventListener('click', this.handleLinkClick);
      }
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<LinkPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new LinkPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: LinkPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
