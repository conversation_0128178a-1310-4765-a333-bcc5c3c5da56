import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Attachment plugin configuration
 */
const config: PluginConfig = {
  id: 'attachment',
  name: 'Attachment',
  description: 'Insert file attachments and downloads',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'attachment',
      command: 'attachment',
      icon: '📎',
      label: 'Attachment',
      tooltip: 'Insert file attachment',
      group: 'media',
      ariaLabel: 'Insert file attachment',
    }
  ],
  shortcuts: [] // No keyboard shortcuts to avoid conflicts
};

/**
 * Attachment plugin implementation
 * Allows adding downloadable file attachments inline
 */
export class AttachmentPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the attachment command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'attachment' && this.editor) {
      this.openAttachmentDialog();
    }
  }
  
  /**
   * Open the attachment insert/edit dialog
   * @param existingAttachment Existing attachment element to edit
   */
  private openAttachmentDialog(existingAttachment: HTMLElement | null = null): void {
    if (this.isDialogOpen) {
      this.closeAttachmentDialog();
    }
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/50 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 min-w-[400px] max-w-[80vw] z-[9999]'; // Tailwind for dialog
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'attachment-dialog-title');
    
    // Get current values if editing an existing attachment
    let currentUrl = '';
    let currentName = '';
    let currentSize = '';
    
    if (existingAttachment) {
      const link = existingAttachment.querySelector('a');
      if (link) {
        currentUrl = link.href;
        
        const nameElement = existingAttachment.querySelector('.feather-attachment-name');
        if (nameElement) {
          currentName = nameElement.textContent || '';
        }
        
        const sizeElement = existingAttachment.querySelector('.feather-attachment-size');
        if (sizeElement) {
          currentSize = sizeElement.getAttribute('data-size') || '';
        }
      }
    }
    
    // Create dialog content programmatically
    const dialogTitle = document.createElement('h3');
    dialogTitle.id = 'attachment-dialog-title';
    dialogTitle.className = 'text-lg font-semibold mt-0 mb-4 text-gray-900 dark:text-slate-100';
    dialogTitle.textContent = `${existingAttachment ? 'Edit' : 'Insert'} Attachment`;
    this.dialog.appendChild(dialogTitle);

    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'flex border-b border-gray-200 dark:border-slate-700 mb-4'; // Tailwind for tabs container
    
    const urlTabButton = document.createElement('button');
    urlTabButton.type = 'button';
    urlTabButton.className = 'px-4 py-2 -mb-px border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400 font-medium text-sm focus:outline-none'; // Active tab
    urlTabButton.dataset.tab = 'url';
    urlTabButton.textContent = 'URL';
    
    const uploadTabButton = document.createElement('button');
    uploadTabButton.type = 'button';
    uploadTabButton.className = 'px-4 py-2 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 font-medium text-sm focus:outline-none border-b-2 border-transparent hover:border-gray-300 dark:hover:border-slate-600'; // Inactive tab
    uploadTabButton.dataset.tab = 'upload';
    uploadTabButton.textContent = 'Upload';

    tabsContainer.appendChild(urlTabButton);
    tabsContainer.appendChild(uploadTabButton);
    this.dialog.appendChild(tabsContainer);

    // URL Panel
    const urlPanel = document.createElement('div');
    urlPanel.className = 'feather-attachment-dialog-panel active'; // Tailwind for panel, active by default
    urlPanel.dataset.panel = 'url';
    const urlForm = document.createElement('form');
    urlForm.className = 'flex flex-col gap-3'; // Tailwind for form

    // URL Field
    let div = document.createElement('div');
    let label = document.createElement('label') as HTMLLabelElement;
    label.htmlFor = 'attachment-url';
    label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    label.textContent = 'File URL';
    let input = document.createElement('input');
    input.type = 'url'; input.id = 'attachment-url';
    input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    input.placeholder = 'https://example.com/file.pdf'; input.value = currentUrl;
    div.appendChild(label); div.appendChild(input); urlForm.appendChild(div);

    // Display Name Field
    div = document.createElement('div');
    label = document.createElement('label') as HTMLLabelElement;
    label.htmlFor = 'attachment-name';
    label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    label.textContent = 'Display Name';
    input = document.createElement('input');
    input.type = 'text'; input.id = 'attachment-name';
    input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    input.placeholder = 'My Document'; input.value = currentName;
    div.appendChild(label); div.appendChild(input); urlForm.appendChild(div);
    
    // File Size Field
    div = document.createElement('div');
    label = document.createElement('label') as HTMLLabelElement;
    label.htmlFor = 'attachment-size';
    label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    label.textContent = 'File Size (Optional)';
    input = document.createElement('input');
    input.type = 'text'; input.id = 'attachment-size';
    input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    input.placeholder = 'e.g. 2.5 MB'; input.value = currentSize;
    div.appendChild(label); div.appendChild(input);
    let p = document.createElement('p');
    p.className = 'mt-1 text-xs text-gray-500 dark:text-slate-400';
    p.textContent = 'For display purposes only, e.g. "2.5 MB"';
    div.appendChild(p); urlForm.appendChild(div);
    
    urlPanel.appendChild(urlForm);
    this.dialog.appendChild(urlPanel);

    // Upload Panel
    const uploadPanel = document.createElement('div');
    uploadPanel.className = 'feather-attachment-dialog-panel hidden'; // Tailwind for panel, hidden by default
    uploadPanel.dataset.panel = 'upload';
    const uploadForm = document.createElement('form');
    uploadForm.className = 'flex flex-col gap-3';

    // Dropzone
    div = document.createElement('div');
    div.id = 'attachment-dropzone';
    div.className = 'border-2 border-dashed border-gray-300 dark:border-slate-600 rounded-md p-6 text-center cursor-pointer hover:border-blue-500 dark:hover:border-blue-400';
    p = document.createElement('p');
    p.className = 'text-gray-500 dark:text-slate-400';
    p.textContent = 'Drag a file here or click to upload';
    div.appendChild(p);
    input = document.createElement('input');
    input.type = 'file'; input.id = 'attachment-file'; input.className = 'hidden';
    div.appendChild(input); uploadForm.appendChild(div);

    // Upload Display Name Field
    div = document.createElement('div');
    label = document.createElement('label') as HTMLLabelElement;
    label.htmlFor = 'attachment-name-upload';
    label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
    label.textContent = 'Display Name (Optional)';
    input = document.createElement('input');
    input.type = 'text'; input.id = 'attachment-name-upload';
    input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    input.placeholder = 'My Document';
    div.appendChild(label); div.appendChild(input);
    p = document.createElement('p');
    p.className = 'mt-1 text-xs text-gray-500 dark:text-slate-400';
    p.textContent = 'Leave empty to use the filename';
    div.appendChild(p); uploadForm.appendChild(div);

    uploadPanel.appendChild(uploadForm);
    this.dialog.appendChild(uploadPanel);

    // Buttons Container
    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'flex justify-end gap-2 mt-6 pt-4 border-t border-gray-200 dark:border-slate-700';
    
    if (existingAttachment) {
      const removeButtonElem = document.createElement('button');
      removeButtonElem.type = 'button';
      removeButtonElem.id = 'attachment-remove-button';
      removeButtonElem.className = 'py-2 px-4 rounded font-medium bg-red-500 hover:bg-red-600 text-white';
      removeButtonElem.textContent = 'Remove Attachment';
      buttonsContainer.appendChild(removeButtonElem);
    }

    const cancelButtonElem = document.createElement('button');
    cancelButtonElem.type = 'button';
    cancelButtonElem.id = 'attachment-cancel-button';
    cancelButtonElem.className = 'py-2 px-4 rounded font-medium bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200 border border-gray-300 dark:border-slate-500';
    cancelButtonElem.textContent = 'Cancel';
    buttonsContainer.appendChild(cancelButtonElem);

    const insertButtonElem = document.createElement('button');
    insertButtonElem.type = 'button';
    insertButtonElem.id = 'attachment-insert-button';
    insertButtonElem.className = 'py-2 px-4 rounded font-medium bg-blue-600 hover:bg-blue-700 text-white';
    insertButtonElem.textContent = existingAttachment ? 'Update' : 'Insert';
    buttonsContainer.appendChild(insertButtonElem);
    
    this.dialog.appendChild(buttonsContainer);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Set up tab switching
    const tabs = this.dialog.querySelectorAll('.feather-attachment-dialog-tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Deactivate all tabs and panels
        tabs.forEach(t => t.classList.remove('active'));
        const panels = this.dialog?.querySelectorAll('.feather-attachment-dialog-panel');
        panels?.forEach(p => p.classList.remove('active'));
        
        // Activate the clicked tab and its panel
        tab.classList.add('active');
        const panelId = tab.getAttribute('data-tab');
        const panel = this.dialog?.querySelector(`.feather-attachment-dialog-panel[data-panel="${panelId}"]`);
        panel?.classList.add('active');
      });
    });
    
    // Set up file upload
    const dropzone = this.dialog.querySelector('#attachment-dropzone');
    const fileInput = this.dialog.querySelector('#attachment-file') as HTMLInputElement;
    
    dropzone?.addEventListener('click', () => {
      fileInput?.click();
    });
    
    dropzone?.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropzone.classList.add('dragover');
    });
    
    dropzone?.addEventListener('dragleave', () => {
      dropzone.classList.remove('dragover');
    });
    
    dropzone?.addEventListener('drop', (e: Event) => {
      const dragEvent = e as DragEvent;
      dragEvent.preventDefault();
      dragEvent.stopPropagation();
      dropzone.classList.remove('dragover');
      
      if (dragEvent.dataTransfer?.files.length) {
        fileInput.files = dragEvent.dataTransfer.files;
        
        // Update the dropzone text with the filename
        const filename = dragEvent.dataTransfer.files[0].name;
        const nameInput = this.dialog?.querySelector('#attachment-name-upload') as HTMLInputElement;
        if (nameInput && !nameInput.value) {
          nameInput.value = filename;
        }
        
        const dropzoneText = dropzone.querySelector('p');
        if (dropzoneText) {
          dropzoneText.textContent = `Selected: ${filename}`;
        }
      }
    });
    
    fileInput?.addEventListener('change', () => {
      // Visual feedback that file was selected
      if (fileInput.files?.length) {
        const filename = fileInput.files[0].name;
        
        // Auto-fill the name field if empty
        const nameInput = this.dialog?.querySelector('#attachment-name-upload') as HTMLInputElement;
        if (nameInput && !nameInput.value) {
          nameInput.value = filename;
        }
        
        const dropzoneText = dropzone?.querySelector('p');
        if (dropzoneText) {
          dropzoneText.textContent = `Selected: ${filename}`;
        }
      }
    });
    
    // Set up button actions
    const cancelButton = this.dialog.querySelector('#attachment-cancel-button');
    const insertButton = this.dialog.querySelector('#attachment-insert-button');
    const removeButton = this.dialog.querySelector('#attachment-remove-button');
    
    cancelButton?.addEventListener('click', () => this.closeAttachmentDialog());
    backdrop.addEventListener('click', () => this.closeAttachmentDialog());
    
    insertButton?.addEventListener('click', () => {
      const activePanel = this.dialog?.querySelector('.feather-attachment-dialog-panel.active');
      const panelId = activePanel?.getAttribute('data-panel');
      
      if (panelId === 'url') {
        // Get values from URL form
        const url = (this.dialog?.querySelector('#attachment-url') as HTMLInputElement)?.value || '';
        const name = (this.dialog?.querySelector('#attachment-name') as HTMLInputElement)?.value || '';
        const size = (this.dialog?.querySelector('#attachment-size') as HTMLInputElement)?.value || '';
        
        if (url) {
          if (existingAttachment) {
            // Update existing attachment
            this.updateAttachment(existingAttachment, url, name, size);
          } else {
            // Insert new attachment
            this.insertAttachment(url, name, size);
          }
        }
      } else if (panelId === 'upload') {
        // Get values from upload form
        const fileInput = this.dialog?.querySelector('#attachment-file') as HTMLInputElement;
        const name = (this.dialog?.querySelector('#attachment-name-upload') as HTMLInputElement)?.value || '';
        
        if (fileInput?.files?.length) {
          const file = fileInput.files[0];
          this.handleFileUpload(file, name, existingAttachment);
        }
      }
      
      this.closeAttachmentDialog();
    });
    
    if (removeButton && existingAttachment) {
      removeButton.addEventListener('click', () => {
        this.removeAttachment(existingAttachment);
        this.closeAttachmentDialog();
      });
    }
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Focus the URL input if showing URL tab
    const urlInput = this.dialog.querySelector('#attachment-url') as HTMLInputElement;
    if (urlInput) {
      urlInput.focus();
    }
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeAttachmentDialog();
    }
  };
  
  /**
   * Close the attachment dialog
   */
  private closeAttachmentDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog and backdrop
    const backdrop = document.querySelector('.feather-attachment-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Format file size for display
   * @param bytes File size in bytes
   * @returns Formatted file size string
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * Get file icon based on file type/extension
   * @param url File URL or name
   * @returns Icon representation
   */
  private getFileIcon(url: string): string {
    // Extract filename from URL
    const filename = url.split('/').pop() || '';
    
    // Get extension
    const extension = filename.split('.').pop()?.toLowerCase() || '';
    
    // Return appropriate icon
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xls':
      case 'xlsx':
        return '📊';
      case 'ppt':
      case 'pptx':
        return '📋';
      case 'zip':
      case 'rar':
      case '7z':
        return '📦';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return '🖼️';
      case 'mp3':
      case 'wav':
      case 'ogg':
        return '🎵';
      case 'mp4':
      case 'avi':
      case 'mov':
        return '🎬';
      case 'txt':
        return '📄';
      default:
        return '📎';
    }
  }
  
  /**
   * Insert a new attachment
   * @param url The attachment URL
   * @param name The display name
   * @param size The file size
   */
  private insertAttachment(
    url: string,
    name: string = '',
    size: string = ''
  ): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    
    // Create the display name
    const displayName = name || url.split('/').pop() || 'Download';
    
    // Create attachment element
    const attachment = document.createElement('div');
    attachment.className = 'inline-flex items-center gap-2 py-2 px-3 bg-gray-100 dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded my-1 transition-colors duration-200 max-w-full hover:bg-blue-50 dark:hover:bg-slate-600';
    attachment.setAttribute('contenteditable', 'false');
    
    // Add the file icon
    const iconSpan = document.createElement('span');
    iconSpan.className = 'text-xl text-gray-500 dark:text-gray-400';
    iconSpan.textContent = this.getFileIcon(url);
    attachment.appendChild(iconSpan);
    
    // Add file info
    const infoDiv = document.createElement('div');
    infoDiv.className = 'flex-1 min-w-0';
    
    const nameDiv = document.createElement('div');
    nameDiv.className = 'font-medium whitespace-nowrap overflow-hidden text-ellipsis text-sm text-gray-800 dark:text-slate-100';
    nameDiv.textContent = displayName;
    infoDiv.appendChild(nameDiv);
    
    if (size) {
      const metaDiv = document.createElement('div');
      metaDiv.className = 'text-xs text-gray-500 dark:text-gray-400'; // Applied to parent of sizeSpan
      
      const sizeSpan = document.createElement('span');
      // sizeSpan.className = 'feather-attachment-size'; // No specific Tailwind class, styling inherited from metaDiv
      sizeSpan.textContent = size;
      sizeSpan.setAttribute('data-size', size);
      
      metaDiv.appendChild(sizeSpan);
      infoDiv.appendChild(metaDiv);
    }
    
    attachment.appendChild(infoDiv);
    
    // Add actions
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'flex gap-1';
    
    // Download button
    const downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = displayName;
    downloadLink.className = 'w-6 h-6 flex items-center justify-center rounded-full cursor-pointer transition-colors duration-200 hover:bg-gray-300 dark:hover:bg-slate-500 text-blue-600 dark:text-blue-400';
    downloadLink.textContent = '⬇️';
    downloadLink.setAttribute('aria-label', 'Download file');
    downloadLink.setAttribute('title', 'Download');
    downloadLink.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent edit dialog from opening
    });
    actionsDiv.appendChild(downloadLink);
    
    // Edit button
    const editButton = document.createElement('div');
    editButton.className = 'w-6 h-6 flex items-center justify-center rounded-full cursor-pointer transition-colors duration-200 hover:bg-gray-300 dark:hover:bg-slate-500 text-gray-600 dark:text-gray-400';
    editButton.textContent = '✏️';
    editButton.setAttribute('aria-label', 'Edit attachment');
    editButton.setAttribute('title', 'Edit');
    editButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.openAttachmentDialog(attachment);
    });
    actionsDiv.appendChild(editButton);
    
    attachment.appendChild(actionsDiv);
    
    // Make the entire attachment clickable to download
    attachment.addEventListener('click', (e) => {
      // Only handle click if not on one of the action buttons
      if (!(e.target as Element).closest('.feather-attachment-action')) {
        downloadLink.click();
      }
    });
    
    // Delete any selected content
    range.deleteContents();
    
    // Insert the attachment
    range.insertNode(attachment);
    
    // Move cursor after the attachment
    selection.removeAllRanges();
    const newRange = document.createRange();
    newRange.setStartAfter(attachment);
    newRange.collapse(true);
    selection.addRange(newRange);
    
    // Trigger input event for history
    const editorElement = this.editor.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Update an existing attachment
   * @param attachmentElement The attachment element
   * @param url The new URL
   * @param name The new display name
   * @param size The new file size
   */
  private updateAttachment(
    attachmentElement: HTMLElement,
    url: string,
    name: string = '',
    size: string = ''
  ): void {
    if (!this.editor) return;
    
    // Create the display name
    const displayName = name || url.split('/').pop() || 'Download';
    
    // Update the download link
    const downloadLink = attachmentElement.querySelector('.feather-attachment-download') as HTMLAnchorElement;
    if (downloadLink) {
      downloadLink.href = url;
      downloadLink.download = displayName;
    }
    
    // Update the file icon
    const iconSpan = attachmentElement.querySelector('.feather-attachment-icon');
    if (iconSpan) {
      iconSpan.textContent = this.getFileIcon(url);
    }
    
    // Update the file name
    const nameDiv = attachmentElement.querySelector('.feather-attachment-name');
    if (nameDiv) {
      nameDiv.textContent = displayName;
    }
    
    // Update the file size
    let sizeSpan = attachmentElement.querySelector('.feather-attachment-size');
    let metaDiv = attachmentElement.querySelector('.feather-attachment-meta');
    
    if (size) {
      if (sizeSpan) {
        sizeSpan.textContent = size;
        sizeSpan.setAttribute('data-size', size);
      } else if (metaDiv) {
        // Create size span if it doesn't exist
        sizeSpan = document.createElement('span');
        sizeSpan.className = 'feather-attachment-size';
        sizeSpan.textContent = size;
        sizeSpan.setAttribute('data-size', size);
        metaDiv.appendChild(sizeSpan);
      } else {
        // Create meta div and size span
        const infoDiv = attachmentElement.querySelector('.feather-attachment-info');
        if (infoDiv) {
          metaDiv = document.createElement('div');
          metaDiv.className = 'feather-attachment-meta';
          
          sizeSpan = document.createElement('span');
          sizeSpan.className = 'feather-attachment-size';
          sizeSpan.textContent = size;
          sizeSpan.setAttribute('data-size', size);
          
          metaDiv.appendChild(sizeSpan);
          infoDiv.appendChild(metaDiv);
        }
      }
    } else if (sizeSpan) {
      // Remove size span if no size
      sizeSpan.parentNode?.removeChild(sizeSpan);
      
      // Remove meta div if empty
      if (metaDiv && !metaDiv.hasChildNodes()) {
        metaDiv.parentNode?.removeChild(metaDiv);
      }
    }
    
    // Trigger input event for history
    const editorElement = this.editor.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Remove an attachment
   * @param attachmentElement The attachment element
   */
  private removeAttachment(attachmentElement: HTMLElement): void {
    if (!this.editor) return;
    
    if (attachmentElement.parentNode) {
      attachmentElement.parentNode.removeChild(attachmentElement);
      
      // Trigger input event for history
      const editorElement = this.editor.getElement();
      if (editorElement) {
        editorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
      }
    }
  }
  
  /**
   * Handle file upload
   * @param file The file to upload
   * @param name The display name
   * @param existingAttachment Existing attachment to update
   */
  private handleFileUpload(
    file: File,
    name: string = '',
    existingAttachment: HTMLElement | null = null
  ): void {
    // Use FileReader to create a data URL
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const url = e.target?.result as string;
      const displayName = name || file.name;
      const size = this.formatFileSize(file.size);
      
      if (existingAttachment) {
        this.updateAttachment(existingAttachment, url, displayName, size);
      } else {
        this.insertAttachment(url, displayName, size);
      }
    };
    
    reader.readAsDataURL(file);
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closeAttachmentDialog();
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<AttachmentPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new AttachmentPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: AttachmentPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
