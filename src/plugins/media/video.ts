import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Video plugin configuration
 */
const config: PluginConfig = {
  id: 'video',
  name: 'Video',
  description: 'Insert and manage video embeds',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'video',
      command: 'video',
      icon: '▶️',
      label: 'Video',
      tooltip: 'Insert video',
      group: 'media',
      ariaLabel: 'Insert video',
    }
  ],
  shortcuts: [] // No keyboard shortcuts for video to avoid conflicts
};

/**
 * Video plugin implementation
 * Adds ability to embed videos from URLs
 */
export class VideoPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  // Supported video platforms and their regex patterns
  private videoProviders = [
    {
      name: 'YouTube',
      regex: /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
      embedUrl: 'https://www.youtube.com/embed/$4',
      icon: '🎬'
    },
    {
      name: 'Vimeo',
      regex: /^(https?:\/\/)?(www\.)?(vimeo\.com\/)(\d+)/,
      embedUrl: 'https://player.vimeo.com/video/$4',
      icon: '📹'
    },
    {
      name: 'Dailymotion',
      regex: /^(https?:\/\/)?(www\.)?(dailymotion\.com\/video\/)([a-zA-Z0-9]+)/,
      embedUrl: 'https://www.dailymotion.com/embed/video/$4',
      icon: '📼'
    }
  ];
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the video command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'video' && this.editor) {
      this.openVideoDialog();
    }
  }
  
  /**
   * Open the video insert/edit dialog
   * @param existingVideoWrapper Existing video wrapper to edit
   */
  private openVideoDialog(existingVideoWrapper: HTMLElement | null = null): void {
    if (this.isDialogOpen) {
      this.closeVideoDialog();
    }
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/30 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 rounded-lg shadow-lg p-5 min-w-[400px] max-w-[500px] z-[9999]'; // Tailwind for dialog
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'video-dialog-title');
    
    // Get current values if editing an existing video
    let currentUrl = '';
    let currentCaption = '';
    let currentWidth = '';
    
    if (existingVideoWrapper) {
      const iframe = existingVideoWrapper.querySelector('iframe');
      if (iframe) {
        currentUrl = this.getOriginalUrlFromEmbed(iframe.src);
      }
      
      const caption = existingVideoWrapper.querySelector('.feather-video-caption');
      if (caption) {
        currentCaption = caption.textContent || '';
      }
      
      currentWidth = existingVideoWrapper.style.maxWidth || '';
      if (currentWidth.endsWith('px')) {
        currentWidth = currentWidth.slice(0, -2);
      }
    }
    
    // Create dialog content programmatically
    const dialogTitle = document.createElement('h3');
    dialogTitle.id = 'video-dialog-title';
    dialogTitle.className = 'text-lg font-semibold mt-0 mb-4 text-gray-900 dark:text-slate-100';
    dialogTitle.textContent = `${existingVideoWrapper ? 'Edit' : 'Insert'} Video`;
    this.dialog.appendChild(dialogTitle);

    const form = document.createElement('form');
    form.className = 'flex flex-col gap-3';

    const createField = (labelText: string, inputId: string, inputType: string, placeholder: string, value: string, hintText?: string) => {
      const div = document.createElement('div');
      const label = document.createElement('label') as HTMLLabelElement;
      label.htmlFor = inputId;
      label.className = 'block mb-1 font-medium text-sm text-gray-700 dark:text-slate-300';
      label.textContent = labelText;
      const input = document.createElement('input');
      input.type = inputType; input.id = inputId;
      input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded box-border bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 text-sm focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
      input.placeholder = placeholder; input.value = value;
      div.appendChild(label); div.appendChild(input);
      if (hintText) {
        const hint = document.createElement('p');
        hint.className = 'mt-1 text-xs text-gray-500 dark:text-slate-400';
        hint.textContent = hintText;
        div.appendChild(hint);
      }
      return div;
    };
    
    form.appendChild(createField('Video URL', 'video-url', 'url', 'https://www.youtube.com/watch?v=...', currentUrl, 'Supports YouTube, Vimeo, and Dailymotion links'));
    
    const previewContainerDiv = document.createElement('div');
    previewContainerDiv.id = 'video-preview';
    previewContainerDiv.className = 'hidden'; // Initially hidden
    form.appendChild(previewContainerDiv);

    form.appendChild(createField('Caption (optional)', 'video-caption', 'text', 'Video description', currentCaption));
    form.appendChild(createField('Width (px)', 'video-width', 'number', '640', currentWidth, 'Leave empty for responsive width'));
    
    this.dialog.appendChild(form);

    const buttonsContainer = document.createElement('div');
    buttonsContainer.className = 'flex justify-end gap-2 mt-4'; 

    if (existingVideoWrapper) {
      const removeButtonElem = document.createElement('button');
      removeButtonElem.type = 'button'; removeButtonElem.id = 'video-remove-button';
      removeButtonElem.className = 'py-2 px-4 rounded text-sm font-medium bg-red-500 hover:bg-red-600 text-white border-red-500 hover:border-red-600';
      removeButtonElem.textContent = 'Remove Video';
      buttonsContainer.appendChild(removeButtonElem);
    }

    const cancelButtonElem = document.createElement('button');
    cancelButtonElem.type = 'button'; cancelButtonElem.id = 'video-cancel-button';
    cancelButtonElem.className = 'py-2 px-4 rounded text-sm font-medium bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200 border border-gray-300 dark:border-slate-500';
    cancelButtonElem.textContent = 'Cancel';
    buttonsContainer.appendChild(cancelButtonElem);

    const insertButtonElem = document.createElement('button');
    insertButtonElem.type = 'button'; insertButtonElem.id = 'video-insert-button';
    insertButtonElem.className = 'py-2 px-4 rounded text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white border-blue-600 hover:border-blue-700';
    insertButtonElem.textContent = existingVideoWrapper ? 'Update' : 'Insert';
    buttonsContainer.appendChild(insertButtonElem);
    
    this.dialog.appendChild(buttonsContainer);
    
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    const urlInput = this.dialog.querySelector('#video-url') as HTMLInputElement;
    const previewContainer = this.dialog.querySelector('#video-preview') as HTMLElement;
    
    if (currentUrl) {
      this.updateVideoPreview(currentUrl, previewContainer);
    }
    
    urlInput.addEventListener('input', () => {
      this.updateVideoPreview(urlInput.value, previewContainer);
    });
    
    const cancelButton = this.dialog.querySelector('#video-cancel-button');
    const insertButton = this.dialog.querySelector('#video-insert-button');
    const removeButton = this.dialog.querySelector('#video-remove-button');
    
    cancelButton?.addEventListener('click', () => this.closeVideoDialog());
    backdrop.addEventListener('click', () => this.closeVideoDialog());
    
    insertButton?.addEventListener('click', () => {
      const urlValue = urlInput.value;
      const captionValue = (this.dialog?.querySelector('#video-caption') as HTMLInputElement)?.value || '';
      const widthValue = (this.dialog?.querySelector('#video-width') as HTMLInputElement)?.value || '';
      
      if (urlValue) {
        const embedInfo = this.getEmbedInfo(urlValue);
        if (embedInfo) {
          if (existingVideoWrapper) {
            this.updateVideo(existingVideoWrapper, embedInfo.embedUrl, captionValue, widthValue);
          } else {
            this.insertVideo(embedInfo.embedUrl, captionValue, widthValue);
          }
        }
      }
      this.closeVideoDialog();
    });
    
    if (removeButton && existingVideoWrapper) {
      removeButton.addEventListener('click', () => {
        this.removeVideo(existingVideoWrapper);
        this.closeVideoDialog();
      });
    }
    
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      const insertBtn = insertButtonElem as HTMLButtonElement;
      insertBtn?.click();
    });
    
    document.addEventListener('keydown', this.handleDialogKeydown);
    urlInput.focus();
  }
  
  /**
   * Update the video preview in the dialog
   * @param url The video URL to preview
   * @param container The container to show the preview in
   */
  private updateVideoPreview(url: string, container: HTMLElement): void {
    const embedInfo = this.getEmbedInfo(url);
    
    container.innerHTML = ''; 
    
    if (embedInfo) {
      container.className = 'block mt-4 border border-dashed border-gray-300 dark:border-slate-600 min-h-[100px] flex items-center justify-center text-gray-400 dark:text-slate-500 text-sm bg-gray-50 dark:bg-slate-800/50 rounded p-4'; // Apply Tailwind classes
      
      const iconDiv = document.createElement('div');
      iconDiv.className = 'text-4xl mr-3'; 
      iconDiv.textContent = embedInfo.icon;
      
      const infoDiv = document.createElement('div');
      infoDiv.className = 'text-left';
      
      const nameDiv = document.createElement('div');
      nameDiv.className = 'font-semibold text-gray-700 dark:text-slate-200';
      nameDiv.textContent = embedInfo.name;
      
      const urlDiv = document.createElement('div');
      urlDiv.className = 'text-xs text-gray-500 dark:text-slate-400 truncate max-w-xs';
      urlDiv.textContent = url;
      
      infoDiv.appendChild(nameDiv);
      infoDiv.appendChild(urlDiv);
      
      container.appendChild(iconDiv);
      container.appendChild(infoDiv);
      
    } else {
      container.className = 'hidden'; 
    }
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeVideoDialog();
    }
  };
  
  /**
   * Close the video dialog
   */
  private closeVideoDialog(): void {
    if (!this.isDialogOpen) return;
    
    const backdrop = document.querySelector('.feather-video-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Get embed information for a video URL
   * @param url The video URL
   * @returns Embed information or null if not supported
   */
  private getEmbedInfo(url: string): { name: string; embedUrl: string; icon: string } | null {
    for (const provider of this.videoProviders) {
      const match = url.match(provider.regex);
      if (match) {
        return {
          name: provider.name,
          embedUrl: provider.embedUrl.replace('$4', match[4]),
          icon: provider.icon
        };
      }
    }
    return null;
  }
  
  /**
   * Get the original URL from an embed URL
   * This is an approximation since we can't always derive the exact original URL
   * @param embedUrl The embed URL
   * @returns The original URL
   */
  private getOriginalUrlFromEmbed(embedUrl: string): string {
    const youtubeMatch = embedUrl.match(/youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/);
    if (youtubeMatch) {
      return `https://www.youtube.com/watch?v=${youtubeMatch[1]}`;
    }
    const vimeoMatch = embedUrl.match(/player\.vimeo\.com\/video\/(\d+)/);
    if (vimeoMatch) {
      return `https://vimeo.com/${vimeoMatch[1]}`;
    }
    const dailymotionMatch = embedUrl.match(/dailymotion\.com\/embed\/video\/([a-zA-Z0-9]+)/);
    if (dailymotionMatch) {
      return `https://www.dailymotion.com/video/${dailymotionMatch[1]}`;
    }
    return embedUrl;
  }
  
  /**
   * Insert a new video
   * @param embedUrl The video embed URL
   * @param caption The video caption
   * @param width The video width
   */
  private insertVideo(embedUrl: string, caption: string = '', width: string = ''): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    
    const wrapper = document.createElement('div');
    wrapper.className = 'group relative w-full my-4 max-w-2xl cursor-pointer';
    
    if (width) {
      wrapper.style.maxWidth = `${width}px`;
    }
    
    const responsive = document.createElement('div');
    responsive.className = 'relative aspect-video overflow-hidden bg-gray-100 dark:bg-slate-800 rounded';
    
    const iframe = document.createElement('iframe');
    iframe.className = 'absolute inset-0 w-full h-full border-none';
    iframe.src = embedUrl;
    iframe.setAttribute('allowfullscreen', 'true');
    iframe.setAttribute('allow', 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture');
    iframe.setAttribute('loading', 'lazy');
    
    const controls = document.createElement('div');
    controls.className = 'absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 group-[.selected]:opacity-100 group-hover:flex group-[.selected]:flex group-hover:gap-1 group-[.selected]:gap-1 transition-opacity duration-200 ease-in-out';
    
    const editControl = document.createElement('div');
    editControl.className = 'bg-black/60 hover:bg-black/80 text-white border-none rounded-full w-7 h-7 flex items-center justify-center cursor-pointer shadow-md text-sm p-0';
    editControl.setAttribute('aria-label', 'Edit video');
    editControl.setAttribute('title', 'Edit');
    editControl.innerHTML = '🖊';
    
    editControl.addEventListener('click', (e) => {
      e.stopPropagation();
      this.openVideoDialog(wrapper);
    });
    controls.appendChild(editControl);
    
    responsive.appendChild(iframe);
    wrapper.appendChild(responsive);
    wrapper.appendChild(controls);
    
    if (caption) {
      const captionElement = document.createElement('div');
      captionElement.className = 'mt-2 text-gray-500 dark:text-slate-400 text-sm text-center';
      captionElement.textContent = caption;
      wrapper.appendChild(captionElement);
    }
    
    range.deleteContents();
    range.insertNode(wrapper);
    
    selection.removeAllRanges();
    const newRange = document.createRange();
    newRange.setStartAfter(wrapper);
    newRange.collapse(true);
    selection.addRange(newRange);
    
    const editorElement = this.editor!.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Update an existing video
   * @param wrapperElement The video wrapper element
   * @param embedUrl The new embed URL
   * @param caption The new caption
   * @param width The new width
   */
  private updateVideo(
    wrapperElement: HTMLElement,
    embedUrl: string,
    caption: string = '',
    width: string = ''
  ): void {
    if (!this.editor) return;
    
    const iframe = wrapperElement.querySelector('iframe');
    if (iframe) {
      iframe.src = embedUrl;
    }
    
    if (width) {
      wrapperElement.style.maxWidth = `${width}px`;
    } else {
      wrapperElement.style.maxWidth = '';
    }
    
    let captionElement = wrapperElement.querySelector('.feather-video-caption');
    if (caption) {
      if (captionElement) {
        captionElement.textContent = caption;
      } else {
        captionElement = document.createElement('div');
        captionElement.className = 'feather-video-caption'; // This should be Tailwind styled
        captionElement.textContent = caption;
        wrapperElement.appendChild(captionElement);
      }
    } else if (captionElement) {
      captionElement.parentNode?.removeChild(captionElement);
    }
    
    const editorElement = this.editor!.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Remove a video
   * @param wrapperElement The video wrapper element
   */
  private removeVideo(wrapperElement: HTMLElement): void {
    if (!this.editor) return;
    
    if (wrapperElement.parentNode) {
      wrapperElement.parentNode.removeChild(wrapperElement);
      
      const editorElement = this.editor!.getElement();
      if (editorElement) {
        editorElement.dispatchEvent(
          new InputEvent('input', { bubbles: true, cancelable: true })
        );
      }
    }
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    if (this.isDialogOpen) {
      this.closeVideoDialog();
    }
  }
}

const plugin: Plugin = createPlugin<VideoPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new VideoPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: VideoPlugin) => {
    instance.destroy();
  }
);

export const pluginId = config.id;
export default plugin;
