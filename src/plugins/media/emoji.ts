import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Emoji plugin configuration
 */
const config: PluginConfig = {
  id: 'emoji',
  name: 'Emoji',
  description: 'Insert emoji characters',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'emoji',
      command: 'emoji',
      icon: '🙂',
      label: 'Emoji',
      tooltip: 'Insert emoji (Ctrl/⌘+.)',
      group: 'special',
      ariaLabel: 'Insert emoji',
    }
  ],
  shortcuts: [
    {
      command: 'emoji',
      key: '.',
      ctrlKey: true,
      description: 'Open emoji picker'
    }
  ]
};

// Common emoji categories and their contents
interface EmojiItem {
  char: string;
  name: string;
}

type EmojiCategory = Record<string, EmojiItem[]>;

const emojiData: EmojiCategory = {
  'Smileys & People': [
    { char: '😀', name: 'Grinning Face' },
    { char: '😃', name: 'Grinning Face with Big Eyes' },
    { char: '😄', name: 'Grinning Face with Smiling Eyes' },
    { char: '😁', name: 'Beaming Face with Smiling Eyes' },
    { char: '😆', name: 'Grinning Squinting Face' },
    { char: '😅', name: 'Grinning Face with Sweat' },
    { char: '🤣', name: 'Rolling on the Floor Laughing' },
    { char: '😂', name: 'Face with Tears of Joy' },
    { char: '🙂', name: 'Slightly Smiling Face' },
    { char: '🙃', name: 'Upside-Down Face' },
    { char: '😉', name: 'Winking Face' },
    { char: '😊', name: 'Smiling Face with Smiling Eyes' },
    { char: '😇', name: 'Smiling Face with Halo' }
  ],
  'Animals & Nature': [
    { char: '🐶', name: 'Dog Face' },
    { char: '🐱', name: 'Cat Face' },
    { char: '🐭', name: 'Mouse Face' },
    { char: '🐹', name: 'Hamster Face' },
    { char: '🐰', name: 'Rabbit Face' },
    { char: '🦊', name: 'Fox Face' },
    { char: '🐻', name: 'Bear Face' },
    { char: '🐼', name: 'Panda Face' },
    { char: '🐨', name: 'Koala Face' },
    { char: '🐯', name: 'Tiger Face' },
    { char: '🦁', name: 'Lion Face' },
    { char: '🐮', name: 'Cow Face' },
    { char: '🐷', name: 'Pig Face' }
  ],
  'Food & Drink': [
    { char: '🍏', name: 'Green Apple' },
    { char: '🍎', name: 'Red Apple' },
    { char: '🍐', name: 'Pear' },
    { char: '🍊', name: 'Tangerine' },
    { char: '🍋', name: 'Lemon' },
    { char: '🍌', name: 'Banana' },
    { char: '🍉', name: 'Watermelon' },
    { char: '🍇', name: 'Grapes' },
    { char: '🍓', name: 'Strawberry' },
    { char: '🍈', name: 'Melon' },
    { char: '🍒', name: 'Cherries' },
    { char: '🍑', name: 'Peach' },
    { char: '🥭', name: 'Mango' }
  ],
  'Objects': [
    { char: '⌚', name: 'Watch' },
    { char: '📱', name: 'Mobile Phone' },
    { char: '💻', name: 'Laptop' },
    { char: '⌨️', name: 'Keyboard' },
    { char: '🖥️', name: 'Desktop Computer' },
    { char: '🖨️', name: 'Printer' },
    { char: '📷', name: 'Camera' },
    { char: '🎥', name: 'Movie Camera' },
    { char: '📺', name: 'Television' },
    { char: '🎮', name: 'Video Game' },
    { char: '🕹️', name: 'Joystick' },
    { char: '📀', name: 'DVD' },
    { char: '💿', name: 'CD' }
  ]
};

/**
 * Emoji plugin implementation
 * Adds ability to insert emoji characters via a picker UI
 */
export class EmojiPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the emoji command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'emoji' && this.editor) {
      this.openEmojiPicker();
    }
  }
  
  /**
   * Open the emoji picker dialog
   */
  private openEmojiPicker(): void {
    if (this.isDialogOpen) {
      this.closeEmojiPicker();
    }
    
    // Get cursor position for placing the picker
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-black/40 z-[1001]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 rounded-lg shadow-lg z-[1002] w-[90%] max-w-[360px] max-h-[70vh] flex flex-col font-sans text-gray-900 dark:text-slate-200';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'emoji-dialog-title');
    
    // Position the picker near the cursor
    const pickerWidth = 350;
    const pickerHeight = 400;
    
    let left = rect.left + window.scrollX;
    let top = rect.bottom + window.scrollY + 10;
    
    // Ensure the picker stays within the viewport
    if (left + pickerWidth > window.innerWidth) {
      left = window.innerWidth - pickerWidth - 10;
    }
    
    if (top + pickerHeight > window.innerHeight) {
      top = rect.top + window.scrollY - pickerHeight - 10;
    }
    
    this.dialog.style.left = `${left}px`;
    this.dialog.style.top = `${top}px`;
    
    // Create dialog content programmatically
    const headerDiv = document.createElement('div');
    headerDiv.className = 'px-4 py-2.5 border-b border-gray-200 dark:border-slate-700 flex justify-between items-center';
    
    const titleH3 = document.createElement('h3');
    titleH3.id = 'emoji-dialog-title';
    titleH3.className = 'text-base font-semibold m-0';
    titleH3.textContent = 'Emoji';
    headerDiv.appendChild(titleH3);
    
    const closeButton = document.createElement('button');
    closeButton.type = 'button';
    closeButton.className = 'bg-transparent border-none text-xl leading-none cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200';
    closeButton.setAttribute('aria-label', 'Close');
    closeButton.textContent = '×';
    closeButton.addEventListener('click', () => this.closeEmojiPicker());
    headerDiv.appendChild(closeButton);
    this.dialog.appendChild(headerDiv);

    const searchDiv = document.createElement('div');
    searchDiv.className = 'px-4 py-2.5 border-b border-gray-200 dark:border-slate-700';
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'w-full py-2 px-2.5 border border-gray-300 dark:border-slate-600 rounded box-border text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
    searchInput.placeholder = 'Search emoji...';
    searchInput.setAttribute('aria-label', 'Search emoji');
    searchDiv.appendChild(searchInput);
    this.dialog.appendChild(searchDiv);

    const tabsDiv = document.createElement('div');
    tabsDiv.className = 'flex flex-wrap px-2.5 py-1 border-b border-gray-200 dark:border-slate-700 gap-[5px]';
    tabsDiv.setAttribute('role', 'tablist');
    
    const tabCategories = ['All', 'Smileys & People', 'Animals & Nature', 'Food & Drink', 'Objects'];
    const tabDataCategories = ['all', 'Smileys & People', 'Animals & Nature', 'Food & Drink', 'Objects'];

    tabCategories.forEach((catName, index) => {
      const tabButton = document.createElement('button');
      tabButton.type = 'button';
      tabButton.className = `px-3 py-2 border-none bg-transparent cursor-pointer text-sm rounded text-gray-600 dark:text-slate-400 hover:bg-gray-100 dark:hover:bg-slate-700 ${index === 0 ? 'bg-gray-300 dark:bg-slate-600 text-black dark:text-white font-medium' : ''}`;
      if (index === 0) tabButton.classList.add('active'); // Keep 'active' for JS logic if needed, or rely on Tailwind conditional styling
      tabButton.dataset.category = tabDataCategories[index];
      tabButton.setAttribute('role', 'tab');
      tabButton.setAttribute('aria-selected', index === 0 ? 'true' : 'false');
      tabButton.textContent = catName;
      tabsDiv.appendChild(tabButton);
    });
    this.dialog.appendChild(tabsDiv);

    const gridContainer = document.createElement('div');
    gridContainer.className = 'feather-emoji-grid px-4 py-2.5 overflow-y-auto flex-grow'; // Renamed class for clarity
    gridContainer.setAttribute('role', 'tabpanel');
    this.dialog.appendChild(gridContainer);
    
    // Initial render of emoji grid
    this.renderEmojiGrid(gridContainer, 'all'); // Corrected method name


    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Set up tab switching
    const tabs = this.dialog.querySelectorAll('[role="tab"]'); // Use attribute selector
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        tabs.forEach(t => {
          t.classList.remove('bg-gray-300', 'dark:bg-slate-600', 'text-black', 'dark:text-white', 'font-medium', 'active');
          t.setAttribute('aria-selected', 'false');
        });
        tab.classList.add('bg-gray-300', 'dark:bg-slate-600', 'text-black', 'dark:text-white', 'font-medium', 'active');
        tab.setAttribute('aria-selected', 'true');
        
        const category = tab.getAttribute('data-category') || 'all';
        this.renderEmojiGrid(gridContainer, category); // Corrected method name
      });
    });
    
    // Set up search functionality
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase();
      this.renderEmojiGrid(gridContainer, 'all', query); // Corrected method name
    });
    
    // Set up emoji selection (delegated to gridContainer)
    gridContainer.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const emojiItem = target.closest('.emoji-item-class'); // Use a specific class for emoji items
      if (emojiItem) {
        const emoji = emojiItem.getAttribute('data-emoji');
        if (emoji) {
          this.insertEmoji(emoji);
          this.closeEmojiPicker();
        }
      }
    });
    
    // Close button already handled above
    
    // Close on backdrop click
    backdrop.addEventListener('click', () => {
      this.closeEmojiPicker();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
    
    // Focus the search input
    searchInput.focus();
  }
  
  /**
   * Render the emoji grid for a specific category
   * @param category The emoji category to render
   * @param gridContainer The HTML element to render the grid into
   * @param category The emoji category to render
   * @param searchQuery Optional search query to filter emojis
   */
  private renderEmojiGrid(gridContainer: HTMLElement, category: string, searchQuery?: string): void {
    gridContainer.innerHTML = ''; // Clear previous content

    const createEmojiItem = (emoji: EmojiItem): HTMLDivElement => {
      const itemDiv = document.createElement('div');
      itemDiv.className = 'emoji-item-class flex justify-center items-center text-[22px] cursor-pointer rounded p-1 transition-colors duration-100 ease-in-out hover:bg-gray-200 dark:hover:bg-slate-700';
      itemDiv.dataset.emoji = emoji.char;
      itemDiv.title = emoji.name;
      itemDiv.textContent = emoji.char;
      return itemDiv;
    };

    const createCategorySection = (catName: string, emojis: EmojiItem[]): DocumentFragment => {
      const fragment = document.createDocumentFragment();
      const categoryDiv = document.createElement('div');
      categoryDiv.className = 'mb-4';
      
      const categoryTitleDiv = document.createElement('div');
      categoryTitleDiv.className = 'text-[13px] font-semibold text-gray-500 dark:text-slate-400 mb-2 uppercase';
      categoryTitleDiv.textContent = catName;
      categoryDiv.appendChild(categoryTitleDiv);
      
      const itemsGridDiv = document.createElement('div');
      itemsGridDiv.className = 'grid grid-cols-[repeat(auto-fill,minmax(30px,1fr))] gap-2';
      emojis.forEach(emoji => itemsGridDiv.appendChild(createEmojiItem(emoji)));
      categoryDiv.appendChild(itemsGridDiv);
      
      fragment.appendChild(categoryDiv);
      return fragment;
    };

    if (searchQuery) {
      const allEmojis: { category: string; emoji: EmojiItem }[] = [];
      Object.entries(emojiData).forEach(([cat, emojis]) => {
        emojis.forEach(emoji => allEmojis.push({ category: cat, emoji }));
      });
      
      const filteredEmojis = allEmojis.filter(item => 
        item.emoji.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      
      if (filteredEmojis.length === 0) {
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'text-gray-500 dark:text-slate-400 italic p-4 text-center';
        noResultsDiv.textContent = 'No emojis found';
        gridContainer.appendChild(noResultsDiv);
        return;
      }
      
      const groupedEmojis: Record<string, EmojiItem[]> = {};
      filteredEmojis.forEach(item => {
        if (!groupedEmojis[item.category]) {
          groupedEmojis[item.category] = [];
        }
        groupedEmojis[item.category].push(item.emoji);
      });

      Object.entries(groupedEmojis).forEach(([catName, emojis]) => {
        gridContainer.appendChild(createCategorySection(catName, emojis));
      });

    } else if (category === 'all') {
      Object.entries(emojiData).forEach(([catName, emojis]) => {
        gridContainer.appendChild(createCategorySection(catName, emojis));
      });
    } else {
      const emojis = emojiData[category as keyof typeof emojiData] || [];
      if (emojis.length > 0) {
        gridContainer.appendChild(createCategorySection(category, emojis));
      } else {
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'text-gray-500 dark:text-slate-400 italic p-4 text-center';
        noResultsDiv.textContent = `No emojis in ${category}`;
        gridContainer.appendChild(noResultsDiv);
      }
    }
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeEmojiPicker();
    }
  };
  
  /**
   * Close the emoji picker
   */
  private closeEmojiPicker(): void {
    if (!this.isDialogOpen) return;
    
    // Remove dialog and backdrop
    const backdrop = document.querySelector('.feather-emoji-dialog-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
    
    // Focus back to editor
    if (this.editor) {
      this.editor.focus();
    }
  }
  
  /**
   * Insert an emoji at the current cursor position
   * @param emoji The emoji character to insert
   */
  private insertEmoji(emoji: string): void {
    if (!this.editor) return;
    
    const selection = window.getSelection();
    if (!selection || !selection.rangeCount) return;
    
    const range = selection.getRangeAt(0);
    range.deleteContents();
    
    // Insert the emoji
    const textNode = document.createTextNode(emoji);
    range.insertNode(textNode);
    
    // Move cursor after the emoji
    range.setStartAfter(textNode);
    range.collapse(true);
    selection.removeAllRanges();
    selection.addRange(range);
    
    // Trigger input event for history
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close picker if open
    if (this.isDialogOpen) {
      this.closeEmojiPicker();
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<EmojiPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new EmojiPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: EmojiPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
