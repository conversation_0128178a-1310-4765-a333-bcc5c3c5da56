import type { Plugin, Editor, ToolbarItem } from '../types';

/**
 * Creates a plugin that conforms to the Plugin interface
 * @param id - The unique identifier for the plugin
 * @param initFn - Function to initialize the plugin with an editor
 * @param destroyFn - Function to clean up the plugin
 * @param toolbarItems - Optional toolbar items for the plugin
 * @returns A Plugin object
 */
export function createPlugin<T>(
  id: string,
  initFn: (editor: Editor) => T,
  destroyFn: (instance: T) => void,
  toolbarItems?: ToolbarItem[]
): Plugin {
  let instance: T | null = null;

  return {
    id,
    toolbarItems,
    init: (editor: Editor) => {
      instance = initFn(editor);
    },
    destroy: () => {
      if (instance) {
        destroyFn(instance);
        instance = null;
      }
    }
  };
}
