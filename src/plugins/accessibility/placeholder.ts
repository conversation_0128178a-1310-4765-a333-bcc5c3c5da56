/**
 * Placeholder Plugin for FeatherJS
 * Provides hidden required hints and ARIA labeling for form fields
 */

import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

export interface PlaceholderConfig {
  /**
   * Whether to auto-add placeholders to form fields
   */
  autoAddPlaceholders: boolean;

  /**
   * Whether to automatically mark required fields
   */
  markRequiredFields: boolean;

  /**
   * Custom text for required field indicator
   */
  requiredIndicator: string;
}

const DEFAULT_CONFIG: PlaceholderConfig = {
  autoAddPlaceholders: true,
  markRequiredFields: true,
  requiredIndicator: '* Required'
};

const config: PluginConfig = {
  id: 'placeholder',
  name: 'Placeholder',
  description: 'Manage field placeholders and hints',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'placeholder',
      command: 'placeholder',
      icon: '❔',
      label: 'Placeholder',
      tooltip: 'Manage field placeholders and hints',
      group: 'accessibility',
      ariaLabel: 'Toggle placeholder management',
    }
  ],
  shortcuts: []
};

class PlaceholderPlugin extends BasePlugin {
  protected config: PluginConfig & PlaceholderConfig;
  private observer: MutationObserver | null = null;
  private processedFields: Set<HTMLElement> = new Set();

  constructor() {
    super(config);
    this.config = { ...config, ...DEFAULT_CONFIG };
  }

  /**
   * Initialize the plugin
   */
  public init(editor: Editor): void {
    this.editor = editor;

    // Register toolbar item
    this.registerToolbarItems();

    // Setup mutation observer
    this.setupMutationObserver();

    // Process existing form fields
    if (this.config.autoAddPlaceholders || this.config.markRequiredFields) {
      this.processFormFields();
    }
  }

  /**
   * Handle toolbar button click or keyboard shortcut
   */
  protected handleCommand(_command: string): void {
    // Toggle automatic placeholder management
    this.togglePlaceholderManagement();
  }

  /**
   * Toggle placeholder management on/off
   */
  private togglePlaceholderManagement(): void {
    if (!this.editor) return;

    // Toggle config
    this.config.autoAddPlaceholders = !this.config.autoAddPlaceholders;
    this.config.markRequiredFields = !this.config.markRequiredFields;

    // Update toolbar button state
    this.updateToolbarState();

    // Apply or remove placeholders
    if (this.config.autoAddPlaceholders || this.config.markRequiredFields) {
      this.processFormFields();
    } else {
      this.removePlaceholdersAndHints();
    }

    // Show feedback message
    this.showFeedbackMessage(
      (this.config.autoAddPlaceholders || this.config.markRequiredFields) ?
        'Placeholder management activated' :
        'Placeholder management deactivated'
    );
  }

  /**
   * Update toolbar button state
   */
  private updateToolbarState(): void {
    const button = this.toolbarButtons[0];
    if (button) {
      const isActive = this.config.autoAddPlaceholders || this.config.markRequiredFields;
      button.classList.toggle('active', isActive);

      // Update ARIA attributes
      button.setAttribute('aria-pressed', isActive ? 'true' : 'false');
    }
  }

  /**
   * Set up mutation observer to watch for new form fields
   */
  private setupMutationObserver(): void {
    if (!this.editor) return;

    this.observer = new MutationObserver((mutations) => {
      let hasNewFormField = false;

      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length) {
          // Check if any added node is a form field or contains a form field
          for (const node of Array.from(mutation.addedNodes)) {
            if (node instanceof HTMLElement) {
              if (this.isFormField(node) || node.querySelector('input, select, textarea')) {
                hasNewFormField = true;
                break;
              }
            }
          }
        }
      }

      // If new form fields were added, process them
      if (hasNewFormField && (this.config.autoAddPlaceholders || this.config.markRequiredFields)) {
        this.processFormFields();
      }
    });

    // Start observing
    const editorElement = this.editor.getElement();
    if (editorElement) {
      this.observer.observe(editorElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['required', 'placeholder', 'aria-required']
      });
    }
  }

  /**
   * Check if an element is a form field
   */
  private isFormField(element: Element): boolean {
    return element instanceof HTMLInputElement ||
           element instanceof HTMLSelectElement ||
           element instanceof HTMLTextAreaElement;
  }

  /**
   * Process all form fields in the editor
   */
  private processFormFields(): void {
    if (!this.editor) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Find all form fields
    const formFields = Array.from(editorElement.querySelectorAll('input, select, textarea'));

    formFields.forEach(field => {
      if (this.processedFields.has(field as HTMLElement)) return;

      if (this.config.autoAddPlaceholders) {
        this.addPlaceholderToField(field as HTMLElement);
      }

      if (this.config.markRequiredFields) {
        this.markRequiredField(field as HTMLElement);
      }

      // Add to processed set
      this.processedFields.add(field as HTMLElement);
    });
  }

  /**
   * Add placeholder to a form field if it doesn't have one
   */
  private addPlaceholderToField(field: HTMLElement): void {
    // Only process certain types of fields
    if (field instanceof HTMLInputElement) {
      const inputTypes = ['text', 'email', 'password', 'tel', 'url', 'search', 'number'];
      if (!inputTypes.includes(field.type)) return;
    }

    // Check if field already has a placeholder
    if (field.hasAttribute('placeholder') && field.getAttribute('placeholder')?.trim()) {
      return;
    }

    // Find a label for the field to use as placeholder base
    let labelText = this.getLabelTextForField(field);

    // If no label found, use field name or type
    if (!labelText) {
      if (field.hasAttribute('name')) {
        labelText = this.humanizeFieldName(field.getAttribute('name') || '');
      } else {
        labelText = field instanceof HTMLInputElement ?
          this.humanizeFieldType(field.type) :
          field.tagName.toLowerCase();
      }
    }

    // Create placeholder text
    const placeholder = `Enter ${labelText.toLowerCase()}...`;

    // Set placeholder
    field.setAttribute('placeholder', placeholder);

    // Also add ARIA described-by if there's a hint element
    this.enhanceWithAriaAttributes(field);
  }

  /**
   * Mark a field as required if it has the required attribute
   */
  private markRequiredField(field: HTMLElement): void {
    // Check if field is required
    const isRequired = field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';

    if (!isRequired) return;

    // Set aria-required attribute if not already set
    if (!field.hasAttribute('aria-required')) {
      field.setAttribute('aria-required', 'true');
    }

    // Find associated label
    let label: HTMLElement | null = null;

    // Check for label with for attribute
    if (field.id) {
      label = this.editor?.getElement()?.querySelector(`label[for="${field.id}"]`) as HTMLElement || null;
    }

    // Check if field is inside a label
    if (!label) {
      label = field.closest('label') as HTMLElement || null;
    }

    // If label exists, add required indicator
    if (label && !label.querySelector('.js-required-indicator')) { // Query for JS hook
      const requiredIndicator = document.createElement('span');
      requiredIndicator.className = 'js-required-indicator text-red-700 dark:text-red-500 text-[0.9em] ml-1'; // Tailwind + JS hook
      requiredIndicator.setAttribute('aria-hidden', 'true');
      requiredIndicator.textContent = this.config.requiredIndicator;
      label.appendChild(requiredIndicator);
    } else if (!label) {
      // If no label exists, add a visually hidden hint after the field
      const existingHint = field.nextElementSibling?.classList.contains('js-field-hint'); // Query for JS hook

      if (!existingHint) {
        const requiredHint = document.createElement('span');
        requiredHint.className = 'js-field-hint block text-[0.85em] text-red-700 dark:text-red-500 mt-1'; // Tailwind + JS hook
        requiredHint.id = `hint-${this.generateUniqueId()}`;
        requiredHint.textContent = this.config.requiredIndicator;

        // If field already has aria-describedby, append to it
        if (field.hasAttribute('aria-describedby')) {
          const existingDescribedBy = field.getAttribute('aria-describedby') || '';
          field.setAttribute('aria-describedby', `${existingDescribedBy} ${requiredHint.id}`);
        } else {
          field.setAttribute('aria-describedby', requiredHint.id);
        }

        // Insert hint after field
        if (field.nextSibling) {
          field.parentNode?.insertBefore(requiredHint, field.nextSibling);
        } else {
          field.parentNode?.appendChild(requiredHint);
        }
      }
    }
  }

  /**
   * Remove placeholders and hints
   */
  private removePlaceholdersAndHints(): void {
    if (!this.editor) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Remove hints using the JS hook class
    const hints = Array.from(editorElement.querySelectorAll('.js-field-hint'));
    hints.forEach(hint => {
      if (hint.parentNode) {
        hint.parentNode.removeChild(hint);
      }
    });

    // Remove required indicators using the JS hook class
    const indicators = Array.from(editorElement.querySelectorAll('.js-required-indicator'));
    indicators.forEach(indicator => {
      if (indicator.parentNode) {
        indicator.parentNode.removeChild(indicator);
      }
    });

    // Clear processed fields set
    this.processedFields.clear();
  }

  /**
   * Get label text for a field
   */
  private getLabelTextForField(field: HTMLElement): string {
    // Check for label with for attribute
    if (field.id) {
      const label = this.editor?.getElement()?.querySelector(`label[for="${field.id}"]`);
      if (label && label.textContent) {
        return label.textContent.trim().replace(/[*:]$/, '');
      }
    }

    // Check if field is inside a label
    const parentLabel = field.closest('label');
    if (parentLabel && parentLabel.textContent) {
      // Get text excluding the field's own text content
      const labelClone = parentLabel.cloneNode(true) as HTMLElement;
      labelClone.removeChild(labelClone.querySelector(field.tagName.toLowerCase()) as HTMLElement);
      return labelClone.textContent?.trim().replace(/[*:]$/, '') || '';
    }

    // Check for field title attribute
    if (field.hasAttribute('title')) {
      return field.getAttribute('title') || '';
    }

    // Check for aria-label
    if (field.hasAttribute('aria-label')) {
      return field.getAttribute('aria-label') || '';
    }

    return '';
  }

  /**
   * Convert camelCase, snake_case, or dash-case field names to human-readable text
   */
  private humanizeFieldName(name: string): string {
    // Remove common prefixes
    name = name.replace(/^(user|field|input|form|txt)[-_]?/i, '');

    // Replace separators and capitalize
    return name
      .replace(/[-_]/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase()
      .replace(/\b\w/g, s => s.toUpperCase());
  }

  /**
   * Convert input type to human-readable text
   */
  private humanizeFieldType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'text': 'Text',
      'email': 'Email Address',
      'password': 'Password',
      'tel': 'Phone Number',
      'url': 'Website',
      'search': 'Search Term',
      'number': 'Number'
    };

    return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
  }

  /**
   * Add ARIA attributes to enhance field accessibility
   */
  private enhanceWithAriaAttributes(field: HTMLElement): void {
    // Check if field already has ARIA attributes
    if (field.hasAttribute('aria-describedby') || !field.id) return;

    // Look for hint elements that may describe this field
    const editorElement = this.editor?.getElement();
    if (!editorElement) return;
    const hints = Array.from(editorElement.querySelectorAll('.feather-field-hint') || []);
    const relatedHints = hints.filter(hint => hint.getAttribute('data-for') === field.id);

    if (relatedHints.length > 0) {
      // Create a space-separated list of hint IDs
      const hintIds = relatedHints.map(hint => hint.id).join(' ');
      field.setAttribute('aria-describedby', hintIds);
    }
  }

  /**
   * Generate a unique ID
   */
  private generateUniqueId(): string {
    return `field-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
  }

  /**
   * Show feedback message
   */
  private showFeedbackMessage(message: string): void {
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = 'fixed bottom-5 right-5 bg-gray-800 dark:bg-slate-700 text-white py-2 px-4 rounded font-sans z-[9999] animate-fade-in-out';
    feedback.textContent = message;
    feedback.setAttribute('role', 'status');
    feedback.setAttribute('aria-live', 'polite');

    // Add to the document
    document.body.appendChild(feedback);

    // Remove after a delay
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback);
      }
    }, 3000);
  }

  /**
   * Clean up on destroy
   */
  public destroy(): void {
    // Disconnect observer
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    // Remove placeholders and hints
    this.removePlaceholdersAndHints();

    // Clear processed fields set
    this.processedFields.clear();

    // Call parent destroy
    super.destroy();
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<PlaceholderPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new PlaceholderPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: PlaceholderPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
