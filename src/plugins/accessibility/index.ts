/**
 * FeatherJS Accessibility Plugins
 * 
 * This module exports accessibility-focused plugins for the FeatherJS editor:
 * - a11y-checker: Scans content for accessibility issues
 * - alt-text-inspector: Enforces alt text on images
 * - landmarks: Visualizes document structure and ARIA landmarks
 * - placeholder: Manages accessible form field placeholders and hints
 */

import type { Editor, Plugin } from '../../types';
import { createPlugin } from '../plugin-factory';

import A11yCheckerPlugin from './a11y-checker';
import AltTextInspectorPlugin from './alt-text-inspector';
import LandmarksPlugin from './landmarks';
import PlaceholderPlugin from './placeholder';

// Export individual plugins
export {
  A11yCheckerPlugin,
  AltTextInspectorPlugin,
  LandmarksPlugin,
  PlaceholderPlugin
};

// Export a combined plugin group for convenience
export const AccessibilityPlugins: Plugin[] = [
  A11yCheckerPlugin,
  AltTextInspectorPlugin,
  LandmarksPlugin,
  PlaceholderPlugin
];

// Create a group plugin that registers all accessibility plugins
// This allows for easily registering all accessibility plugins at once
const groupPlugin: Plugin = createPlugin(
  'accessibility-group',
  (editor: Editor) => {
    // Initialize all accessibility plugins
    AccessibilityPlugins.forEach(plugin => {
      plugin.init(editor);
    });
    
    // Return null since there's no instance to track
    return null;
  },
  () => {
    // Clean up all accessibility plugins
    AccessibilityPlugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
);

// Default export for convenience
export default groupPlugin;