import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Accessibility Issue interface
 */
interface A11yIssue {
  id: string;
  type: 'error' | 'warning' | 'info';
  element: HTMLElement;
  message: string;
  impact: 'critical' | 'serious' | 'moderate' | 'minor';
  help: string;
  helpUrl?: string;
  fix?: () => void;
}

/**
 * A11y Checker plugin configuration
 */
const config: PluginConfig = {
  id: 'a11y-checker',
  name: 'Accessibility Checker',
  description: 'Check content for accessibility issues like contrast, headings, and alt-text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'a11y-checker',
      command: 'a11y-checker',
      icon: '♿',
      label: 'Accessibility',
      tooltip: 'Check accessibility',
      group: 'accessibility',
      ariaLabel: 'Check content accessibility',
    }
  ],
  shortcuts: [] // No shortcuts for accessibility checker
};

/**
 * A11y Checker plugin implementation
 * Scans content for accessibility issues such as contrast, headings, and alt-text
 */
class A11yCheckerPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  private issues: A11yIssue[] = [];
  private isScanning = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the a11y checker command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'a11y-checker') {
      if (this.isDialogOpen) {
        this.closeDialog();
      } else {
        this.startAccessibilityCheck();
      }
    }
  }
  
  /**
   * Start the accessibility checking process
   */
  private startAccessibilityCheck(): void {
    if (!this.editor || this.isScanning) return;
    
    // Reset issues
    this.issues = [];
    
    // Show scanning dialog
    this.showScanningDialog();
    
    // Start the check after a short delay to allow the UI to update
    setTimeout(() => {
      this.performAccessibilityCheck();
    }, 100);
  }
  
  /**
   * Show a dialog indicating that accessibility checking is in progress
   */
  private showScanningDialog(): void {
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/50 z-[9998]';
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-800 border border-gray-300 dark:border-slate-600 shadow-md z-[10000] max-w-2xl w-[90%] max-h-[80vh] flex flex-col rounded-lg font-sans text-gray-900 dark:text-slate-200';
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'a11y-checker-dialog-title');
    
    // Create dialog content programmatically
    // Header
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center px-4 py-3 border-b border-gray-200 dark:border-slate-700 bg-gray-100 dark:bg-slate-700 rounded-t-lg';
    
    const titleH3 = document.createElement('h3');
    titleH3.id = 'a11y-checker-dialog-title';
    titleH3.className = 'm-0 text-base font-semibold flex items-center gap-2';
    const titleIcon = document.createElement('span');
    titleIcon.className = 'text-lg'; // For icon size
    titleIcon.textContent = '♿';
    titleH3.appendChild(titleIcon);
    titleH3.appendChild(document.createTextNode(' Accessibility Checker'));
    header.appendChild(titleH3);
    
    const closeButtonDialog = document.createElement('button');
    closeButtonDialog.type = 'button';
    closeButtonDialog.className = 'bg-transparent border-none text-xl cursor-pointer text-gray-500 dark:text-slate-400 p-1 leading-none hover:text-gray-700 dark:hover:text-slate-200';
    closeButtonDialog.setAttribute('aria-label', 'Close');
    closeButtonDialog.textContent = '×';
    header.appendChild(closeButtonDialog);
    this.dialog.appendChild(header);

    // Content area for scanning message
    const contentArea = document.createElement('div');
    contentArea.className = 'p-4 overflow-y-auto flex-grow'; // Base class for content area
    
    const scanningDiv = document.createElement('div');
    scanningDiv.className = 'flex items-center justify-center py-10 px-5 text-center text-gray-600 dark:text-slate-300 text-sm';
    
    const spinner = document.createElement('div');
    spinner.className = 'border-4 border-gray-100 dark:border-slate-700 border-t-blue-500 rounded-full w-[30px] h-[30px] animate-spin mr-4';
    scanningDiv.appendChild(spinner);
    
    const scanningText = document.createElement('div');
    scanningText.textContent = 'Scanning content for accessibility issues...';
    scanningDiv.appendChild(scanningText);
    
    contentArea.appendChild(scanningDiv);
    this.dialog.appendChild(contentArea);
        
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    this.isScanning = true;
    
    // Setup close button
    const closeButton = this.dialog.querySelector('.feather-a11y-checker-close');
    closeButton?.addEventListener('click', () => {
      this.closeDialog();
    });
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
  }
  
  /**
   * Perform the actual accessibility checking
   */
  private performAccessibilityCheck(): void {
    if (!this.editor || !this.dialog) return;
    
    // Check headings hierarchy
    this.checkHeadingsHierarchy();
    
    // Check images for alt text
    this.checkImagesForAltText();
    
    // Check color contrast
    this.checkColorContrast();
    
    // Check for empty links and buttons
    this.checkEmptyInteractiveElements();
    
    // Check for other issues
    this.checkOtherAccessibilityIssues();
    
    // Update dialog with results
    this.updateResultsDialog();
  }
  
  /**
   * Check headings hierarchy
   * Makes sure headings follow a proper structure (h1 > h2 > h3)
   */
  private checkHeadingsHierarchy(): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return; // Exit if editor element is not available
    
    const headings = editorElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let lastLevel = 0;
 
    headings?.forEach(heading => {
      const currentHeading = heading as HTMLHeadingElement;
      const level = parseInt(currentHeading.tagName.substring(1), 10);
 
      if (level > lastLevel + 1) {
        this.issues.push({
          id: `heading-skip-${this.issues.length}`,
          type: 'warning',
          element: currentHeading,
          message: `Heading level skipped: h${lastLevel} is followed by h${level}. Headings should follow a logical hierarchy.`,
          impact: 'moderate',
          help: 'Ensure headings descend by only one level at a time (e.g., h1 to h2, h2 to h3).',
          helpUrl: 'https://www.w3.org/WAI/tutorials/page-structure/headings/',
          fix: () => {
            if (!this.editor) return;
            const currentHeadingElement = heading as HTMLHeadingElement;
            const fixLevel = lastLevel + 1;
            const newHeadingTag = `h${fixLevel}`;
            const newHeading = document.createElement(newHeadingTag);
            newHeading.innerHTML = currentHeadingElement.innerHTML;
            // Preserve attributes
            for (let i = 0; i < currentHeadingElement.attributes.length; i++) {
              const attr = currentHeadingElement.attributes[i];
              newHeading.setAttribute(attr.name, attr.value);
            }
            currentHeadingElement.replaceWith(newHeading);
            this.startAccessibilityCheck(); // Re-run check after fixing
          }
        });
      }
 
      lastLevel = level;
    });
 
    if (editorElement && headings && headings.length > 0) {
      const firstHeading = headings[0] as HTMLHeadingElement;
      // Check if the first heading is within the main content area, not potential UI elements
      if (editorElement.contains(firstHeading)) {
        const firstLevel = parseInt(firstHeading.tagName.substring(1), 10);
        if (firstLevel !== 1) {
          this.issues.push({
            id: `first-heading-not-h1-${this.issues.length}`,
            type: 'warning',
            element: firstHeading,
            message: `The first heading on the page should ideally be an H1. Found H${firstLevel} first.`,
            impact: 'moderate',
            help: 'Use H1 for the main title of the page content.',
            helpUrl: 'https://www.w3.org/WAI/tutorials/page-structure/headings/',
            // Optional: Add a fix function if applicable
          });
        }
      }
    }
 
    const h1Headings = editorElement?.querySelectorAll('h1');
    if (h1Headings && h1Headings.length > 1) {
      h1Headings.forEach((h, index) => {
        if (index > 0) { // Only flag subsequent H1s
          this.issues.push({
            id: `multiple-h1-${this.issues.length}`,
            type: 'warning',
            element: h as HTMLHeadingElement,
            message: 'Multiple H1 headings found. Typically, a page should have only one H1.',
            impact: 'moderate',
            help: 'Use only one H1 for the main page title. Use H2-H6 for subheadings.',
            helpUrl: 'https://www.w3.org/WAI/tutorials/page-structure/headings/',
            fix: () => {
              if (!this.editor) return;
              const currentH1 = h as HTMLHeadingElement;
              const newH2 = document.createElement('h2');
              newH2.innerHTML = currentH1.innerHTML;
              // Preserve attributes
              for (let i = 0; i < currentH1.attributes.length; i++) {
                const attr = currentH1.attributes[i];
                newH2.setAttribute(attr.name, attr.value);
              }
              currentH1.replaceWith(newH2);
              this.startAccessibilityCheck(); // Re-run check
            }
          });
        }
      });
    }
  }
  
  /**
   * Check images for alt text
   */
  private checkImagesForAltText(): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return; // Exit if editor element is not available
    
    const images = editorElement.querySelectorAll('img');
    
    images?.forEach(img => {
      const imageElement = img as HTMLImageElement;
      const alt = imageElement.getAttribute('alt');
      
      // Missing alt attribute
      if (alt === null) {
        this.issues.push({
          id: `img-alt-missing-${this.issues.length}`,
          type: 'error',
          element: imageElement,
          message: 'Image is missing an alt attribute.',
          impact: 'critical',
          help: 'Provide descriptive alt text for informative images or use alt="" for decorative images.',
          helpUrl: 'https://www.w3.org/WAI/tutorials/images/decision-tree/',
          fix: () => {
            const newAlt = prompt(`Enter alt text for image source: ${imageElement.src} (leave empty if decorative):`, '');
            if (newAlt !== null) { // Handle cancel
              imageElement.setAttribute('alt', newAlt);
              this.startAccessibilityCheck(); // Re-run check
            }
          }
        });
      } else if (alt.trim() === '' && !this.isLikelyDecorativeImage(imageElement)) {
        // Alt attribute is empty, but image might be informative
        this.issues.push({
          id: `img-alt-empty-informative-${this.issues.length}`,
          type: 'warning',
          element: imageElement,
          message: 'Image has an empty alt attribute (alt=""), but may be informative. Verify if it is purely decorative.',
          impact: 'serious',
          help: 'If the image conveys information, provide descriptive alt text. If it is purely decorative, alt="" is appropriate.',
          fix: () => {
            const newAlt = prompt(`Image source: ${imageElement.src}. Enter alt text (required if informative):`, '');
            if (newAlt !== null && newAlt.trim() !== '') {
              imageElement.setAttribute('alt', newAlt);
              this.startAccessibilityCheck(); // Re-run check
            }
          }
        });
      } else if (alt.length > 0 && alt.length < 5) {
        // Alt text might be too short or generic
        this.issues.push({
          id: `img-alt-short-${this.issues.length}`,
          type: 'warning',
          element: imageElement,
          message: `Alt text "${alt}" is very short. Ensure it accurately describes the image content or function.`,
          impact: 'minor',
          help: 'Good alt text is concise but descriptive. Avoid generic terms like "image" or "picture".',
          fix: () => {
            const newAlt = prompt(`Update alt text for image source: ${imageElement.src}:`, alt);
            if (newAlt !== null && newAlt !== alt) {
              imageElement.setAttribute('alt', newAlt);
              this.startAccessibilityCheck(); // Re-run check
            }
          }
        });
      }
    });
  }
  
  /**
   * Check if an image is likely decorative
   * @param img The image element to check
   * @returns True if likely decorative, false otherwise
   */
  private isLikelyDecorativeImage(img: HTMLImageElement): boolean {
    // Check dimensions - small images may be decorative
    if (img.width < 50 || img.height < 50) {
      return true;
    }
    
    // Check if image has a role="presentation" or role="none"
    if (img.getAttribute('role') === 'presentation' || img.getAttribute('role') === 'none') {
      return true;
    }
    
    // Check filename for indicators of decorative images
    const src = img.getAttribute('src') || '';
    const decorativePatterns = ['divider', 'separator', 'line', 'bullet', 'icon', 'border', 'bg', 'background'];
    if (decorativePatterns.some(pattern => src.toLowerCase().includes(pattern))) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Check for color contrast issues
   */
  private checkColorContrast(): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return; // Exit if editor element is not available
    
    const elements = editorElement.querySelectorAll('*:not(script):not(style)');
    
    elements?.forEach(el => {
      const element = el as Element;
      // Skip elements that are likely not visible or don't contain text
      if (!(element instanceof HTMLElement) || element.offsetParent === null || !element.textContent?.trim()) {
        return;
      }
      
      const style = window.getComputedStyle(element);
      const color = style.color;
      const backgroundColor = this.getBackgroundColor(element as HTMLElement); // Cast here is okay as we checked instanceof above
 
      if (color && backgroundColor) {
        try {
          const colorRGB = this.parseColor(color);
          const backgroundRGB = this.parseColor(backgroundColor);
 
          // Ensure colors were parsed correctly (might fail for 'transparent' or invalid formats)
          if (!colorRGB || !backgroundRGB) return;
 
          const contrastRatio = this.calculateContrastRatio(colorRGB, backgroundRGB);
 
          // Check against WCAG AA guidelines (4.5:1 for normal text, 3:1 for large text)
          const fontSize = parseFloat(style.fontSize);
          const fontWeight = style.fontWeight;
          // WCAG definition: 18pt (~24px) normal weight or 14pt (~18.66px) bold
          const isLargeText = fontSize >= 24 || (fontSize >= 18.66 && (parseInt(fontWeight) >= 700 || fontWeight === 'bold'));
          const requiredRatio = isLargeText ? 3 : 4.5;
 
          if (contrastRatio < requiredRatio) {
            this.issues.push({
              id: `contrast-${this.issues.length}`,
              type: 'error',
              element: element as HTMLElement, // Assert as HTMLElement
              message: `Low text contrast (${contrastRatio.toFixed(2)}:1). Required ratio is ${requiredRatio}:1 for ${isLargeText ? 'large' : 'normal'} text.`,
              impact: 'serious',
              help: 'Ensure sufficient contrast between text and background colors.',
              helpUrl: 'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html'
              // Fix might involve suggesting colors or opening a color picker
            });
          }
        } catch (error) {
          console.error('Error checking color contrast:', error);
        }
      }
    });
  }
  
  /**
   * Get the background color of an element, traversing up the DOM if necessary
   * @param element The element to check
   * @returns The background color as a string
   */
  private getBackgroundColor(element: HTMLElement): string {
    let currentEl: Element | null = element;
    let bgColor = 'transparent'; // Initialize with transparent
 
    // Traverse up the DOM tree until a non-transparent background is found or the editor root is reached
    while (currentEl) {
      bgColor = window.getComputedStyle(currentEl).backgroundColor;
 
      // Check if the color is fully opaque and not transparent
      if (bgColor && bgColor !== 'transparent' && !bgColor.startsWith('rgba(0, 0, 0, 0)')) {
        break;
      }
 
      // Stop if we reach the editor root
      if (currentEl === this.editor?.getElement()) {
        // If no explicit background color is found up the chain, assume transparent against white
        // Note: This is a simplification. True background might be complex (gradients, images).
        // A more robust solution might involve rendering checks, but that's complex.
        bgColor = 'rgba(255, 255, 255, 0)'; // Assume transparent white if no background found
        break;
      }
      
      currentEl = currentEl.parentElement;
    }
    
    return bgColor;
  }
  
  /**
   * Parse a color string into RGB components
   * @param color Color string (rgb, rgba, hex)
   * @returns RGB color object
   */
  private parseColor(color: string): { r: number; g: number; b: number } | null {
    if (color.startsWith('rgb')) {
      // Improved regex to handle spaces better
      const match = color.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*[\d.]+\s*)?\)/);
      if (match) {
        return { r: parseInt(match[1]), g: parseInt(match[2]), b: parseInt(match[3]) };
      }
      return null; // Could not parse RGB
    } else if (color.startsWith('#')) {
      let hex = color.substring(1);
      // Handle shorthand hex (e.g., #03F)
      if (hex.length === 3) {
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
      }
      // Ensure it's a 6-digit hex code now
      if (hex.length !== 6) {
        return null;
      }
      const bigint = parseInt(hex, 16);
      const r = (bigint >> 16) & 255;
      const g = (bigint >> 8) & 255;
      const b = bigint & 255;
      return { r, g, b };
    }
    // Add support for named colors if needed (would require a lookup table)
    console.warn(`Cannot parse color: ${color}`);
    return null; // Unsupported or invalid format
  }
  
  /**
   * Calculate contrast ratio between two colors
   * @param color1 First color
   * @param color2 Second color
   * @returns Contrast ratio
   */
  private calculateContrastRatio(color1: { r: number; g: number; b: number }, color2: { r: number; g: number; b: number }): number {
    // Calculate relative luminance for each color
    const l1 = this.calculateRelativeLuminance(color1);
    const l2 = this.calculateRelativeLuminance(color2);
    
    // Calculate contrast ratio
    const lighterLuminance = Math.max(l1, l2);
    const darkerLuminance = Math.min(l1, l2);
    
    return (lighterLuminance + 0.05) / (darkerLuminance + 0.05);
  }
  
  /**
   * Calculate relative luminance of a color
   * @param color RGB color object
   * @returns Relative luminance
   */
  private calculateRelativeLuminance(color: { r: number; g: number; b: number }): number {
    // Normalize RGB values
    const r = color.r / 255;
    const g = color.g / 255;
    const b = color.b / 255;
    
    // Calculate RGB components
    const R = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
    const G = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
    const B = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);
    
    // Calculate luminance (per WCAG formula)
    return 0.2126 * R + 0.7152 * G + 0.0722 * B;
  }
  
  /**
   * Check for empty interactive elements like links and buttons
   */
  private checkEmptyInteractiveElements(): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    
    // Check empty links
    const links = editorElement.querySelectorAll('a');
    links?.forEach(link => {
      const anchorElement = link as HTMLAnchorElement;
      // Check for text content, accessible image/svg, or aria attributes
      if (!anchorElement.textContent?.trim() &&
          !anchorElement.querySelector('img[alt]:not([alt=""]), svg[aria-label], svg[aria-labelledby]') &&
          !anchorElement.getAttribute('aria-label') &&
          !anchorElement.getAttribute('aria-labelledby')) {
        this.issues.push({
          id: `empty-link-${this.issues.length}`,
          type: 'error',
          element: anchorElement,
          message: 'Link has no discernible text content (text, accessible image, or aria-label).',
          impact: 'serious',
          help: 'Provide descriptive text within the link, or use aria-label for icon-only links.',
          fix: () => {
            const newText = prompt(`Enter descriptive text for link URL: ${anchorElement.href}:`, '');
            if (newText !== null && newText.trim() !== '') {
              anchorElement.textContent = newText;
              this.startAccessibilityCheck(); // Re-run check
            }
          }
        });
      }
    });
    
    // Check empty buttons
    const buttons = editorElement.querySelectorAll('button');
    buttons?.forEach(button => {
      const buttonElement = button as HTMLButtonElement;
      // Check for text content, accessible image/svg, or aria attributes
      if (!buttonElement.textContent?.trim() &&
          !buttonElement.querySelector('img[alt]:not([alt=""]), svg[aria-label], svg[aria-labelledby]') &&
          !buttonElement.getAttribute('aria-label') &&
          !buttonElement.getAttribute('aria-labelledby')) {
        this.issues.push({
          id: `empty-button-${this.issues.length}`,
          type: 'error',
          element: buttonElement,
          message: 'Button has no discernible text content (text, accessible image, or aria-label).',
          impact: 'serious',
          help: 'Provide descriptive text within the button, or use aria-label for icon-only buttons.',
          fix: () => {
            const newText = prompt('Enter descriptive text for the button:', '');
            if (newText !== null && newText.trim() !== '') {
              buttonElement.textContent = newText;
              this.startAccessibilityCheck(); // Re-run check
            }
          }
        });
      }
    });
  }
  
  /**
   * Check for other common accessibility issues
   */
  private checkOtherAccessibilityIssues(): void {
    if (!this.editor) return;
    
    const editorElement = this.editor.getElement();
    if (!editorElement) return;
    
    // Check for tables without headers
    const tables = editorElement.querySelectorAll('table');
    tables?.forEach(table => {
      const tableElement = table as HTMLTableElement;
      if (!tableElement.querySelector('th')) {
        this.issues.push({
          id: `table-no-headers-${this.issues.length}`,
          type: 'warning',
          element: tableElement,
          message: 'Table has no header cells',
          impact: 'serious',
          help: 'Tables should include header cells to provide context for data cells.',
          helpUrl: 'https://www.w3.org/WAI/tutorials/tables/'
        });
      }
    });
    
    // Check for form fields without labels
    const fields = editorElement.querySelectorAll('input, select, textarea');
    fields?.forEach(field => {
      const fieldElement = field as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
      const fieldId = fieldElement.id;
      let hasLabel = false;
      
      if (fieldId) {
        // Check for associated label
        hasLabel = !!editorElement.querySelector(`label[for="${fieldId}"]`);
      } else {
        // Check for wrapping label
        hasLabel = !!fieldElement.closest('label');
      }
      
      if (!hasLabel) {
        this.issues.push({
          id: `field-no-label-${this.issues.length}`,
          type: 'error',
          element: fieldElement,
          message: 'Form field has no associated label',
          impact: 'serious',
          help: 'All form fields must have associated labels.',
          helpUrl: 'https://www.w3.org/WAI/tutorials/forms/labels/',
          fix: () => {
            // Generate a unique ID
            const newId = `field-${Date.now()}`;
            fieldElement.setAttribute('id', newId);
            
            // Create a label element
            const label = document.createElement('label');
            label.setAttribute('for', newId);
            const promptText = prompt(`Enter label text for the form field (name: ${fieldElement.name || 'N/A'}, id: ${fieldElement.id || 'N/A'}):`, '');
            label.textContent = promptText || 'Label';
            
            // Insert the label before the field
            fieldElement.parentNode?.insertBefore(label, fieldElement);
            
            // Trigger input event for history
            this.editor?.getElement()?.dispatchEvent(
              new InputEvent('input', { bubbles: true, cancelable: true })
            );
          }
        });
      }
    });
  }
  
  /**
   * Update the dialog with accessibility check results
   */
  private updateResultsDialog(): void {
    if (!this.dialog) return;
    
    // Stop the scanning state
    this.isScanning = false;
    
    const dialogContentContainer = this.dialog.querySelector('.p-4.overflow-y-auto.flex-grow'); // Get the main content area
    if (!dialogContentContainer) return;
    
    dialogContentContainer.innerHTML = ''; // Clear previous content (e.g., scanning message)

    // Group issues by type (declare once)
    const errorCount = this.issues.filter(issue => issue.type === 'error').length;
    const warningCount = this.issues.filter(issue => issue.type === 'warning').length;
    const infoCount = this.issues.filter(issue => issue.type === 'info').length;
    
    if (this.issues.length === 0) {
      const noIssuesDiv = document.createElement('div');
      noIssuesDiv.className = 'text-center p-5 text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/30 rounded-md mt-2.5';
      const iconDiv = document.createElement('div');
      iconDiv.className = 'text-3xl mb-2.5';
      iconDiv.textContent = '✓';
      const p1 = document.createElement('p');
      p1.textContent = 'No accessibility issues detected.';
      const p2 = document.createElement('p');
      p2.textContent = 'Great job! Your content appears to meet accessibility standards.';
      noIssuesDiv.appendChild(iconDiv);
      noIssuesDiv.appendChild(p1);
      noIssuesDiv.appendChild(p2);
      dialogContentContainer.appendChild(noIssuesDiv);
    } else {
      const issuesUl = document.createElement('ul');
      issuesUl.className = 'mt-2.5 space-y-3'; // space-y-3 for margin between items

      this.issues.forEach((issue, index) => {
        const li = document.createElement('li');
        li.className = 'border border-gray-200 dark:border-slate-700 rounded-md p-3 bg-white dark:bg-slate-800';
        li.dataset.issueIndex = index.toString();

        const issueHeader = document.createElement('div');
        issueHeader.className = 'flex justify-between items-center mb-2';
        
        const issueTitle = document.createElement('h4');
        issueTitle.className = 'font-semibold text-sm m-0';
        issueTitle.textContent = issue.message;
        issueHeader.appendChild(issueTitle);
        
        const impactSpan = document.createElement('span');
        let impactClasses = 'text-xs px-1.5 py-0.5 rounded';
        switch(issue.impact) {
            case 'critical': impactClasses += ' bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400'; break;
            case 'serious': impactClasses += ' bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400'; break;
            case 'moderate': impactClasses += ' bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400'; break;
            case 'minor': impactClasses += ' bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'; break;
        }
        impactSpan.className = impactClasses;
        impactSpan.textContent = issue.impact;
        issueHeader.appendChild(impactSpan);
        li.appendChild(issueHeader);

        const helpDiv = document.createElement('div');
        helpDiv.className = 'text-[13px] text-gray-500 dark:text-slate-400 mb-3';
        helpDiv.textContent = issue.help;
        li.appendChild(helpDiv);

        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'flex gap-2 flex-wrap'; // Added flex-wrap
        
        const highlightBtn = document.createElement('button');
        highlightBtn.type = 'button';
        highlightBtn.className = 'text-[13px] py-1 px-2 rounded border-none bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-slate-200 cursor-pointer hover:opacity-90';
        highlightBtn.dataset.action = 'highlight';
        highlightBtn.textContent = 'Highlight Element';
        actionsDiv.appendChild(highlightBtn);

        if (issue.helpUrl) {
          const learnMoreLink = document.createElement('a');
          learnMoreLink.href = issue.helpUrl;
          learnMoreLink.className = 'text-[13px] py-1 px-2 rounded border-none bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-slate-200 cursor-pointer hover:opacity-90 no-underline hover:underline';
          learnMoreLink.target = '_blank';
          learnMoreLink.rel = 'noopener noreferrer';
          learnMoreLink.textContent = 'Learn More';
          actionsDiv.appendChild(learnMoreLink);
        }

        if (issue.fix) {
          const fixBtn = document.createElement('button');
          fixBtn.type = 'button';
          fixBtn.className = 'text-[13px] py-1 px-2 rounded border-none bg-blue-600 text-white hover:bg-blue-700 cursor-pointer hover:opacity-90';
          fixBtn.dataset.action = 'fix';
          fixBtn.textContent = 'Quick Fix';
          actionsDiv.appendChild(fixBtn);
        }
        li.appendChild(actionsDiv);
        issuesUl.appendChild(li);
      });
      dialogContentContainer.appendChild(issuesUl);
    }
    
    // Remove existing footer if any, before adding a new one
    const existingFooter = this.dialog.querySelector('.a11y-checker-footer-dynamic');
    existingFooter?.remove();

    // Add footer with summary and actions
    const footer = document.createElement('div');
    footer.className = 'a11y-checker-footer-dynamic mt-5 flex justify-between items-center pt-3 border-t border-gray-200 dark:border-slate-700 px-4 pb-3'; // Added px-4 pb-3 to match header padding
    
    const summaryDiv = document.createElement('div');
    summaryDiv.className = 'text-sm text-gray-700 dark:text-slate-300';
    summaryDiv.textContent = this.issues.length > 0 ? 
      `Found ${this.issues.length} ${this.issues.length === 1 ? 'issue' : 'issues'} (${errorCount} errors, ${warningCount} warnings, ${infoCount} info)` : 
      'No accessibility issues found';
    footer.appendChild(summaryDiv);
      
    const footerActionsDiv = document.createElement('div');
    footerActionsDiv.className = 'flex gap-2.5';

    if (this.issues.length > 0) {
      const rescanButtonElem = document.createElement('button');
      rescanButtonElem.type = 'button';
      rescanButtonElem.id = 'rescan-button';
      rescanButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer bg-gray-100 dark:bg-slate-700 text-gray-700 dark:text-slate-200 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed';
      rescanButtonElem.textContent = 'Re-scan Content';
      footerActionsDiv.appendChild(rescanButtonElem);
    }

    const closeButtonFooter = document.createElement('button');
    closeButtonFooter.type = 'button';
    closeButtonFooter.id = 'close-button'; // Ensure this ID is unique or handled correctly
    closeButtonFooter.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer bg-blue-600 text-white hover:bg-blue-700 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed';
    closeButtonFooter.textContent = 'Close';
    footerActionsDiv.appendChild(closeButtonFooter);
    
    footer.appendChild(footerActionsDiv);
    this.dialog.appendChild(footer);
    
    // Add event listeners
    this.setupResultsDialogEventListeners();
  }
  
  /**
   * Setup event listeners for the results dialog
   */
  private setupResultsDialogEventListeners(): void {
    if (!this.dialog) return;
    
    // Re-scan button
    const rescanButton = this.dialog.querySelector('#rescan-button');
    rescanButton?.addEventListener('click', () => {
      this.startAccessibilityCheck();
    });
    
    // Close button
    const closeButton = this.dialog.querySelector('#close-button');
    closeButton?.addEventListener('click', () => {
      this.closeDialog();
    });
    
    // Issue action buttons
    const issueItems = this.dialog.querySelectorAll('.feather-a11y-checker-issue');
    issueItems.forEach(item => {
      // Get the issue index
      const index = parseInt(item.getAttribute('data-issue-index') || '0');
      const issue = this.issues[index];
      
      // Highlight button
      const highlightButton = item.querySelector('[data-action="highlight"]');
      highlightButton?.addEventListener('click', () => {
        this.highlightElement(issue.element);
      });
      
      // Fix button
      const fixButton = item.querySelector('[data-action="fix"]');
      fixButton?.addEventListener('click', () => {
        if (issue.fix) {
          issue.fix();
          
          // Remove this issue from the list
          this.issues.splice(index, 1);
          
          // Update dialog with new results
          this.updateResultsDialog();
        }
      });
    });
  }
  
  /**
   * Highlight an element in the editor
   * @param element The element to highlight
   */
  private highlightElement(element: HTMLElement): void {
    // Remove any existing highlights
    const editorElement = this.editor?.getElement();
    if (!editorElement) return;
    const highlightedElements = editorElement.querySelectorAll('.js-a11y-highlight'); // Use a JS hook class
    highlightedElements?.forEach((el: Element) => {
      el.classList.remove('js-a11y-highlight', 'outline', 'outline-2', 'outline-orange-500', 'outline-offset-2');
    });
    
    // Add highlight class
    element.classList.add('js-a11y-highlight', 'outline', 'outline-2', 'outline-orange-500', 'outline-offset-2');
    
    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // Remove highlight after a few seconds
    setTimeout(() => {
      element.classList.remove('js-a11y-highlight', 'outline', 'outline-2', 'outline-orange-500', 'outline-offset-2');
    }, 3000);
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closeDialog();
    }
  };
  
  /**
   * Close the dialog
   */
  private closeDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove backdrop
    const backdrop = document.querySelector('.feather-a11y-checker-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    // Remove dialog
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    this.isScanning = false;
    
    // Clear issues
    this.issues = [];
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<A11yCheckerPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new A11yCheckerPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: A11yCheckerPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
