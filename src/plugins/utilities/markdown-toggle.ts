/**
 * Markdown Toggle Plugin for FeatherJS
 * Converts editor content between **Markdown** and **HTML**.
 */

import type { Editor, Plugin } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';

/* -------------------------------------------------------------------------- */
/*                              Config & metadata                             */
/* -------------------------------------------------------------------------- */

export interface MarkdownToggleConfig {
  defaultFormat?: 'markdown' | 'html';
  preserveStructure?: boolean;
  markdownOptions?: {
    gfm?: boolean;
    sanitize?: boolean;
  };
}

const DEFAULTS: Required<MarkdownToggleConfig> = {
  defaultFormat: 'html',
  preserveStructure: true,
  markdownOptions: {
    gfm: true,
    sanitize: true
  }
};

const META: PluginConfig = {
  id: 'markdown-toggle',
  name: 'Markdown Toggle',
  description: 'Switch between Markdown and HTML',
  version: '2.0.0',
  toolbarItems: [
    {
      id: 'markdown-toggle',
      command: 'markdown-toggle',
      icon: 'MD↔︎',
      label: 'Markdown Toggle',
      tooltip: 'Toggle Markdown / HTML',
      group: 'utility',
      ariaLabel: 'Toggle Markdown / HTML'
    }
  ]
};

/* -------------------------------------------------------------------------- */
/*                                 Utilities                                  */
/* -------------------------------------------------------------------------- */

/** Very small HTML sanitizer using DOM – not bullet-proof but blocks tags & JS. */
function sanitizeHtml(dirtyHtml: string): string {
  const template = document.createElement('template');
  template.innerHTML = dirtyHtml;
  template.content.querySelectorAll('script,iframe,object').forEach((n) => n.remove());
  return template.innerHTML;
}

/** HTML-escaper for code blocks / inline code. */
function escapeHtml(raw: string): string {
  return raw
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/* -------------------------------------------------------------------------- */
/*                             Plugin implementation                           */
/* -------------------------------------------------------------------------- */

class MarkdownTogglePlugin extends BasePlugin {
  /* merged cfg */
  private cfg: ReturnType<typeof Object.assign>;

  /* current editor mode */
  private format: 'markdown' | 'html';

  /* abort controller for all listeners */
  private abort = new AbortController();

  private isRunningConversion = false;

  constructor(userCfg: Partial<MarkdownToggleConfig> = {}) {
    super(META);
    this.cfg = Object.assign({}, DEFAULTS, userCfg);
    this.format = this.cfg.defaultFormat;
  }

  /* ------------------------- life-cycle hooks ------------------------- */

  protected override onInit(): void {
    if (!this.editor) return;
    this.registerToolbarItems();
    this.updateToolbarButton();

    /* If requested default is Markdown, convert once on load */
    if (this.cfg.defaultFormat === 'markdown') {
      this.convertHtmlToMarkdown();
    }
  }

  protected override handleCommand(command: string): void {
    if (command === 'markdown-toggle') this.toggleFormat();
  }

  /* --------------------------- main actions --------------------------- */

  /** Toggle between HTML ↔︎ Markdown, preserving undo stack. */
  private toggleFormat(): void {
    if (this.isRunningConversion || !this.editor) return;

    this.isRunningConversion = true;
    try {
      if (this.format === 'html') {
        this.convertHtmlToMarkdown();
      } else {
        this.convertMarkdownToHtml();
      }
      this.emitFeedback();
      this.updateToolbarButton();
    } finally {
      this.isRunningConversion = false;
    }
  }

  /* ------------------- HTML → Markdown (encoder) --------------------- */

  private convertHtmlToMarkdown(): void {
    const editorElement = this.editor!.getElement();
    const htmlContent = editorElement.innerHTML;
    const markdownContent = this.encodeHtmlToMarkdown(htmlContent);
    
    // Replace editor body with a `<pre><code>` wrapper
    // Using escapeHtml to ensure the markdown itself is displayed literally within the code tag
    const preWrappedMarkdown = `<pre class="feather-markdown-content" contenteditable="true"><code>${escapeHtml(markdownContent)}</code></pre>`;

    editorElement.innerHTML = preWrappedMarkdown;
    editorElement.classList.add('feather-markdown-mode');

    this.format = 'markdown';
    this.updateToolbarButton();

    // Trigger input event so history might catch the change
    editorElement.dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }

  private encodeHtmlToMarkdown(html: string): string {
    const container = document.createElement('div');
    container.innerHTML = html;

    /** Recursive helper converts child nodes to Markdown text */
    const walk = (node: Node): string => {
      if (node.nodeType === Node.TEXT_NODE) {
        return (node as Text).data;
      }
      if (node.nodeType !== Node.ELEMENT_NODE) return '';

      const element = node as HTMLElement;
      const childrenText = Array.from(element.childNodes).map(walk).join('');

      switch (element.tagName.toLowerCase()) {
        case 'h1': return `\n# ${childrenText}\n`;
        case 'h2': return `\n## ${childrenText}\n`;
        case 'h3': return `\n### ${childrenText}\n`;
        case 'h4': return `\n#### ${childrenText}\n`;
        case 'h5': return `\n##### ${childrenText}\n`;
        case 'h6': return `\n###### ${childrenText}\n`;
        case 'p': return `\n${childrenText}\n`;
        case 'strong':
        case 'b': return `**${childrenText}**`;
        case 'em':
        case 'i': return `*${childrenText}*`;
        case 'code':
          if (element.parentElement?.tagName.toLowerCase() === 'pre') {
            const langMatch = element.className.match(/language-(\w+)/);
            const language = langMatch ? langMatch[1] : '';
            return `\n\`\`\`${language}\n${element.textContent}\n\`\`\`\n`;
          }
          return `\`${childrenText}\``;
        case 'pre':
          return `\n\`\`\`\n${element.textContent}\n\`\`\`\n`;
        case 'blockquote':
          return `\n> ${childrenText}\n`;
        case 'a':
          return `[${childrenText}](${element.getAttribute('href') ?? ''})`;
        case 'img':
          return `![${element.getAttribute('alt') ?? ''}](${element.getAttribute('src') ?? ''})`;
        case 'ul':
        case 'ol': {
          const isOrdered = element.tagName.toLowerCase() === 'ol';
          return (
            '\n' +
            Array.from(element.children)
              .map((li, index) => {
                const bullet = isOrdered ? `${index + 1}. ` : '* ';
                return bullet + walk(li);
              })
              .join('\n') +
            '\n'
          );
        }
        case 'li':
          return childrenText;
        case 'hr': return '\n---\n';
        case 'br': return '  \n';
        default: return childrenText;
      }
    };

    return walk(container).trim() + '\n';
  }

  /* ------------------- Markdown → HTML (decoder) --------------------- */

  private convertMarkdownToHtml(): void {
    const editorElement = this.editor!.getElement();
    // Get content from the assumed <pre><code> structure
    const markdownContent = editorElement.textContent || '';
    const htmlContent = this.decodeMarkdownToHtml(markdownContent);

    editorElement.innerHTML = htmlContent;
    editorElement.classList.remove('feather-markdown-mode');

    this.format = 'html';
    this.updateToolbarButton();

    // Trigger input event
    editorElement.dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }

  private decodeMarkdownToHtml(markdown: string): string {
    if (this.cfg.markdownOptions.sanitize) markdown = sanitizeHtml(markdown);

    /* Tokenise lines to blocks first – easier than monster RegExp chain */
    const lines = markdown.split('\n');
    const htmlLines: string[] = [];
    let i = 0;

    const listStack: { type: 'ul' | 'ol'; indent: number }[] = [];

    const closeLists = (untilIndent = 0) => {
      while (listStack.length && listStack[listStack.length - 1].indent >= untilIndent) {
        htmlLines.push(`</${listStack.pop()!.type}>`);
      }
    };

    while (i < lines.length) {
      const line = lines[i];

      /* headings */
      const headingMatch = line.match(/^(#{1,6})\s+(.*)$/);
      if (headingMatch) {
        closeLists();
        const level = headingMatch[1].length;
        htmlLines.push(`<h${level}>${headingMatch[2]}</h${level}>`);
        i++; continue;
      }

      /* hr */
      if (/^---\s*$/.test(line)) {
        closeLists();
        htmlLines.push('<hr>');
        i++; continue;
      }

      /* code block ``` */
      const codeBlockStart = line.match(/^```(\w*)\s*$/);
      if (codeBlockStart) {
        closeLists();
        const language = codeBlockStart[1];
        const block: string[] = [];
        i++; // skip ```
        while (i < lines.length && !/^```\s*$/.test(lines[i])) {
          block.push(lines[i]);
          i++;
        }
        htmlLines.push(
          `<pre><code${language ? ` class="language-${language}"` : ''}>${escapeHtml(
            block.join('\n')
          )}</code></pre>`
        );
        i++; // skip closing ```
        continue;
      }

      /* blockquote */
      const blockQuoteMatch = line.match(/^>\s?(.*)$/);
      if (blockQuoteMatch) {
        closeLists();
        htmlLines.push(`<blockquote>${blockQuoteMatch[1]}</blockquote>`);
        i++; continue;
      }

      /* list item */
      const listItemMatch = line.match(/^(\s*)([*+-]|\d+\.)\s+(.*)$/);
      if (listItemMatch) {
        const indent = listItemMatch[1].length;
        const marker = listItemMatch[2];
        const text = listItemMatch[3];

        const listType: 'ul' | 'ol' = marker.match(/^\d/) ? 'ol' : 'ul';

        if (!listStack.length || indent > listStack[listStack.length - 1].indent) {
          listStack.push({ type: listType, indent });
          htmlLines.push(`<${listType}>`);
        } else if (indent < listStack[listStack.length - 1].indent) {
          closeLists(indent);
          if (!listStack.length || listStack[listStack.length - 1].type !== listType) {
            listStack.push({ type: listType, indent });
            htmlLines.push(`<${listType}>`);
          }
        }

        htmlLines.push(`<li>${text}</li>`);
        i++; continue;
      }

      /* empty line breaks lists / paragraphs */
      if (line.trim() === '') {
        closeLists();
        i++; continue;
      }

      /* paragraph */
      closeLists();
      htmlLines.push(`<p>${line}</p>`);
      i++;
    }
    closeLists();

    /* inline transforms */
    let html = htmlLines.join('\n');
    html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');   // bold
    html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');              // italic
    html = html.replace(/`([^`]+)`/g, (_m, code) => `<code>${escapeHtml(code)}</code>`);
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img alt="$1" src="$2">');
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    return html;
  }

  /* --------------------------- UI feedback ---------------------------- */

  private emitFeedback(): void {
    const toast = document.createElement('div');
    toast.className = 'feather-format-feedback';
    toast.textContent = `Switched to ${this.format.toUpperCase()} mode`;
    toast.setAttribute('role', 'status');
    toast.setAttribute('aria-live', 'polite');
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 2000);
  }

  private updateToolbarButton(): void {
    const button = this.toolbarButtons[0];
    if (!button) return;
    const nextFormat = this.format === 'html' ? 'Markdown' : 'HTML';
    button.setAttribute('aria-label', `Switch to ${nextFormat} mode`);
    button.setAttribute('title', `Currently in ${this.format.toUpperCase()} mode`);
    button.classList.toggle('active', this.format === 'markdown');
  }

  /* ------------------------------ destroy ----------------------------- */

  public override destroy(): void {
    this.abort.abort();                 // remove all listeners
    /* always end in HTML mode */
    if (this.format === 'markdown' && this.editor) {
      this.convertMarkdownToHtml();
    }
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                               Factory export                               */
/* -------------------------------------------------------------------------- */

const plugin: Plugin = createPlugin<MarkdownTogglePlugin>(
  META.id,
  (editor: Editor) => {
    const instance = new MarkdownTogglePlugin();
    instance.init(editor);
    return instance;
  },
  (instance: MarkdownTogglePlugin) => instance.destroy()
);

export const pluginId = META.id;
export default plugin;