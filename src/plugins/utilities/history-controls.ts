import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * History Controls plugin configuration
 */
const config: PluginConfig = {
  id: 'history-controls',
  name: 'History Controls',
  description: 'Adds undo and redo controls to the toolbar',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'undo',
      command: 'undo',
      icon: '↺',
      label: 'Undo',
      tooltip: 'Undo (Ctrl/⌘+Z)',
      group: 'history',
      ariaLabel: 'Undo last action',
    },
    {
      id: 'redo',
      command: 'redo',
      icon: '↻',
      label: 'Redo',
      tooltip: 'Redo (Ctrl/⌘+Y or Ctrl/⌘+Shift+Z)',
      group: 'history',
      ariaLabel: 'Redo action',
    }
  ],
  shortcuts: [
    {
      command: 'undo',
      key: 'z',
      ctrlKey: true,
      description: 'Undo last action'
    },
    {
      command: 'redo',
      key: 'y',
      ctrlKey: true,
      description: 'Redo action'
    },
    {
      command: 'redo',
      key: 'z',
      ctrlKey: true,
      shiftKey: true,
      description: 'Redo action (alternative)'
    }
  ]
};

/**
 * History Controls plugin implementation
 * Adds undo and redo buttons to the toolbar and handles keyboard shortcuts
 */
export class HistoryControlsPlugin extends BasePlugin {
  private undoButtonElement: HTMLElement | null = null;
  private redoButtonElement: HTMLElement | null = null;
  private historyStateObserver: MutationObserver | null = null;
  private boundUpdateButtonStates: () => void;

  constructor() {
    super(config);
    this.boundUpdateButtonStates = this.updateButtonStates.bind(this);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
    
    // Find the toolbar buttons
    this.findToolbarButtons();
    
    // Initial button state update
    this.updateButtonStates();
    
    // Set up listeners for editor changes to update button states
    this.setupHistoryStateObserver();
  }
  
  /**
   * Find the toolbar buttons after they're registered
   */
  private findToolbarButtons(): void {
    const toolbar = document.getElementById('toolbar');
    if (!toolbar) return;
    
    // Find the undo and redo buttons
    this.undoButtonElement = toolbar.querySelector('[data-command="undo"]');
    this.redoButtonElement = toolbar.querySelector('[data-command="redo"]');
  }
  
  /**
   * Set up a mutation observer to watch for changes in the editor's data attributes
   * that indicate history state changes
   */
  private setupHistoryStateObserver(): void {
    const editorElement = this.editor?.getElement();
    if (!this.editor || !editorElement) return;
    
    // Watch for changes to the editor's data attributes
    this.historyStateObserver = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'attributes' && 
            (mutation.attributeName === 'data-history-position' || 
             mutation.attributeName === 'data-history-length')) {
          this.updateButtonStates();
          break;
        }
      }
    });
    
    this.historyStateObserver.observe(editorElement, {
      attributes: true,
      attributeFilter: ['data-history-position', 'data-history-length']
    });
    
    // Also listen for input events to update button states
    editorElement.addEventListener('input', this.boundUpdateButtonStates);
  }
  
  /**
   * Update the disabled state of the undo and redo buttons
   * based on the current history state
   */
  private updateButtonStates(): void {
    const editorElement = this.editor?.getElement();
    if (!this.editor || !editorElement) return;
    
    // Get history state from data attributes
    const historyPosition = parseInt(editorElement.getAttribute('data-history-position') || '0', 10);
    const historyLength = parseInt(editorElement.getAttribute('data-history-length') || '0', 10);
    
    // Update undo button state
    if (this.undoButtonElement) {
      if (historyPosition <= 0) {
        this.undoButtonElement.setAttribute('disabled', 'true');
        this.undoButtonElement.setAttribute('aria-disabled', 'true');
      } else {
        this.undoButtonElement.removeAttribute('disabled');
        this.undoButtonElement.setAttribute('aria-disabled', 'false');
      }
    }
    
    // Update redo button state
    if (this.redoButtonElement) {
      if (historyPosition >= historyLength - 1) {
        this.redoButtonElement.setAttribute('disabled', 'true');
        this.redoButtonElement.setAttribute('aria-disabled', 'true');
      } else {
        this.redoButtonElement.removeAttribute('disabled');
        this.redoButtonElement.setAttribute('aria-disabled', 'false');
      }
    }
  }
  
  /**
   * Handle toolbar commands
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (!this.editor) return;
    
    if (_command === 'undo') {
      this.editor.undo();
    } else if (_command === 'redo') {
      this.editor.redo();
    }
    
    // Focus the editor after undo/redo
    this.editor.focus();
    
    // Update button states
    this.updateButtonStates();
  }
  
  /**
   * Clean up when the plugin is destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Disconnect the mutation observer
    if (this.historyStateObserver) {
      this.historyStateObserver.disconnect();
      this.historyStateObserver = null;
    }
    
    // Remove event listeners
    const editorElement = this.editor?.getElement();
    if (editorElement) {
      editorElement.removeEventListener('input', this.boundUpdateButtonStates);
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<HistoryControlsPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new HistoryControlsPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: HistoryControlsPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
