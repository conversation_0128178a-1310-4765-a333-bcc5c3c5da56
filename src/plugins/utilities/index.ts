import type { Editor, Plugin } from '../../types';
import { createPlugin } from '../plugin-factory';

// Import all utility plugins
import historyControlsPlugin from './history-controls';
import findReplacePlugin from './find-replace';
import wordCountPlugin from './word-count';
import sourceViewPlugin from './source-view';
import fullscreenPlugin from './fullscreen';
import printExportPlugin from './print-export';
import importExportPlugin from './import-export';
import spellGrammarPlugin from './spell-grammar';
import markdownTogglePlugin from './markdown-toggle';

// Export individual plugins for direct imports
export {
  historyControlsPlugin,
  findReplacePlugin,
  wordCountPlugin,
  sourceViewPlugin,
  fullscreenPlugin,
  printExportPlugin,
  importExportPlugin,
  spellGrammarPlugin,
  markdownTogglePlugin
}

// Export a combined plugin group for convenience
export const UtilityPlugins: Plugin[] = [
  historyControlsPlugin,
  findReplacePlugin,
  wordCountPlugin,
  sourceViewPlugin,
  fullscreenPlugin,
  printExportPlugin,
  importExportPlugin,
  spellGrammarPlugin,
  markdownTogglePlugin
];

// Create a group plugin that registers all utility plugins
const groupPlugin: Plugin = createPlugin<Plugin[]>(
  'utilities-group',
  (editor: Editor) => {
    // Initialize all plugins
    const plugins = UtilityPlugins.map(plugin => {
      plugin.init(editor);
      return plugin;
    });
    return plugins;
  },
  (plugins: Plugin[]) => {
    // Clean up all plugins
    plugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
);

// Default export for simplified registration
export default groupPlugin;