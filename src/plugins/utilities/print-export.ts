import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Print and Export plugin configuration
 */
const config: PluginConfig = {
  id: 'print-export',
  name: 'Print & Export',
  description: 'Print the editor content or export as PDF',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'print-export',
      command: 'print-export',
      icon: '🖨',
      label: 'Print/PDF',
      tooltip: 'Print or export as PDF',
      group: 'utilities',
      ariaLabel: 'Print or export as PDF',
    }
  ],
  shortcuts: [] // No default shortcuts for print
};

/**
 * Print and Export plugin implementation
 * Allows printing the editor content or exporting as PDF
 */
export class PrintExportPlugin extends BasePlugin {
  private dialog: HTMLElement | null = null;
  private isDialogOpen = false;
  
  constructor() {
    super(config);
  }
  
  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);
  }
  
  /**
   * Handle the print/export command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    const editorElement = this.editor?.getElement();
    if (this.isDialogOpen || !this.editor || !editorElement || document.activeElement !== editorElement) {
      return; // Prevent opening if already open, no editor, or editor not focused
    }
    if (_command === 'print-export') {
      if (this.isDialogOpen) {
        this.closePrintDialog();
      } else {
        this.openPrintDialog();
      }
    }
  }
  
  /**
   * Open the print dialog
   */
  private openPrintDialog(): void {
    if (this.isDialogOpen) return;
    
    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 w-full h-full bg-black/40 z-[9998]'; // Tailwind for backdrop
    document.body.appendChild(backdrop);
    
    // Create dialog
    this.dialog = document.createElement('div');
    this.dialog.className = 'fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[420px] bg-white dark:bg-slate-800 rounded-lg shadow-xl p-5 z-[9999] font-sans text-gray-900 dark:text-slate-200 flex flex-col'; // Tailwind for dialog
    this.dialog.setAttribute('role', 'dialog');
    this.dialog.setAttribute('aria-labelledby', 'print-dialog-title');
    
    // Create dialog content programmatically
    // Header
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-4';
    const title = document.createElement('h3');
    title.id = 'print-dialog-title';
    title.className = 'font-semibold text-base m-0';
    title.textContent = 'Print & PDF Options';
    const closeButtonDialog = document.createElement('button');
    closeButtonDialog.type = 'button';
    closeButtonDialog.className = 'bg-transparent border-none text-lg cursor-pointer p-1 text-gray-500 dark:text-slate-400 hover:text-gray-700 dark:hover:text-slate-200 leading-none';
    closeButtonDialog.setAttribute('aria-label', 'Close');
    closeButtonDialog.textContent = '×';
    header.appendChild(title);
    header.appendChild(closeButtonDialog);
    this.dialog.appendChild(header);

    // Options Container
    const optionsContainer = document.createElement('div');
    optionsContainer.className = 'mb-5';

    const createSelectOption = (labelText: string, selectId: string, options: {value: string, text: string}[]) => {
      const optionDiv = document.createElement('div');
      optionDiv.className = 'mb-4';
      const label = document.createElement('label');
      label.htmlFor = selectId;
      label.className = 'block mb-1.5 font-medium text-sm text-gray-700 dark:text-slate-300';
      label.textContent = labelText;
      const select = document.createElement('select');
      select.id = selectId;
      select.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
      options.forEach(opt => {
        const optionEl = document.createElement('option');
        optionEl.value = opt.value;
        optionEl.textContent = opt.text;
        select.appendChild(optionEl);
      });
      optionDiv.appendChild(label);
      optionDiv.appendChild(select);
      return optionDiv;
    };
    
    optionsContainer.appendChild(createSelectOption('Paper Size', 'print-paper-size', [
      {value: 'letter', text: 'Letter (8.5" × 11")'}, {value: 'a4', text: 'A4 (210mm × 297mm)'},
      {value: 'legal', text: 'Legal (8.5" × 14")'}, {value: 'tabloid', text: 'Tabloid (11" × 17")'}
    ]));
    optionsContainer.appendChild(createSelectOption('Orientation', 'print-orientation', [
      {value: 'portrait', text: 'Portrait'}, {value: 'landscape', text: 'Landscape'}
    ]));
    optionsContainer.appendChild(createSelectOption('Scale', 'print-scale', [
      {value: '1', text: 'Default (100%)'}, {value: '0.75', text: '75%'},
      {value: '0.5', text: '50%'}, {value: 'fit', text: 'Fit to page width'}
    ]));

    // Checkbox options
    const checkboxOptionDiv = document.createElement('div');
    checkboxOptionDiv.className = 'mb-4';
    const cbLabel = document.createElement('label');
    cbLabel.className = 'block mb-1.5 font-medium text-sm text-gray-700 dark:text-slate-300';
    cbLabel.textContent = 'Options';
    checkboxOptionDiv.appendChild(cbLabel);
    const checkboxGroup = document.createElement('div');
    checkboxGroup.className = 'flex flex-wrap gap-3 mt-2';
    
    const createCheckbox = (id: string, text: string, checked: boolean = false) => {
      const label = document.createElement('label');
      label.className = 'flex items-center gap-1.5 text-sm cursor-pointer text-gray-700 dark:text-slate-300';
      const input = document.createElement('input');
      input.type = 'checkbox'; input.id = id; input.checked = checked;
      input.className = 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 cursor-pointer';
      label.appendChild(input);
      label.appendChild(document.createTextNode(text));
      return label;
    };
    checkboxGroup.appendChild(createCheckbox('print-background', 'Print background colors and images', true));
    checkboxGroup.appendChild(createCheckbox('print-header-footer', 'Include header and footer'));
    checkboxOptionDiv.appendChild(checkboxGroup);
    optionsContainer.appendChild(checkboxOptionDiv);

    // Header/Footer input container
    const headerFooterContainer = document.createElement('div');
    headerFooterContainer.id = 'header-footer-container';
    headerFooterContainer.className = 'mb-4 hidden'; // Initially hidden
    const createInput = (labelText: string, inputId: string, placeholder: string, marginTop: boolean = false) => {
      const div = document.createElement('div');
      if (marginTop) div.className = 'mt-3';
      const label = document.createElement('label');
      label.htmlFor = inputId;
      label.className = 'block mb-1.5 font-medium text-sm text-gray-700 dark:text-slate-300';
      label.textContent = labelText;
      const input = document.createElement('input');
      input.type = 'text'; input.id = inputId;
      input.className = 'w-full p-2 border border-gray-300 dark:border-slate-600 rounded text-sm bg-white dark:bg-slate-700 text-gray-900 dark:text-slate-200 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400';
      input.placeholder = placeholder;
      div.appendChild(label); div.appendChild(input);
      return div;
    };
    headerFooterContainer.appendChild(createInput('Custom Header (optional)', 'print-header', 'Title, date, or other information'));
    headerFooterContainer.appendChild(createInput('Custom Footer (optional)', 'print-footer', 'Page number, copyright, etc.', true));
    optionsContainer.appendChild(headerFooterContainer);
    
    this.dialog.appendChild(optionsContainer);

    // Actions
    const actionsContainer = document.createElement('div');
    actionsContainer.className = 'flex justify-end gap-2.5 mt-5';
    const pdfButtonElem = document.createElement('button');
    pdfButtonElem.type = 'button'; pdfButtonElem.id = 'print-pdf-button';
    pdfButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200';
    pdfButtonElem.textContent = 'Save as PDF';
    actionsContainer.appendChild(pdfButtonElem);
    const printButtonElem = document.createElement('button');
    printButtonElem.type = 'button'; printButtonElem.id = 'print-button';
    printButtonElem.className = 'py-2 px-4 rounded border-none text-sm cursor-pointer font-medium bg-blue-600 hover:bg-blue-700 text-white';
    printButtonElem.textContent = 'Print';
    actionsContainer.appendChild(printButtonElem);
    this.dialog.appendChild(actionsContainer);
    
    // Add dialog to document
    document.body.appendChild(this.dialog);
    this.isDialogOpen = true;
    
    // Setup event listeners
    this.setupDialogEventListeners();
    
    // Handle escape key
    document.addEventListener('keydown', this.handleDialogKeydown);
  }
  
  /**
   * Setup event listeners for the print dialog
   */
  private setupDialogEventListeners(): void {
    if (!this.dialog) return;
    
    // Close button
    const closeButton = this.dialog.querySelector('.feather-print-close');
    closeButton?.addEventListener('click', () => {
      this.closePrintDialog();
    });
    
    // Backdrop click
    const backdrop = document.querySelector('.feather-print-backdrop');
    backdrop?.addEventListener('click', () => {
      this.closePrintDialog();
    });
    
    // Header/footer checkbox
    const headerFooterCheckbox = this.dialog.querySelector('#print-header-footer') as HTMLInputElement;
    const headerFooterContainer = this.dialog.querySelector('#header-footer-container') as HTMLElement;
    
    headerFooterCheckbox?.addEventListener('change', () => {
      if (headerFooterCheckbox.checked) {
        headerFooterContainer.style.display = 'block';
      } else {
        headerFooterContainer.style.display = 'none';
      }
    });
    
    // Print button
    const printButton = this.dialog.querySelector('#print-button');
    printButton?.addEventListener('click', () => {
      this.printContent();
    });
    
    // PDF button
    const pdfButton = this.dialog.querySelector('#print-pdf-button');
    pdfButton?.addEventListener('click', () => {
      this.printContent(true);
    });
  }
  
  /**
   * Print or export the content as PDF
   * @param asPdf Whether to save as PDF instead of printing
   */
  private printContent(asPdf: boolean = false): void {
    if (!this.editor || !this.dialog) return;
    
    // Get options from dialog
    const paperSize = (this.dialog.querySelector('#print-paper-size') as HTMLSelectElement).value;
    const orientation = (this.dialog.querySelector('#print-orientation') as HTMLSelectElement).value;
    const scale = (this.dialog.querySelector('#print-scale') as HTMLSelectElement).value;
    const printBackground = (this.dialog.querySelector('#print-background') as HTMLInputElement).checked;
    const includeHeaderFooter = (this.dialog.querySelector('#print-header-footer') as HTMLInputElement).checked;
    const headerText = includeHeaderFooter ? (this.dialog.querySelector('#print-header') as HTMLInputElement).value : '';
    const footerText = includeHeaderFooter ? (this.dialog.querySelector('#print-footer') as HTMLInputElement).value : '';
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups to print or export as PDF.');
      return;
    }
    
    // Get editor content
    const content = this.editor.getElement()?.innerHTML;
    
    // Write the document
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Print Preview</title>
        <style>
          @page {
            size: ${paperSize} ${orientation};
            margin: 1cm;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 20px;
            ${!printBackground ? 'background-color: white !important;' : ''}
          }
          
          img, svg {
            max-width: 100%;
            height: auto;
          }
          
          table {
            border-collapse: collapse;
            width: 100%;
          }
          
          td, th {
            border: 1px solid #ddd;
            padding: 8px;
          }
          
          .header {
            position: fixed;
            top: 0;
            width: 100%;
            border-bottom: 1px solid #ddd;
            padding: 10px 0;
            text-align: center;
          }
          
          .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            border-top: 1px solid #ddd;
            padding: 10px 0;
            text-align: center;
          }
          
          .content {
            ${includeHeaderFooter ? 'margin-top: 50px; margin-bottom: 50px;' : ''}
          }
          
          /* Apply scale */
          ${scale !== '1' && scale !== 'fit' ? `
            .content {
              transform: scale(${scale});
              transform-origin: top left;
            }
          ` : ''}
          
          ${scale === 'fit' ? `
            .content {
              width: 100%;
            }
          ` : ''}
          
          /* Custom styles from editor */
          ${this.getEditorStyles()}
        </style>
      </head>
      <body>
        ${includeHeaderFooter && headerText ? `<div class="header">${headerText}</div>` : ''}
        
        <div id="feather-print-container" class="content">
          ${content}
        </div>
        
        ${includeHeaderFooter && footerText ? `<div class="footer">${footerText}</div>` : ''}
        
        <script>
          // Auto-print when loaded
          window.onload = function() {
            setTimeout(function() {
              window.print();
              ${!asPdf ? 'window.close();' : ''}
            }, 500);
          };
        </script>
      </body>
      </html>
    `);
    
    printWindow.document.close();
    
    // Close the dialog
    this.closePrintDialog();
  }
  
  /**
   * Extract relevant styles from the editor to apply to the print output
   */
  private getEditorStyles(): string {
    if (!this.editor) return '';
    
    // Get computed styles for the editor
    const editorElement = this.editor.getElement();
    if (!editorElement) return ''; // Element might not be ready
    const computedStyle = window.getComputedStyle(editorElement);
    
    // Build a style string with the most relevant properties
    return `
      .content {
        font-family: ${computedStyle.fontFamily};
        font-size: ${computedStyle.fontSize};
        color: ${computedStyle.color};
      }
      
      h1, h2, h3, h4, h5, h6 {
        font-family: ${computedStyle.fontFamily};
        font-weight: bold;
        margin-top: 1em;
        margin-bottom: 0.5em;
      }
      
      h1 { font-size: 2em; }
      h2 { font-size: 1.5em; }
      h3 { font-size: 1.3em; }
      h4 { font-size: 1.1em; }
      h5 { font-size: 1em; }
      h6 { font-size: 0.9em; }
      
      p {
        margin-top: 0;
        margin-bottom: 1em;
      }
      
      blockquote {
        border-left: 4px solid #ddd;
        padding-left: 1em;
        margin-left: 0;
        color: #666;
      }
      
      code {
        font-family: monospace;
        padding: 0.2em 0.4em;
        background-color: #f5f5f5;
        border-radius: 3px;
      }
      
      pre {
        background-color: #f5f5f5;
        padding: 1em;
        overflow: auto;
        border-radius: 4px;
      }
      
      a {
        color: #1a73e8;
        text-decoration: underline;
      }
    `;
  }
  
  /**
   * Handle keydown events for the dialog
   */
  private handleDialogKeydown = (event: KeyboardEvent): void => {
    if (event.key === 'Escape') {
      this.closePrintDialog();
    }
  };
  
  /**
   * Close the print dialog
   */
  private closePrintDialog(): void {
    if (!this.isDialogOpen) return;
    
    // Remove backdrop
    const backdrop = document.querySelector('.feather-print-backdrop');
    if (backdrop && backdrop.parentNode) {
      backdrop.parentNode.removeChild(backdrop);
    }
    
    // Remove dialog
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    this.dialog = null;
    this.isDialogOpen = false;
    
    // Remove event listener
    document.removeEventListener('keydown', this.handleDialogKeydown);
  }
  
  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();
    
    // Close dialog if open
    if (this.isDialogOpen) {
      this.closePrintDialog();
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<PrintExportPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new PrintExportPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: PrintExportPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
