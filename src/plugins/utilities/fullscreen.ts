import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Fullscreen plugin configuration
 */
const config: PluginConfig = {
  id: 'fullscreen',
  name: 'Fullscreen',
  description: 'Toggle fullscreen (zen) mode for distraction-free editing',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'fullscreen',
      command: 'fullscreen',
      icon: '⛶',
      label: 'Fullscreen',
      tooltip: 'Toggle fullscreen mode',
      group: 'utilities',
      ariaLabel: 'Toggle fullscreen mode',
    }
  ],
  shortcuts: [
    {
      key: 'mod+shift+f',
      command: 'fullscreen',
      description: 'Toggle fullscreen mode',
    }
  ]
};

/**
 * Fullscreen plugin implementation
 * Provides a distraction-free editing experience by toggling fullscreen mode
 */
export class FullscreenPlugin extends BasePlugin {
  private isFullscreen = false;
  private originalStyles: Record<string, string> = {};
  private editorContainer: HTMLElement | null = null;
  private toolbarButton: HTMLElement | null = null;
  private originalParent: Node | null = null;
  private originalWidth: string = '';
  private originalHeight: string = '';
  private fullscreenContainer: HTMLElement | null = null;
  private abort = new AbortController();

  constructor() {
    super(config);
  }

  /**
   * Initialize the plugin
   * @param editor The editor instance
   */
  public init(editor: Editor): void {
    super.init(editor);

    // Add CSS for the fullscreen UI - Removed, handled externally
    // this.addFullscreenStyles();

    // Store the editor container
    this.editorContainer = this.editor?.getElement().parentElement || null;

    // Find the toolbar button after it's registered
    this.findToolbarButton();

    // Listen for escape key to exit fullscreen
    document.addEventListener('keydown', this.handleEscapeKey);
  }

  /**
   * Find the toolbar button after it's registered
   */
  private findToolbarButton(): void {
    setTimeout(() => {
      const toolbar = document.getElementById('toolbar');
      if (!toolbar) return;

      // Find the fullscreen button
      this.toolbarButton = toolbar.querySelector('[data-command="fullscreen"]');
    }, 100);
  }

  /**
   * Handle the fullscreen command
   * @param _command The command to handle
   */
  protected handleCommand(_command: string): void {
    if (_command === 'fullscreen' && this.editor) {
      this.toggleFullscreen();
    }
  }

  /**
   * Toggle fullscreen mode
   */
  private toggleFullscreen(): void {
    if (this.isFullscreen) {
      this.exitFullscreen();
    } else {
      this.enterFullscreen();
    }
  }

  /**
   * Enter fullscreen mode
   */
  private enterFullscreen(): void {
    if (!this.editor || !this.editorContainer) return;

    // Save original styles and parent
    this.originalParent = this.editorContainer.parentNode;
    this.originalWidth = this.editorContainer.style.width;
    this.originalHeight = this.editorContainer.style.height;

    // Save more styles if needed
    const editorElement = this.editor.getElement();
    this.originalStyles = {
      width: editorElement.style.width,
      height: editorElement.style.height,
      minHeight: editorElement.style.minHeight,
      maxHeight: editorElement.style.maxHeight,
      overflow: editorElement.style.overflow
    };

    // Create fullscreen container
    this.fullscreenContainer = document.createElement('div');
    this.fullscreenContainer.className = 'feather-fullscreen-container fixed inset-0 w-full h-full bg-white dark:bg-slate-900 z-[9999] flex flex-col overflow-hidden';

    // Check if we should use dark theme based on user preference, not system preference
    const storedTheme = localStorage.getItem('editor-theme');
    const htmlElement = document.documentElement;

    // If the html element has the dark class or the stored theme is dark, add dark class to fullscreen container
    if (htmlElement.classList.contains('dark') || storedTheme === 'dark') {
      this.fullscreenContainer.classList.add('dark');
    }

    // Also add the appropriate theme class for CSS variables
    if (storedTheme) {
      this.fullscreenContainer.classList.add(`theme-${storedTheme}`);
    }

    // Listen for theme changes
    const themeChangeListener = (e: Event) => {
      if (!this.fullscreenContainer) return;

      const theme = (e as CustomEvent).detail;
      // Remove existing theme classes
      this.fullscreenContainer.classList.remove('theme-light', 'theme-dark', 'dark');
      // Add new theme class
      this.fullscreenContainer.classList.add(`theme-${theme}`);
      if (theme === 'dark') {
        this.fullscreenContainer.classList.add('dark');
      }
    };

    document.addEventListener('themechange', themeChangeListener, { signal: this.abort.signal });

    // Create header
    const header = document.createElement('div');
    header.className = 'flex justify-between items-center py-2.5 px-5 bg-gray-100 dark:bg-slate-800 border-b border-gray-300 dark:border-slate-700';

    // Create title
    const title = document.createElement('h2');
    title.className = 'text-base font-semibold text-gray-800 dark:text-slate-200 m-0';
    title.textContent = 'Distraction-free Editing';

    // Create close button
    const closeButton = document.createElement('button');
    closeButton.className = 'bg-transparent border-none text-2xl cursor-pointer text-gray-500 dark:text-slate-400 py-1 px-2.5 rounded hover:bg-gray-200 dark:hover:bg-slate-700';
    closeButton.textContent = '×';
    closeButton.setAttribute('aria-label', 'Exit fullscreen mode');
    closeButton.addEventListener('click', () => this.exitFullscreen());

    // Add title and close button to header
    header.appendChild(title);
    header.appendChild(closeButton);

    // Create editor container
    const editorContainer = document.createElement('div');
    editorContainer.className = 'flex-1 p-5 max-w-3xl mx-auto w-full overflow-y-auto flex flex-col';

    // Create editor wrapper
    const editorWrapper = document.createElement('div');
    editorWrapper.className = 'flex-1 border-none shadow-none outline-none p-0 w-full h-full resize-none';

    // Move the editor to the fullscreen container
    editorWrapper.appendChild(this.editorContainer);
    editorContainer.appendChild(editorWrapper);

    // Add header and editor container to fullscreen container
    this.fullscreenContainer.appendChild(header);
    this.fullscreenContainer.appendChild(editorContainer);

    // Add fullscreen container to body
    document.body.appendChild(this.fullscreenContainer);

    // Update editor styles
    editorElement.style.width = '100%';
    editorElement.style.height = '100%';
    editorElement.style.minHeight = '100%';
    editorElement.style.maxHeight = 'none';
    editorElement.style.overflow = 'auto';

    // Update state
    this.isFullscreen = true;

    // Update the toolbar button state
    if (this.toolbarButton) {
      this.toolbarButton.classList.add('active');
    }

    // Focus editor
    this.editor.focus();

    // Dispatch custom event
    editorElement.dispatchEvent(
      new CustomEvent('feather:fullscreen', {
        bubbles: true,
        detail: { fullscreen: true }
      })
    );
  }

  /**
   * Exit fullscreen mode
   */
  private exitFullscreen(): void {
    if (!this.isFullscreen || !this.editor || !this.editorContainer || !this.fullscreenContainer) return;

    // Remove the editor from the fullscreen container
    const editorWrapper = this.fullscreenContainer.querySelector('.feather-fullscreen-editor');
    if (editorWrapper && this.editorContainer.parentNode === editorWrapper) {
      editorWrapper.removeChild(this.editorContainer);
    }

    // Restore the editor to its original parent
    if (this.originalParent) {
      this.originalParent.appendChild(this.editorContainer);
    }

    // Restore original styles
    this.editorContainer.style.width = this.originalWidth;
    this.editorContainer.style.height = this.originalHeight;

    // Restore editor styles
    const editorElement = this.editor.getElement();
    editorElement.style.width = this.originalStyles.width || '';
    editorElement.style.height = this.originalStyles.height || '';
    editorElement.style.minHeight = this.originalStyles.minHeight || '';
    editorElement.style.maxHeight = this.originalStyles.maxHeight || '';
    editorElement.style.overflow = this.originalStyles.overflow || '';

    // Remove the fullscreen container
    if (this.fullscreenContainer.parentNode) {
      this.fullscreenContainer.parentNode.removeChild(this.fullscreenContainer);
    }

    // Update state
    this.isFullscreen = false;
    this.fullscreenContainer = null;

    // Update the toolbar button state
    if (this.toolbarButton) {
      this.toolbarButton.classList.remove('active');
    }

    // Focus editor
    this.editor.focus();

    // Dispatch custom event
    editorElement.dispatchEvent(
      new CustomEvent('feather:fullscreen', {
        bubbles: true,
        detail: { fullscreen: false }
      })
    );
  }

  /**
   * Handle escape key to exit fullscreen
   */
  private handleEscapeKey = (event: KeyboardEvent): void => {
    if (event.key === 'Escape' && this.isFullscreen) {
      this.exitFullscreen();
      event.preventDefault();
    }
  };

  /**
   * Clean up when destroyed
   */
  public destroy(): void {
    super.destroy();

    // Abort any ongoing operations
    this.abort.abort();

    // Exit fullscreen if active
    if (this.isFullscreen) {
      this.exitFullscreen();
    }

    // Remove event listener
    document.removeEventListener('keydown', this.handleEscapeKey);

    // Remove potential leftover style tag
    const styleTag = document.getElementById('feather-fullscreen-styles');
    styleTag?.remove();
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<FullscreenPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new FullscreenPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: FullscreenPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
