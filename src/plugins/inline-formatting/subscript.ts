import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Subscript formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'subscript',
  name: 'Subscript',
  description: 'Format selected text as subscript',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'subscript',
      command: 'subscript',
      icon: 'x₂',
      label: 'Subscript',
      tooltip: 'Subscript (Ctrl/⌘+,)',
      group: 'formatting',
      ariaLabel: 'Subscript formatting',
    }
  ],
  shortcuts: [
    {
      command: 'subscript',
      key: ',',
      ctrlKey: true,
      description: 'Apply subscript formatting'
    }
  ]
};

/**
 * Subscript formatting plugin implementation
 * Uses <sub> element for semantic HTML
 */
export class SubscriptPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the subscript command
   * @param command The command to handle
   */
  protected handleCommand(command: string): void {
    if (command === 'subscript' && this.editor) {
      this.editor.format('subscript');
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<SubscriptPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new SubscriptPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: SubscriptPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
