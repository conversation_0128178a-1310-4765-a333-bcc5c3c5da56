import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Underline formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'underline',
  name: 'Underline',
  description: 'Apply underline formatting to selected text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'underline',
      command: 'underline',
      icon: 'U̲',
      label: 'Underline',
      tooltip: 'Underline (Ctrl/⌘+U)',
      group: 'formatting',
      ariaLabel: 'Underline formatting',
    }
  ],
  shortcuts: [
    {
      command: 'underline',
      key: 'u',
      ctrlKey: true,
      description: 'Apply underline formatting'
    }
  ]
};

/**
 * Underline formatting plugin implementation
 */
export class UnderlinePlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the underline command
   * @param command The command to handle
   */
  protected handleCommand(command: string): void {
    if (command === 'underline' && this.editor) {
      this.editor.format('underline');
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<UnderlinePlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new UnderlinePlugin();
    instance.init(editor);
    return instance;
  },
  (instance: UnderlinePlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
