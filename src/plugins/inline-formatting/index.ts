import type { Editor, Plugin } from '../../types';
import { createPlugin } from '../plugin-factory';

// Import all plugins
import boldPlugin from './bold';
import italicPlugin from './italic';
import underlinePlugin from './underline';
import strikethroughPlugin from './strikethrough';
import superscriptPlugin from './superscript';
import subscriptPlugin from './subscript';
import inlineCodePlugin from './inline-code';
import textColorPlugin from './text-color';
import clearFormatPlugin from './clear-format';

// This file serves as the entry point for all inline formatting plugins
// Each plugin is exported individually to ensure tree-shaking works properly

// Export individual plugins
export { 
  boldPlugin, 
  italicPlugin, 
  underlinePlugin,
  strikethroughPlugin,
  superscriptPlugin,
  subscriptPlugin,
  inlineCodePlugin,
  textColorPlugin,
  clearFormatPlugin
};

// Export a combined plugin group for convenience
export const InlineFormattingPlugins: Plugin[] = [
  boldPlugin,
  italicPlugin,
  underlinePlugin,
  strikethroughPlugin,
  superscriptPlugin,
  subscriptPlugin,
  inlineCodePlugin,
  textColorPlugin,
  clearFormatPlugin
];

// Create a group plugin that registers all inline formatting plugins
const groupPlugin: Plugin = createPlugin<Plugin[]>(
  'inline-formatting-group',
  (editor: Editor) => {
    // Initialize all plugins
    const plugins = InlineFormattingPlugins.map(plugin => {
      plugin.init(editor);
      return plugin;
    });
    return plugins;
  },
  (plugins: Plugin[]) => {
    // Clean up all plugins
    plugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
);

// Default export for simplified registration
export default groupPlugin;
