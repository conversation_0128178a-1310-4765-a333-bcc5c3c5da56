import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Bold formatting plugin
 * Handles the bold command in the editor
 */
const config: PluginConfig = {
  id: 'bold',
  name: 'Bold',
  description: 'Apply bold formatting to selected text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'bold',
      command: 'bold',
      icon: 'B',
      label: 'Bold',
      tooltip: 'Bold (Ctrl/⌘+B)',
      group: 'formatting',
      ariaLabel: 'Bold formatting',
    }
  ],
  shortcuts: [
    {
      command: 'bold',
      key: 'b',
      ctrlKey: true,
      description: 'Apply bold formatting'
    }
  ]
};

/**
 * Bold formatting plugin implementation
 */
export class BoldPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the bold command
   * @param command The command to handle
   */
  protected handleCommand(command: string): void {
    if (command === 'bold' && this.editor) {
      this.editor.format('bold');
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<BoldPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new BoldPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: BoldPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
