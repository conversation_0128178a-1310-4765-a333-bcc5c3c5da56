import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Italic formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'italic',
  name: 'Italic',
  description: 'Apply italic formatting to selected text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'italic',
      command: 'italic',
      icon: 'I',
      label: 'Italic',
      tooltip: 'Italic (Ctrl/⌘+I)',
      group: 'formatting',
      ariaLabel: 'Italic formatting',
    }
  ],
  shortcuts: [
    {
      command: 'italic',
      key: 'i',
      ctrlKey: true,
      description: 'Apply italic formatting'
    }
  ]
};

/**
 * Italic formatting plugin implementation
 */
export class ItalicPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the italic command
   * @param command The command to handle
   */
  protected handleCommand(command: string): void {
    if (command === 'italic' && this.editor) {
      this.editor.format('italic');
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<ItalicPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new ItalicPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: ItalicPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;

