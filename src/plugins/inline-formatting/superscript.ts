import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Superscript formatting plugin configuration
 */
const config: PluginConfig = {
  id: 'superscript',
  name: 'Superscript',
  description: 'Format selected text as superscript',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'superscript',
      command: 'superscript',
      icon: 'x²',
      label: 'Superscript',
      tooltip: 'Superscript (Ctrl/⌘+.)',
      group: 'formatting',
      ariaLabel: 'Superscript formatting',
    }
  ],
  shortcuts: [
    {
      command: 'superscript',
      key: '.',
      ctrlKey: true,
      description: 'Apply superscript formatting'
    }
  ]
};

/**
 * Superscript formatting plugin implementation
 * Uses <sup> element for semantic HTML
 */
export class SuperscriptPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the superscript command
   * @param command The command to handle
   */
  protected handleCommand(command: string): void {
    if (command === 'superscript' && this.editor) {
      this.editor.format('superscript');
    }
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<SuperscriptPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new SuperscriptPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: SuperscriptPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;
