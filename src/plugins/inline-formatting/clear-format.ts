import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';
import type { Editor, Plugin } from '../../types';

/**
 * Clear format plugin configuration
 */
const config: PluginConfig = {
  id: 'clear-format',
  name: 'Clear Formatting',
  description: 'Remove all formatting from selected text',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'clear-format',
      command: 'clearFormat',
      icon: '𝘅',
      label: 'Clear Format',
      tooltip: 'Clear Formatting (Ctrl/⌘+\\)',
      group: 'formatting',
      ariaLabel: 'Remove all formatting',
    }
  ],
  shortcuts: [
    {
      command: 'clearFormat',
      key: '\\',
      ctrlKey: true,
      description: 'Remove all formatting from selected text'
    }
  ]
};

/**
 * Clear formatting plugin implementation
 * Removes all inline formatting tags from selected text
 */
export class ClearFormatPlugin extends BasePlugin {
  constructor() {
    super(config);
  }
  
  /**
   * Handle the clear format command
   * @param command The command to handle
   */
  protected handleCommand(command: string): void {
    if (command === 'clearFormat' && this.editor) {
      this.clearFormatting();
    }
  }
  
  /**
   * Remove all formatting from the selected text
   * This function uses the editor's format API for better integration and history management.
   */
  private clearFormatting(): void {
    if (!this.editor) return;
    this.editor.format('removeFormat');
  }
}

// Create a plugin that conforms to the Plugin interface
const plugin: Plugin = createPlugin<ClearFormatPlugin>(
  config.id,
  (editor: Editor) => {
    const instance = new ClearFormatPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: ClearFormatPlugin) => {
    instance.destroy();
  }
);

// Export the plugin ID for registration in the plugin manager
export const pluginId = config.id;

// Default export for tree-shaking
export default plugin;