import type { Editor, Plugin } from '../../types';
import { createPlugin } from '../plugin-factory';

// Import all collaboration plugins
import liveCursorsPlugin from './live-cursors';
import commentsPlugin from './comments';
import trackChangesPlugin from './track-changes';
import presencePlugin from './presence';

// Export individual plugins for direct imports
export {
  liveCursorsPlugin,
  commentsPlugin,
  trackChangesPlugin,
  presencePlugin
}

// Export a combined plugin group for convenience
export const CollaborationPlugins: Plugin[] = [
  liveCursorsPlugin,
  commentsPlugin,
  trackChangesPlugin,
  presencePlugin
];

// Create a group plugin that registers all collaboration plugins
// This allows for easily registering all collaboration plugins at once
const groupPlugin: Plugin = createPlugin<Plugin[]>(
  'collaboration-group',
  (editor: Editor) => {
    // Initialize all collaboration plugins
    const plugins = CollaborationPlugins.map(plugin => {
      plugin.init(editor);
      return plugin;
    });
    return plugins;
  },
  (plugins: Plugin[]) => {
    plugins.forEach(plugin => {
      if (plugin.destroy) {
        plugin.destroy();
      }
    });
  }
);

// Default export for convenience
export default groupPlugin;