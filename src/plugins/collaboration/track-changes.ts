/**
 * Track-Changes Plugin for FeatherJS
 * ----------------------------------
 * Adds Word-style revision marks (insertions / deletions) plus Accept / Reject.
 * This implementation is local-only; hook `changeHistory` into your
 * real-time layer to broadcast ops.
 */

import type { Editor, Plugin } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
import { createPlugin } from '../plugin-factory';

/* -------------------------------------------------------------------------- */
/*                               Plugin metadata                              */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id: 'track-changes',
  name: 'Track Changes',
  description: 'Inline diff marks with accept / reject',
  version: '1.0.0',
  toolbarItems: [
    {
      id: 'track-changes',
      command: 'track-changes',
      icon: '✍',
      label: 'Track Changes',
      tooltip: 'Toggle change tracking',
      group: 'collaboration',
      ariaLabel: 'Toggle change tracking'
    }
  ]
};

export const pluginId = META.id;

/* -------------------------------------------------------------------------- */
/*                                Data model                                  */
/* -------------------------------------------------------------------------- */

type ChangeKind = 'insert' | 'delete';

interface ChangeRecord {
  id: string;
  kind: ChangeKind;
  userId: string;
  timestamp: number;

  /** Path from editor root to range start & end (for persistence) */
  startPath: number[];
  startOffset: number;
  endPath: number[];
  endOffset: number;

  payload: string;            // inserted or deleted text
  status: 'pending' | 'accepted' | 'rejected';
}

/* -------------------------------------------------------------------------- */
/*                             Helper functions                               */
/* -------------------------------------------------------------------------- */

/** Produce child-index path from root → node (similar to comments plugin) */
function nodeToPath(editorRoot: HTMLElement, node: Node): number[] {
  const path: number[] = [];
  let current: Node | null = node;
  while (current && current !== editorRoot) {
    const parent = current.parentNode as Node;
    if (!parent) break;
    path.unshift(Array.prototype.indexOf.call(parent.childNodes, current));
    current = parent;
  }
  return path;
}

/* -------------------------------------------------------------------------- */
/*                               The plugin                                   */
/* -------------------------------------------------------------------------- */

class TrackChangesPlugin extends BasePlugin {
  /* -------------------------------------------------------------------- */
  /* Internal state                                                        */
  /* -------------------------------------------------------------------- */

  /** Whether new operations are recorded */
  private trackingEnabled = true;

  /** List of all changes this session */
  private readonly changeHistory: ChangeRecord[] = [];

  /** Use AbortController to release every listener on destroy */
  private readonly abort = new AbortController();

  /** Local user ID (demo only) */
  private readonly localUserId = `u-${Math.random().toString(36).slice(2, 8)}`;

  /** Guard flag to avoid infinite loops during DOM manipulation */
  private isApplyingInternalChange = false;

  /* ---------------------------- constructor --------------------------- */

  constructor() {
    super(META);
  }

  /* -------------------------------------------------------------------- */
  /* BasePlugin integration                                                */
  /* -------------------------------------------------------------------- */

  protected override onInit(): void {
    if (!this.editor) return;

    /* Listen to *every* editing operation through `beforeinput`.
       Works for typing, paste, drag-drop, cut, delete, etc. */
    const handler = (event: InputEvent) =>
      this.handleBeforeInput(event as InputEvent);
    this.editor!.getElement().addEventListener('beforeinput', handler, {
      signal: this.abort.signal
    });

    /* Highlight button state */
    this.updateToolbarVisual();
  }

  /**
   * Handle the track changes command
   * @param command The command to handle
   */
  protected override handleCommand(command: string): void {
    if (command === 'track-changes') {
      this.trackingEnabled = !this.trackingEnabled;
      this.updateToolbarVisual();
    }
  }

  /**
   * Update the toolbar visual state
   */
  private updateToolbarVisual(): void {
    const button = this.toolbarButtons[0];
    if (!button) return;
    button.classList.toggle('active', this.trackingEnabled);
    button.setAttribute(
      'title',
      this.trackingEnabled ? 'Change tracking ON' : 'Change tracking OFF'
    );
    button.setAttribute(
      'aria-pressed',
      this.trackingEnabled ? 'true' : 'false'
    );
  }

  /**
   * Handle before input events
   * @param event The event to handle
   */
  private handleBeforeInput(event: InputEvent): void {
    /* Never intercept while we're programmatically accepting / rejecting */
    if (!this.trackingEnabled || this.isApplyingInternalChange) return;
    if (!this.editor) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const startPath = nodeToPath(this.editor!.getElement(), range.startContainer);
    const endPath = nodeToPath(this.editor!.getElement(), range.endContainer);

    /* Handle “insertText” as insertion mark AFTER browser commit */
    if (event.inputType === 'insertText' && event.data !== null) {
      /* Let browser mutate DOM first */
      requestAnimationFrame(() => {
        const insertedNode = this.findInsertedNode(range);
        if (!insertedNode) return;

        const changeId = `c-${Date.now()}`;
        const mark = document.createElement('mark');
        mark.className = 'js-track-change px-px rounded-sm no-underline bg-green-100 dark:bg-green-800/40 text-green-700 dark:text-green-300';
        mark.dataset.changeId = changeId;
        mark.textContent = event.data!;
        insertedNode.textContent =
          insertedNode.textContent!.slice(0, -event.data!.length);
        range.collapse(); // caret after inserted char
        range.insertNode(mark);

        /* Record operation */
        this.changeHistory.push({
          id: changeId,
          kind: 'insert',
          userId: this.localUserId,
          timestamp: Date.now(),
          startPath,
          startOffset: range.startOffset,
          endPath,
          endOffset: range.endOffset,
          payload: event.data!,
          status: 'pending'
        });

        this.attachActionButtons(mark);
      });
      return;
    }

    /* Handle all deletes: convert deleted range to <del> */
    if (event.inputType.startsWith('delete')) {
      const deletedText = range.toString();
      if (!deletedText) return;

      event.preventDefault(); // stop browser from deleting
      const changeId = `c-${Date.now()}`;
      const delWrapper = document.createElement('del');
      delWrapper.className = 'js-track-change px-px rounded-sm line-through text-inherit bg-red-100 dark:bg-red-800/40 text-red-700 dark:text-red-400';
      delWrapper.dataset.changeId = changeId;
      delWrapper.textContent = deletedText;

      /* Replace selected content by our wrapper */
      range.deleteContents();
      range.insertNode(delWrapper);
      range.setStartAfter(delWrapper);
      range.collapse(true);

      /* Record op */
      this.changeHistory.push({
        id: changeId,
        kind: 'delete',
        userId: this.localUserId,
        timestamp: Date.now(),
        startPath,
        startOffset: range.startOffset,
        endPath,
        endOffset: range.endOffset,
        payload: deletedText,
        status: 'pending'
      });

      this.attachActionButtons(delWrapper);
      return;
    }
  }

  /** Attempt to locate the DOM Text node that browser just inserted into */
  private findInsertedNode(referenceRange: Range): Text | null {
    /* The char is normally appended to range.startContainer if it's a Text node */
    if (referenceRange.startContainer.nodeType === Node.TEXT_NODE) {
      return referenceRange.startContainer as Text;
    }
    /* Otherwise walk previous sibling */
    let node = referenceRange.startContainer.previousSibling;
    while (node) {
      if (node.nodeType === Node.TEXT_NODE) return node as Text;
      node = node.previousSibling;
    }
    return null;
  }

  /**
   * Attach action buttons to a change wrapper
   * @param wrapper The wrapper to attach buttons to
   */
  private attachActionButtons(wrapper: HTMLElement): void {
    const actionContainer = document.createElement('span');
    // Base opacity-70, full opacity on hover of the container itself.
    // Showing on hover of the mark/del would require JS or a parent 'group' class.
    actionContainer.className = 'inline-flex items-center gap-0.5 ml-0.5 text-xs align-middle opacity-70 hover:opacity-100 transition-opacity duration-150 ease-linear';

    const acceptButton = document.createElement('button');
    acceptButton.type = 'button';
    acceptButton.title = 'Accept change';
    acceptButton.textContent = '✅';
    acceptButton.className = 'border-none bg-transparent cursor-pointer px-0.5 leading-none text-xs opacity-80 hover:opacity-100 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300';
    acceptButton.addEventListener('click', () =>
      this.applyResolution(wrapper.dataset.changeId!, 'accept')
    );

    const rejectButton = document.createElement('button');
    rejectButton.type = 'button';
    rejectButton.title = 'Reject change';
    rejectButton.textContent = '❌';
    rejectButton.className = 'border-none bg-transparent cursor-pointer px-0.5 leading-none text-xs opacity-80 hover:opacity-100 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300';
    rejectButton.addEventListener('click', () =>
      this.applyResolution(wrapper.dataset.changeId!, 'reject')
    );

    actionContainer.append(acceptButton, rejectButton);
    wrapper.after(actionContainer);
  }

  /**
   * Apply a resolution to a change
   * @param changeId The ID of the change to resolve
   * @param decision The resolution decision
   */
  private applyResolution(changeId: string, decision: 'accept' | 'reject'): void {
    const record = this.changeHistory.find((c) => c.id === changeId);
    if (!record || record.status !== 'pending') return;

    this.isApplyingInternalChange = true;
    const wrapper = this.editor!.getElement().querySelector<HTMLElement>(
      `[data-change-id="${changeId}"]`
    );
    const controls = wrapper?.nextSibling;
    if (decision === 'accept') {
      if (record.kind === 'insert') {
        /* keep text, unwrap <mark> */
        const textNode = document.createTextNode(wrapper!.textContent!);
        wrapper!.replaceWith(textNode);
      } else {
        /* hide deleted content */
        wrapper!.remove();
      }
      record.status = 'accepted';
    } else {
      /* reject */
      if (record.kind === 'insert') {
        wrapper!.remove();
      } else {
        const textNode = document.createTextNode(record.payload);
        wrapper!.replaceWith(textNode);
      }
      record.status = 'rejected';
    }
    controls?.remove();
    this.isApplyingInternalChange = false;

    /* For undo stack */
    this.editor!.getElement().dispatchEvent(
      new InputEvent('input', { bubbles: true, cancelable: true })
    );
  }

  /**
   * Cleanup
   */
  public override destroy(): void {
    this.abort.abort();
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                                Export                                       */
/* -------------------------------------------------------------------------- */

const plugin: Plugin = createPlugin<TrackChangesPlugin>(
  META.id,
  (editor: Editor) => {
    const instance = new TrackChangesPlugin();
    instance.init(editor);
    return instance;
  },
  (instance: TrackChangesPlugin) => instance.destroy()
);

export default plugin;
