/**
 * FeatherJS Toolbar Layout Configuration
 *
 * This file defines the toolbar groups and their organization.
 * The layout follows the requirements specified in the project documentation.
 */

import type { ToolbarItem } from '../types';
import type { PluginManager } from '../plugins/plugin-manager';

// Define toolbar group types
export interface ToolbarGroup {
  id: string;
  name: string;
  plugins: string[];
  alignment?: 'left' | 'center' | 'right';
}

/**
 * Default toolbar layout as specified in requirements
 *
 * Default Toolbar Groups (left→right):
 * 1. Core: ↺ ↻ B I U Color Clear
 * 2. Blocks: H1 Lists Indent/Outdent Align Line-Height Quote CodeBlk
 * 3. Insert: Link Image Video Table HR Emoji Ω Math
 * 4. Collab: Comment Track-Changes Source Fullscreen
 * 5. Status (right-aligned): Word-Count Presence Avatars
 */
export const defaultToolbarLayout: ToolbarGroup[] = [
  {
    id: 'core',
    name: 'Core Formatting',
    plugins: [
      'undo',                // ↺
      'redo',                // ↻
      'bold',                // B
      'italic',              // I
      'underline',           // U
      'text-color',          // Color
      'clear-format'         // Clear
    ],
    alignment: 'left'
  },
  {
    id: 'blocks',
    name: 'Block Formatting',
    plugins: [
      'headings',            // H1
      'bullet-list',         // Lists
      'numbered-list',       // Lists
      'checklist',           // Lists
      'indent',              // Indent
      'outdent',             // Outdent
      'align',               // Align
      'line-height',         // Line Height
      'blockquote',          // Quote
      'code-block'           // CodeBlk
    ],
    alignment: 'left'
  },
  {
    id: 'insert',
    name: 'Insert Content',
    plugins: [
      'link',                // Link
      'image',               // Image
      'video',               // Video
      'table',               // Table
      'hr',                  // Horizontal Rule
      'emoji',               // Emoji
      'special-char',        // Special Character (Ω)
      'math'                 // Math
    ],
    alignment: 'left'
  },
  {
    id: 'utilities',
    name: 'Utilities',
    plugins: [
      'find-replace'         // Find & Replace
    ],
    alignment: 'left'
  },
  {
    id: 'collab',
    name: 'Collaboration',
    plugins: [
      'comments',            // Comment
      'track-changes',       // Track Changes
      'source-view',         // Source
      'fullscreen'           // Fullscreen
    ],
    alignment: 'left'
  },
  {
    id: 'status',
    name: 'Status',
    plugins: [
      'word-count',          // Word Count
      'presence',            // Presence
      'live-cursors'         // Avatars/Cursors
    ],
    alignment: 'right'
  }
];

/**
 * Toolbar layout manager class
 * Handles organizing and rendering toolbar groups
 */
export class ToolbarLayoutManager {
  private element: HTMLElement | null = null;
  private groups: ToolbarGroup[] = [];
  private pluginManager!: PluginManager;

  constructor(toolbarLayout: ToolbarGroup[] = defaultToolbarLayout) {
    this.groups = toolbarLayout;
  }

  /**
   * Initialize the toolbar layout
   * @param element - The toolbar container element
   * @param pluginManager - The plugin manager instance
   */
  public initialize(element: HTMLElement, pluginManager: PluginManager): void {
    this.element = element;
    this.pluginManager = pluginManager;
    this.render();
  }

  /**
   * Render the toolbar groups and buttons
   */
  private render(): void {
    if (!this.element) return;

    // Clear existing content
    this.element.innerHTML = '';

    // Create container for left-aligned groups
    const leftContainer = document.createElement('div');
    leftContainer.className = 'flex flex-wrap items-center flex-grow gap-x-1 gap-y-1'; // Adjusted gap

    // Create container for right-aligned groups
    const rightContainer = document.createElement('div');
    rightContainer.className = 'flex flex-wrap items-center justify-end gap-x-1 gap-y-1 ml-auto'; // Added ml-auto for right alignment

    // Add groups to their respective containers
    this.groups.forEach(group => {
      const groupElement = this.createGroupElement(group);

      if (group.alignment === 'right') {
        rightContainer.appendChild(groupElement);
      } else {
        leftContainer.appendChild(groupElement);
      }
    });

    // Add containers to toolbar
    this.element.appendChild(leftContainer);
    this.element.appendChild(rightContainer);

    // Styles are now handled by Tailwind classes, so addStyles() is removed.
  }

  /**
   * Create a toolbar group element
   * @param group - The toolbar group configuration
   * @returns The created group element
   */
  private createGroupElement(group: ToolbarGroup): HTMLElement {
    const groupElement = document.createElement('div');
    // Tailwind classes for a toolbar group
    // The media query for responsive behavior will be handled by Tailwind's responsive prefixes on the parent toolbar or here if needed.
    // For simplicity, the border logic from CSS is replicated. A more Tailwind-idiomatic way might involve `space-x-` on parent and no borders, or conditional borders.
    groupElement.className = `flex items-center p-1 mx-0.5 border-r border-gray-200 dark:border-slate-700 last:border-r-0 group-${group.id}`;
    if (group.id === 'status') { // Specific case from old CSS
        groupElement.classList.remove('border-r', 'border-gray-200', 'dark:border-slate-700');
    }
    groupElement.setAttribute('data-group', group.id);
    groupElement.setAttribute('aria-label', group.name);

    // Add plugins to group
    group.plugins.forEach(pluginId => {
      const plugin = this.pluginManager?.get(pluginId);

      if (plugin && plugin.toolbarItems) {
        plugin.toolbarItems.forEach((item: ToolbarItem) => {
          const button = document.createElement('button');
          button.type = 'button';
          button.className = [
            'p-2', 'rounded-md', 'text-sm', 'font-medium',
            'transition-colors', 'duration-150', 'ease-in-out',
            'bg-gray-100', 'text-gray-700', 'hover:bg-gray-200',
            'dark:bg-slate-700', 'dark:text-slate-200', 'dark:hover:bg-slate-600',
            'focus-visible:outline-none', 'focus-visible:ring-2', 'focus-visible:ring-blue-500',
            'dark:focus-visible:ring-blue-400',
            'focus-visible:ring-offset-2', 'dark:focus-visible:ring-offset-slate-800'
          ].join(' ');

          button.setAttribute('data-command', item.command || pluginId);
          button.innerHTML = item.icon || ''; // Use innerHTML for icons (e.g. SVG strings or emojis)
          button.setAttribute('aria-label', item.ariaLabel || item.label || pluginId);

          // Use item.title as defined in ToolbarItem interface (from types/index.ts)
          // item.title corresponds to item.tooltip from ToolbarItemConfig (from base-plugin.ts)
          const tooltipText = item.title || item.label || pluginId;
          if (tooltipText || item.shortcut) {
            let titleAttr = tooltipText;
            if (item.shortcut) {
              titleAttr += ` (${item.shortcut})`;
            }
            button.setAttribute('title', titleAttr);
          }

          // Add click handler to dispatch command
          button.addEventListener('click', () => {
            // Dispatch a custom event that plugins can listen for
            document.dispatchEvent(new CustomEvent('feather:command', {
              detail: {
                command: item.command || pluginId,
                pluginId: pluginId,
                source: 'toolbar'
              }
            }));
          });

          groupElement.appendChild(button);
        });
      }
    });

    return groupElement;
  }

  // addStyles() method is removed as styles are now applied via Tailwind classes.
}

export default new ToolbarLayoutManager();
