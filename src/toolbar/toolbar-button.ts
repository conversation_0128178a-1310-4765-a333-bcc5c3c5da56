/**
 * This custom element <toolbar-button> is now obsolete.
 * The BasePlugin and ToolbarLayoutManager have been refactored to use
 * standard HTML <button> elements styled with Tailwind CSS.
 * This file can be safely deleted.
 */

// Original content commented out or removed:
/*
export class ToolbarButton extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
  }
  
  connectedCallback() {
    const command = this.getAttribute('data-command');
    const icon = this.getAttribute('data-icon') || '';
    const label = this.getAttribute('data-label') || command;
    
    if (!this.shadowRoot) return;

    this.shadowRoot.innerHTML = `
      <style>
        // ... old styles ...
      </style>
      <button title="${label}">${icon || label}</button>
    `;
    
    const button = this.shadowRoot.querySelector('button');
    
    if (button) {
      button.addEventListener('click', () => {
        const event = new CustomEvent('toolbar-action', {
          bubbles: true,
          composed: true,
          detail: { command }
        });
        this.dispatchEvent(event);
      });
    }
  }
  
  setActive(isActive: boolean) {
    if (!this.shadowRoot) return;
    const button = this.shadowRoot.querySelector('button');
    if (button) {
      if (isActive) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    }
  }
}

if (!customElements.get('toolbar-button')) {
  customElements.define('toolbar-button', ToolbarButton);
}
*/
