/**
 * Theme Performance Benchmark Utilities
 * Provides automated performance testing and regression detection for theme operations.
 * Follows Single Responsibility Principle by focusing solely on benchmarking.
 */

import { IPerformanceMonitor, IThemeLogger } from '../interfaces/core-interfaces';
import { ThemeOperation } from '../theme-types';

/**
 * Benchmark scenario configuration
 */
export interface BenchmarkScenario {
  /** Unique identifier for the scenario */
  id: string;
  /** Human-readable name */
  name: string;
  /** Description of what this scenario tests */
  description: string;
  /** Theme operation being benchmarked */
  operation: ThemeOperation;
  /** Expected performance threshold in milliseconds */
  expectedThreshold: number;
  /** Number of iterations to run */
  iterations: number;
  /** Setup function to prepare for the benchmark */
  setup?: () => Promise<void> | void;
  /** Cleanup function to run after the benchmark */
  cleanup?: () => Promise<void> | void;
  /** Custom validation function for results */
  validate?: (results: BenchmarkResult) => boolean;
}

/**
 * Benchmark execution result
 */
export interface BenchmarkResult {
  /** Scenario that was executed */
  scenario: BenchmarkScenario;
  /** Individual iteration durations in milliseconds */
  durations: number[];
  /** Average duration across all iterations */
  averageDuration: number;
  /** Minimum duration recorded */
  minDuration: number;
  /** Maximum duration recorded */
  maxDuration: number;
  /** Standard deviation of durations */
  standardDeviation: number;
  /** Whether the benchmark passed the threshold */
  passed: boolean;
  /** Memory usage before benchmark */
  memoryBefore?: number;
  /** Memory usage after benchmark */
  memoryAfter?: number;
  /** Memory delta (after - before) */
  memoryDelta?: number;
  /** Timestamp when benchmark was executed */
  timestamp: number;
  /** Any errors that occurred during execution */
  errors: string[];
}

/**
 * Benchmark suite containing multiple scenarios
 */
export interface BenchmarkSuite {
  /** Suite identifier */
  id: string;
  /** Suite name */
  name: string;
  /** Suite description */
  description: string;
  /** Scenarios in this suite */
  scenarios: BenchmarkScenario[];
  /** Global setup for the entire suite */
  globalSetup?: () => Promise<void> | void;
  /** Global cleanup for the entire suite */
  globalCleanup?: () => Promise<void> | void;
}

/**
 * Benchmark suite execution results
 */
export interface BenchmarkSuiteResult {
  /** Suite that was executed */
  suite: BenchmarkSuite;
  /** Results for each scenario */
  results: BenchmarkResult[];
  /** Overall suite statistics */
  statistics: {
    totalScenarios: number;
    passedScenarios: number;
    failedScenarios: number;
    totalDuration: number;
    averageDuration: number;
  };
  /** Timestamp when suite was executed */
  timestamp: number;
}

/**
 * Performance regression detection configuration
 */
export interface RegressionConfig {
  /** Percentage threshold for regression detection (e.g., 0.1 = 10% slower) */
  regressionThreshold: number;
  /** Number of historical results to compare against */
  historicalSampleSize: number;
  /** Whether to use median or average for comparison */
  comparisonMethod: 'median' | 'average';
}

/**
 * Theme Benchmark Runner
 * Executes performance benchmarks and detects regressions
 */
export class ThemeBenchmarkRunner {
  private performanceMonitor: IPerformanceMonitor;
  private logger: IThemeLogger;
  private historicalResults: Map<string, BenchmarkResult[]>;
  private regressionConfig: RegressionConfig;

  constructor(
    performanceMonitor: IPerformanceMonitor,
    logger: IThemeLogger,
    regressionConfig?: Partial<RegressionConfig>
  ) {
    this.performanceMonitor = performanceMonitor;
    this.logger = logger;
    this.historicalResults = new Map();
    this.regressionConfig = {
      regressionThreshold: 0.15, // 15% slower is considered a regression
      historicalSampleSize: 10,
      comparisonMethod: 'median',
      ...regressionConfig
    };
  }

  /**
   * Execute a single benchmark scenario
   */
  public async executeBenchmark(scenario: BenchmarkScenario): Promise<BenchmarkResult> {
    this.logger.logUserAction('benchmark_start', undefined, {
      scenarioId: scenario.id,
      scenarioName: scenario.name,
      iterations: scenario.iterations
    });

    const durations: number[] = [];
    const errors: string[] = [];
    let memoryBefore: number | undefined;
    let memoryAfter: number | undefined;

    try {
      // Setup
      if (scenario.setup) {
        await scenario.setup();
      }

      // Record initial memory
      memoryBefore = this.getMemoryUsage();

      // Execute iterations
      for (let i = 0; i < scenario.iterations; i++) {
        try {
          const startTime = performance.now();

          // Execute the operation through performance monitor
          await this.performanceMonitor.measureOperation(
            scenario.operation,
            async () => {
              // Simulate the operation - in real usage, this would be the actual operation
              await this.simulateOperation(scenario.operation);
            }
          );

          const endTime = performance.now();
          durations.push(endTime - startTime);
        } catch (error) {
          errors.push(error instanceof Error ? error.message : 'Unknown error');
        }
      }

      // Record final memory
      memoryAfter = this.getMemoryUsage();

      // Cleanup
      if (scenario.cleanup) {
        await scenario.cleanup();
      }
    } catch (error) {
      errors.push(`Setup/cleanup error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Calculate statistics
    const averageDuration = durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;
    const minDuration = durations.length > 0 ? Math.min(...durations) : 0;
    const maxDuration = durations.length > 0 ? Math.max(...durations) : 0;
    const standardDeviation = this.calculateStandardDeviation(durations, averageDuration);
    const passed = averageDuration <= scenario.expectedThreshold && errors.length === 0;

    const result: BenchmarkResult = {
      scenario,
      durations,
      averageDuration,
      minDuration,
      maxDuration,
      standardDeviation,
      passed,
      memoryBefore,
      memoryAfter,
      memoryDelta: memoryBefore && memoryAfter ? memoryAfter - memoryBefore : undefined,
      timestamp: Date.now(),
      errors
    };

    // Validate with custom validator if provided
    if (scenario.validate && !scenario.validate(result)) {
      result.passed = false;
      result.errors.push('Custom validation failed');
    }

    // Store result for regression detection
    this.storeResult(scenario.id, result);

    this.logger.logUserAction('benchmark_complete', undefined, {
      scenarioId: scenario.id,
      passed: result.passed,
      averageDuration: result.averageDuration,
      threshold: scenario.expectedThreshold,
      errors: result.errors
    });

    return result;
  }

  /**
   * Execute a benchmark suite
   */
  public async executeSuite(suite: BenchmarkSuite): Promise<BenchmarkSuiteResult> {
    this.logger.logUserAction('benchmark_suite_start', undefined, {
      suiteId: suite.id,
      suiteName: suite.name,
      scenarioCount: suite.scenarios.length
    });

    const results: BenchmarkResult[] = [];
    const startTime = performance.now();

    try {
      // Global setup
      if (suite.globalSetup) {
        await suite.globalSetup();
      }

      // Execute each scenario
      for (const scenario of suite.scenarios) {
        const result = await this.executeBenchmark(scenario);
        results.push(result);
      }

      // Global cleanup
      if (suite.globalCleanup) {
        await suite.globalCleanup();
      }
    } catch (error) {
      this.logger.logUserAction('benchmark_suite_error', undefined, {
        suiteId: suite.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    const endTime = performance.now();
    const totalDuration = endTime - startTime;

    // Calculate suite statistics
    const passedScenarios = results.filter(r => r.passed).length;
    const failedScenarios = results.length - passedScenarios;
    const averageDuration = results.length > 0 ?
      results.reduce((sum, r) => sum + r.averageDuration, 0) / results.length : 0;

    const suiteResult: BenchmarkSuiteResult = {
      suite,
      results,
      statistics: {
        totalScenarios: results.length,
        passedScenarios,
        failedScenarios,
        totalDuration,
        averageDuration
      },
      timestamp: Date.now()
    };

    this.logger.logUserAction('benchmark_suite_complete', undefined, {
      suiteId: suite.id,
      totalScenarios: suiteResult.statistics.totalScenarios,
      passedScenarios: suiteResult.statistics.passedScenarios,
      failedScenarios: suiteResult.statistics.failedScenarios,
      totalDuration: suiteResult.statistics.totalDuration
    });

    return suiteResult;
  }

  /**
   * Detect performance regressions for a scenario
   */
  public detectRegression(scenarioId: string, currentResult: BenchmarkResult): {
    isRegression: boolean;
    baselineValue: number;
    currentValue: number;
    percentageChange: number;
    message: string;
  } {
    const historicalResults = this.historicalResults.get(scenarioId) || [];

    if (historicalResults.length < 2) {
      return {
        isRegression: false,
        baselineValue: 0,
        currentValue: currentResult.averageDuration,
        percentageChange: 0,
        message: 'Insufficient historical data for regression detection'
      };
    }

    // Get baseline value from historical results
    const recentResults = historicalResults
      .slice(-this.regressionConfig.historicalSampleSize)
      .filter(r => r.timestamp !== currentResult.timestamp); // Exclude current result

    const baselineValue = this.regressionConfig.comparisonMethod === 'median' ?
      this.calculateMedian(recentResults.map(r => r.averageDuration)) :
      recentResults.reduce((sum, r) => sum + r.averageDuration, 0) / recentResults.length;

    const currentValue = currentResult.averageDuration;
    const percentageChange = (currentValue - baselineValue) / baselineValue;
    const isRegression = percentageChange > this.regressionConfig.regressionThreshold;

    return {
      isRegression,
      baselineValue,
      currentValue,
      percentageChange,
      message: isRegression ?
        `Performance regression detected: ${(percentageChange * 100).toFixed(1)}% slower than baseline` :
        `Performance within acceptable range: ${(percentageChange * 100).toFixed(1)}% change from baseline`
    };
  }

  /**
   * Get historical results for a scenario
   */
  public getHistoricalResults(scenarioId: string): ReadonlyArray<BenchmarkResult> {
    return this.historicalResults.get(scenarioId) || [];
  }

  /**
   * Clear historical results
   */
  public clearHistoricalResults(scenarioId?: string): void {
    if (scenarioId) {
      this.historicalResults.delete(scenarioId);
    } else {
      this.historicalResults.clear();
    }
  }

  private storeResult(scenarioId: string, result: BenchmarkResult): void {
    const results = this.historicalResults.get(scenarioId) || [];
    results.push(result);

    // Keep only the most recent results
    const maxResults = this.regressionConfig.historicalSampleSize * 2;
    if (results.length > maxResults) {
      results.splice(0, results.length - maxResults);
    }

    this.historicalResults.set(scenarioId, results);
  }

  private calculateStandardDeviation(values: number[], mean: number): number {
    if (values.length === 0) return 0;

    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    const variance = squaredDifferences.reduce((sum, diff) => sum + diff, 0) / values.length;
    return Math.sqrt(variance);
  }

  private calculateMedian(values: number[]): number {
    if (values.length === 0) return 0;

    const sorted = [...values].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
      return sorted[middle];
    }
  }

  private getMemoryUsage(): number | undefined {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const performanceWithMemory = performance as Performance & {
        memory?: {
          usedJSHeapSize?: number;
        };
      };
      return performanceWithMemory.memory?.usedJSHeapSize;
    }
    return undefined;
  }

  private async simulateOperation(operation: ThemeOperation): Promise<void> {
    // Simulate different operations with appropriate delays
    switch (operation) {
      case ThemeOperation.THEME_SWITCH:
        // Simulate theme switching work
        await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
        break;
      case ThemeOperation.CSS_INJECT:
        // Simulate CSS injection work
        await new Promise(resolve => setTimeout(resolve, Math.random() * 5));
        break;
      case ThemeOperation.ELEMENT_CREATE:
        // Simulate element creation work
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1));
        break;
      default:
        // Default simulation
        await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
        break;
    }
  }
}
