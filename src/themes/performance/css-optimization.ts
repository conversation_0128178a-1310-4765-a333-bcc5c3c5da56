/**
 * CSS Performance Optimization Strategies
 * Provides efficient CSS variable injection and DOM manipulation optimization.
 * Follows Single Responsibility Principle by focusing solely on CSS performance.
 */

import { ThemeDefinition, CSSVariableMapping } from '../theme-types';
import { IThemeLogger } from '../interfaces/core-interfaces';

/**
 * CSS variable cache entry
 */
interface CSSVariableCache {
  /** The CSS variable name */
  name: string;
  /** The current value */
  value: string;
  /** When this entry was last updated */
  lastUpdated: number;
  /** How many times this variable has been accessed */
  accessCount: number;
}

/**
 * CSS injection strategy interface
 */
export interface ICSSInjectionStrategy {
  /** Strategy name for identification */
  readonly name: string;
  /** Priority for strategy selection (higher = more preferred) */
  readonly priority: number;

  /**
   * Check if this strategy can handle the given context
   */
  canHandle(variableCount: number, targetElement?: Element): boolean;

  /**
   * Inject CSS variables using this strategy
   */
  inject(variables: CSSVariableMapping[], targetElement?: Element): Promise<void>;
}

/**
 * Batch CSS injection strategy
 * Groups multiple CSS variable updates into a single DOM operation
 */
export class BatchCSSInjectionStrategy implements ICSSInjectionStrategy {
  public readonly name = 'BatchCSSInjectionStrategy';
  public readonly priority = 80;

  public canHandle(variableCount: number, _targetElement?: Element): boolean {
    return variableCount > 5; // Use batch strategy for multiple variables
  }

  public async inject(variables: CSSVariableMapping[], targetElement?: Element): Promise<void> {
    const target = targetElement || document.documentElement;

    // Build CSS text in memory first
    const cssText = variables
      .map(variable => `--${variable.name}: ${variable.value};`)
      .join(' ');

    // Apply all variables in a single operation
    (target as HTMLElement).style.cssText += cssText;
  }
}

/**
 * Individual CSS injection strategy
 * Sets CSS variables one by one (better for small numbers of variables)
 */
export class IndividualCSSInjectionStrategy implements ICSSInjectionStrategy {
  public readonly name = 'IndividualCSSInjectionStrategy';
  public readonly priority = 60;

  public canHandle(variableCount: number, _targetElement?: Element): boolean {
    return variableCount <= 5; // Use individual strategy for few variables
  }

  public async inject(variables: CSSVariableMapping[], targetElement?: Element): Promise<void> {
    const target = targetElement || document.documentElement;

    // Set each variable individually
    for (const variable of variables) {
      (target as HTMLElement).style.setProperty(`--${variable.name}`, variable.value);
    }
  }
}

/**
 * Stylesheet CSS injection strategy
 * Creates a dedicated stylesheet for theme variables (best for large numbers)
 */
export class StylesheetCSSInjectionStrategy implements ICSSInjectionStrategy {
  public readonly name = 'StylesheetCSSInjectionStrategy';
  public readonly priority = 90;
  private stylesheet: CSSStyleSheet | null = null;

  public canHandle(variableCount: number, targetElement?: Element): boolean {
    return variableCount > 20 && !targetElement; // Only for document-level injection with many variables
  }

  public async inject(variables: CSSVariableMapping[], _targetElement?: Element): Promise<void> {
    // Create or reuse stylesheet
    if (!this.stylesheet) {
      const style = document.createElement('style');
      style.id = 'featherjs-theme-variables';
      document.head.appendChild(style);
      this.stylesheet = style.sheet as CSSStyleSheet;
    }

    // Clear existing rules
    while (this.stylesheet.cssRules.length > 0) {
      this.stylesheet.deleteRule(0);
    }

    // Build CSS rule
    const cssVariables = variables
      .map(variable => `  --${variable.name}: ${variable.value};`)
      .join('\n');

    const cssRule = `:root {\n${cssVariables}\n}`;
    this.stylesheet.insertRule(cssRule, 0);
  }
}

/**
 * CSS Optimizer
 * Manages efficient CSS variable injection with caching and optimization strategies
 */
export class CSSOptimizer {
  private cache: Map<string, CSSVariableCache>;
  private strategies: ICSSInjectionStrategy[];
  private logger: IThemeLogger;
  private pendingUpdates: Map<string, string>;
  private updateTimer: number | null;

  constructor(logger: IThemeLogger) {
    this.cache = new Map();
    this.logger = logger;
    this.pendingUpdates = new Map();
    this.updateTimer = null;

    // Initialize strategies
    this.strategies = [
      new StylesheetCSSInjectionStrategy(),
      new BatchCSSInjectionStrategy(),
      new IndividualCSSInjectionStrategy()
    ];

    // Sort by priority (highest first)
    this.strategies.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Inject CSS variables from a theme definition
   */
  public async injectThemeVariables(
    theme: ThemeDefinition,
    targetElement?: Element
  ): Promise<void> {
    const startTime = performance.now();

    this.logger.logUserAction('css_optimization_start', theme.id, {
      targetElement: targetElement?.tagName || 'document',
      colorCount: Object.keys(theme.colors).length
    });

    try {
      // Convert theme colors to CSS variable mappings
      const variables: CSSVariableMapping[] = Object.entries(theme.colors).map(([key, value]) => ({
        name: `theme-${key}`,
        value,
        category: 'core' as const
      }));

      await this.injectVariables(variables, targetElement);

      const duration = performance.now() - startTime;
      this.logger.logUserAction('css_optimization_complete', theme.id, {
        duration,
        variableCount: variables.length,
        targetElement: targetElement?.tagName || 'document'
      });
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logger.logUserAction('css_optimization_error', theme.id, {
        duration,
        error: error instanceof Error ? error.message : 'Unknown error',
        targetElement: targetElement?.tagName || 'document'
      });
      throw error;
    }
  }

  /**
   * Inject CSS variables with optimization
   */
  public async injectVariables(
    variables: CSSVariableMapping[],
    targetElement?: Element
  ): Promise<void> {
    // Filter out variables that haven't changed
    const changedVariables = this.filterChangedVariables(variables);

    if (changedVariables.length === 0) {
      this.logger.logUserAction('css_optimization_skipped', undefined, {
        reason: 'no_changes',
        totalVariables: variables.length
      });
      return;
    }

    // Select the best injection strategy
    const strategy = this.selectStrategy(changedVariables.length, targetElement);

    if (!strategy) {
      throw new Error('No suitable CSS injection strategy found');
    }

    // Inject variables using the selected strategy
    await strategy.inject(changedVariables, targetElement);

    // Update cache
    this.updateCache(changedVariables);

    this.logger.logUserAction('css_variables_injected', undefined, {
      strategy: strategy.name,
      variableCount: changedVariables.length,
      totalVariables: variables.length,
      targetElement: targetElement?.tagName || 'document'
    });
  }

  /**
   * Update CSS variables with debouncing
   */
  public updateVariablesDebounced(
    variables: CSSVariableMapping[],
    targetElement?: Element,
    debounceMs: number = 16 // One frame at 60fps
  ): void {
    // Add variables to pending updates
    for (const variable of variables) {
      this.pendingUpdates.set(variable.name, variable.value);
    }

    // Clear existing timer
    if (this.updateTimer !== null) {
      clearTimeout(this.updateTimer);
    }

    // Set new timer
    this.updateTimer = window.setTimeout(async () => {
      const pendingVariables: CSSVariableMapping[] = Array.from(this.pendingUpdates.entries())
        .map(([name, value]) => ({
          name,
          value,
          category: 'core' as const
        }));

      this.pendingUpdates.clear();
      this.updateTimer = null;

      try {
        await this.injectVariables(pendingVariables, targetElement);
      } catch (error) {
        this.logger.logUserAction('css_optimization_debounced_error', undefined, {
          error: error instanceof Error ? error.message : 'Unknown error',
          variableCount: pendingVariables.length
        });
      }
    }, debounceMs);
  }

  /**
   * Get cached variable value
   */
  public getCachedVariable(name: string): string | undefined {
    const cached = this.cache.get(name);
    if (cached) {
      cached.accessCount++;
      return cached.value;
    }
    return undefined;
  }

  /**
   * Clear the CSS variable cache
   */
  public clearCache(): void {
    this.cache.clear();
    this.logger.logUserAction('css_cache_cleared', undefined, {
      timestamp: Date.now()
    });
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    size: number;
    totalAccesses: number;
    averageAccesses: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    const entries = Array.from(this.cache.values());
    const totalAccesses = entries.reduce((sum, entry) => sum + entry.accessCount, 0);
    const timestamps = entries.map(entry => entry.lastUpdated);

    return {
      size: this.cache.size,
      totalAccesses,
      averageAccesses: entries.length > 0 ? totalAccesses / entries.length : 0,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0
    };
  }

  /**
   * Register a custom CSS injection strategy
   */
  public registerStrategy(strategy: ICSSInjectionStrategy): void {
    this.strategies.push(strategy);
    this.strategies.sort((a, b) => b.priority - a.priority);

    this.logger.logUserAction('css_strategy_registered', undefined, {
      strategyName: strategy.name,
      priority: strategy.priority
    });
  }

  private filterChangedVariables(variables: CSSVariableMapping[]): CSSVariableMapping[] {
    return variables.filter(variable => {
      const cached = this.cache.get(variable.name);
      return !cached || cached.value !== variable.value;
    });
  }

  private selectStrategy(variableCount: number, targetElement?: Element): ICSSInjectionStrategy | null {
    for (const strategy of this.strategies) {
      if (strategy.canHandle(variableCount, targetElement)) {
        return strategy;
      }
    }
    return null;
  }

  private updateCache(variables: CSSVariableMapping[]): void {
    const now = Date.now();

    for (const variable of variables) {
      const existing = this.cache.get(variable.name);
      this.cache.set(variable.name, {
        name: variable.name,
        value: variable.value,
        lastUpdated: now,
        accessCount: existing?.accessCount || 0
      });
    }

    // Clean up old cache entries (keep only the most recent 1000)
    if (this.cache.size > 1000) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => b.lastUpdated - a.lastUpdated);

      this.cache.clear();
      entries.slice(0, 1000).forEach(([key, value]) => {
        this.cache.set(key, value);
      });
    }
  }
}

/**
 * Factory function to create CSS optimization strategies
 */
export function createCSSOptimizationStrategies(): ICSSInjectionStrategy[] {
  return [
    new StylesheetCSSInjectionStrategy(),
    new BatchCSSInjectionStrategy(),
    new IndividualCSSInjectionStrategy()
  ];
}

/**
 * Utility function to measure CSS injection performance
 */
export async function measureCSSInjectionPerformance(
  variables: CSSVariableMapping[],
  targetElement?: Element
): Promise<{
  duration: number;
  variableCount: number;
  strategy: string;
}> {
  const startTime = performance.now();

  // Use batch strategy for measurement
  const strategy = new BatchCSSInjectionStrategy();
  await strategy.inject(variables, targetElement);

  const endTime = performance.now();

  return {
    duration: endTime - startTime,
    variableCount: variables.length,
    strategy: strategy.name
  };
}
