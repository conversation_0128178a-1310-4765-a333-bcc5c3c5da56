/**
 * DOM Manipulation Optimizer
 * Provides efficient DOM updates with batching and lifecycle management.
 * Follows Single Responsibility Principle by focusing solely on DOM optimization.
 */

import { IThemeLogger } from '../interfaces/core-interfaces';

/**
 * DOM update operation types
 */
export enum DOMUpdateType {
  CLASS_ADD = 'class_add',
  CLASS_REMOVE = 'class_remove',
  STYLE_SET = 'style_set',
  ATTRIBUTE_SET = 'attribute_set',
  ATTRIBUTE_REMOVE = 'attribute_remove',
  ELEMENT_CREATE = 'element_create',
  ELEMENT_REMOVE = 'element_remove'
}

/**
 * DOM update operation
 */
export interface DOMUpdateOperation {
  /** Unique identifier for this operation */
  id: string;
  /** Type of DOM operation */
  type: DOMUpdateType;
  /** Target element for the operation */
  element: Element;
  /** Operation-specific data */
  data: {
    className?: string;
    property?: string;
    value?: string;
    attribute?: string;
  };
  /** Priority of this operation (higher = more important) */
  priority: number;
  /** Timestamp when operation was queued */
  timestamp: number;
}

/**
 * Element tracking information
 */
interface ElementTrackingInfo {
  /** Unique identifier for the element */
  id: string;
  /** Element reference */
  element: Element;
  /** Element role/type for optimization */
  role: string;
  /** When this element was registered */
  registeredAt: number;
  /** Last time this element was updated */
  lastUpdated: number;
  /** Number of updates performed on this element */
  updateCount: number;
  /** Whether this element is currently visible */
  isVisible: boolean;
}

/**
 * Batch update configuration
 */
export interface BatchUpdateConfig {
  /** Maximum time to wait before forcing a batch update (ms) */
  maxWaitTime: number;
  /** Maximum number of operations to batch together */
  maxBatchSize: number;
  /** Whether to use requestAnimationFrame for batching */
  useAnimationFrame: boolean;
  /** Whether to prioritize visible elements */
  prioritizeVisible: boolean;
}

/**
 * DOM Optimizer
 * Manages efficient DOM updates with batching and element lifecycle management
 */
export class DOMOptimizer {
  private logger: IThemeLogger;
  private pendingOperations: DOMUpdateOperation[];
  private trackedElements: Map<string, ElementTrackingInfo>;
  private batchTimer: number | null;
  private animationFrameId: number | null;
  private config: BatchUpdateConfig;
  private intersectionObserver: IntersectionObserver | null;
  private operationIdCounter: number;

  constructor(logger: IThemeLogger, config?: Partial<BatchUpdateConfig>) {
    this.logger = logger;
    this.pendingOperations = [];
    this.trackedElements = new Map();
    this.batchTimer = null;
    this.animationFrameId = null;
    this.operationIdCounter = 0;

    this.config = {
      maxWaitTime: 16, // One frame at 60fps
      maxBatchSize: 100,
      useAnimationFrame: true,
      prioritizeVisible: true,
      ...config
    };

    // Set up intersection observer for visibility tracking
    this.intersectionObserver = this.createIntersectionObserver();
  }

  /**
   * Register an element for tracking and optimization
   */
  public registerElement(element: Element, role: string): string {
    const id = this.generateElementId();
    const trackingInfo: ElementTrackingInfo = {
      id,
      element,
      role,
      registeredAt: Date.now(),
      lastUpdated: 0,
      updateCount: 0,
      isVisible: true // Assume visible until intersection observer reports otherwise
    };

    this.trackedElements.set(id, trackingInfo);

    // Start observing visibility if intersection observer is available
    if (this.intersectionObserver) {
      this.intersectionObserver.observe(element);
    }

    this.logger.logUserAction('dom_element_registered', undefined, {
      elementId: id,
      role,
      tagName: element.tagName
    });

    return id;
  }

  /**
   * Unregister an element from tracking
   */
  public unregisterElement(elementId: string): void {
    const trackingInfo = this.trackedElements.get(elementId);
    if (!trackingInfo) {
      return;
    }

    // Stop observing visibility
    if (this.intersectionObserver) {
      this.intersectionObserver.unobserve(trackingInfo.element);
    }

    this.trackedElements.delete(elementId);

    this.logger.logUserAction('dom_element_unregistered', undefined, {
      elementId,
      role: trackingInfo.role,
      updateCount: trackingInfo.updateCount
    });
  }

  /**
   * Queue a DOM update operation
   */
  public queueUpdate(operation: Omit<DOMUpdateOperation, 'id' | 'timestamp'>): string {
    const id = this.generateOperationId();
    const fullOperation: DOMUpdateOperation = {
      ...operation,
      id,
      timestamp: Date.now()
    };

    this.pendingOperations.push(fullOperation);

    // Schedule batch processing
    this.scheduleBatchUpdate();

    this.logger.logUserAction('dom_update_queued', undefined, {
      operationId: id,
      type: operation.type,
      priority: operation.priority,
      queueSize: this.pendingOperations.length
    });

    return id;
  }

  /**
   * Add a CSS class to an element
   */
  public addClass(element: Element, className: string, priority: number = 50): string {
    return this.queueUpdate({
      type: DOMUpdateType.CLASS_ADD,
      element,
      data: { className },
      priority
    });
  }

  /**
   * Remove a CSS class from an element
   */
  public removeClass(element: Element, className: string, priority: number = 50): string {
    return this.queueUpdate({
      type: DOMUpdateType.CLASS_REMOVE,
      element,
      data: { className },
      priority
    });
  }

  /**
   * Set a style property on an element
   */
  public setStyle(element: Element, property: string, value: string, priority: number = 50): string {
    return this.queueUpdate({
      type: DOMUpdateType.STYLE_SET,
      element,
      data: { property, value },
      priority
    });
  }

  /**
   * Set an attribute on an element
   */
  public setAttribute(element: Element, attribute: string, value: string, priority: number = 50): string {
    return this.queueUpdate({
      type: DOMUpdateType.ATTRIBUTE_SET,
      element,
      data: { attribute, value },
      priority
    });
  }

  /**
   * Force immediate execution of all pending operations
   */
  public async flushUpdates(): Promise<void> {
    if (this.pendingOperations.length === 0) {
      return;
    }

    this.cancelScheduledUpdate();
    await this.executeBatchUpdate();
  }

  /**
   * Get statistics about tracked elements and operations
   */
  public getStatistics(): {
    trackedElements: number;
    pendingOperations: number;
    totalUpdates: number;
    visibleElements: number;
    averageUpdatesPerElement: number;
  } {
    const totalUpdates = Array.from(this.trackedElements.values())
      .reduce((sum, info) => sum + info.updateCount, 0);

    const visibleElements = Array.from(this.trackedElements.values())
      .filter(info => info.isVisible).length;

    return {
      trackedElements: this.trackedElements.size,
      pendingOperations: this.pendingOperations.length,
      totalUpdates,
      visibleElements,
      averageUpdatesPerElement: this.trackedElements.size > 0 ?
        totalUpdates / this.trackedElements.size : 0
    };
  }

  /**
   * Clean up resources and stop tracking
   */
  public dispose(): void {
    this.cancelScheduledUpdate();

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
      this.intersectionObserver = null;
    }

    this.trackedElements.clear();
    this.pendingOperations = [];

    this.logger.logUserAction('dom_optimizer_disposed', undefined, {
      timestamp: Date.now()
    });
  }

  private scheduleBatchUpdate(): void {
    if (this.batchTimer !== null || this.animationFrameId !== null) {
      return; // Already scheduled
    }

    if (this.config.useAnimationFrame) {
      this.animationFrameId = requestAnimationFrame(() => {
        this.animationFrameId = null;
        this.executeBatchUpdate();
      });
    } else {
      this.batchTimer = window.setTimeout(() => {
        this.batchTimer = null;
        this.executeBatchUpdate();
      }, this.config.maxWaitTime);
    }

    // Also set a maximum wait timer if using animation frame
    if (this.config.useAnimationFrame && this.config.maxWaitTime > 0) {
      this.batchTimer = window.setTimeout(() => {
        this.cancelScheduledUpdate();
        this.executeBatchUpdate();
      }, this.config.maxWaitTime);
    }
  }

  private cancelScheduledUpdate(): void {
    if (this.batchTimer !== null) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private async executeBatchUpdate(): Promise<void> {
    if (this.pendingOperations.length === 0) {
      return;
    }

    const startTime = performance.now();
    const operationsToProcess = this.pendingOperations.splice(0, this.config.maxBatchSize);

    this.logger.logUserAction('dom_batch_update_start', undefined, {
      operationCount: operationsToProcess.length,
      maxBatchSize: this.config.maxBatchSize
    });

    try {
      // Sort operations by priority and visibility
      const sortedOperations = this.sortOperationsByPriority(operationsToProcess);

      // Execute operations
      for (const operation of sortedOperations) {
        await this.executeOperation(operation);
      }

      const duration = performance.now() - startTime;
      this.logger.logUserAction('dom_batch_update_complete', undefined, {
        operationCount: sortedOperations.length,
        duration,
        remainingOperations: this.pendingOperations.length
      });

      // Schedule next batch if there are remaining operations
      if (this.pendingOperations.length > 0) {
        this.scheduleBatchUpdate();
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      this.logger.logUserAction('dom_batch_update_error', undefined, {
        operationCount: operationsToProcess.length,
        duration,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private sortOperationsByPriority(operations: DOMUpdateOperation[]): DOMUpdateOperation[] {
    return operations.sort((a, b) => {
      // First sort by priority
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // Higher priority first
      }

      // Then by visibility if prioritizeVisible is enabled
      if (this.config.prioritizeVisible) {
        const aVisible = this.isElementVisible(a.element);
        const bVisible = this.isElementVisible(b.element);

        if (aVisible !== bVisible) {
          return aVisible ? -1 : 1; // Visible elements first
        }
      }

      // Finally by timestamp (older first)
      return a.timestamp - b.timestamp;
    });
  }

  private async executeOperation(operation: DOMUpdateOperation): Promise<void> {
    try {
      switch (operation.type) {
        case DOMUpdateType.CLASS_ADD:
          if (operation.data.className) {
            operation.element.classList.add(operation.data.className);
          }
          break;

        case DOMUpdateType.CLASS_REMOVE:
          if (operation.data.className) {
            operation.element.classList.remove(operation.data.className);
          }
          break;

        case DOMUpdateType.STYLE_SET:
          if (operation.data.property && operation.data.value !== undefined) {
            (operation.element as HTMLElement).style.setProperty(
              operation.data.property,
              operation.data.value
            );
          }
          break;

        case DOMUpdateType.ATTRIBUTE_SET:
          if (operation.data.attribute && operation.data.value !== undefined) {
            operation.element.setAttribute(operation.data.attribute, operation.data.value);
          }
          break;

        case DOMUpdateType.ATTRIBUTE_REMOVE:
          if (operation.data.attribute) {
            operation.element.removeAttribute(operation.data.attribute);
          }
          break;

        default:
          this.logger.logUserAction('dom_operation_unknown', undefined, {
            operationId: operation.id,
            type: operation.type
          });
          break;
      }

      // Update tracking info
      this.updateElementTrackingInfo(operation.element);
    } catch (error) {
      this.logger.logUserAction('dom_operation_error', undefined, {
        operationId: operation.id,
        type: operation.type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private updateElementTrackingInfo(element: Element): void {
    // Find tracking info for this element
    for (const info of this.trackedElements.values()) {
      if (info.element === element) {
        info.lastUpdated = Date.now();
        info.updateCount++;
        break;
      }
    }
  }

  private isElementVisible(element: Element): boolean {
    // Find tracking info for this element
    for (const info of this.trackedElements.values()) {
      if (info.element === element) {
        return info.isVisible;
      }
    }

    // Default to visible if not tracked
    return true;
  }

  private createIntersectionObserver(): IntersectionObserver | null {
    if (typeof IntersectionObserver === 'undefined') {
      return null;
    }

    return new IntersectionObserver((entries) => {
      for (const entry of entries) {
        // Find tracking info for this element
        for (const info of this.trackedElements.values()) {
          if (info.element === entry.target) {
            info.isVisible = entry.isIntersecting;
            break;
          }
        }
      }
    }, {
      threshold: 0.1 // Consider visible if at least 10% is visible
    });
  }

  private generateElementId(): string {
    return `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateOperationId(): string {
    return `op_${++this.operationIdCounter}_${Date.now()}`;
  }
}
