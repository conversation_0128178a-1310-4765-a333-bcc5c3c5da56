/**
 * Core Element Factory Implementation
 *
 * Handles core element creation and theme application functionality.
 * Follows Single Responsibility Principle by focusing on basic element operations.
 */

import { ThemeDefinition, ThemeOperation } from '../theme-types';
import { IThemeLogger, IPerformanceMonitor } from '../interfaces/core-interfaces';
import { ElementOptions, SemanticRole, IElementFactory } from './interfaces';
import { SemanticRoleHandler } from './semantic-roles';
import { ElementOptionsValidator } from './validation';

/**
 * Core element factory implementation
 * Handles basic element creation and theme application
 */
export class CoreElementFactory implements IElementFactory {
  private readonly roleHandler: SemanticRoleHandler;
  private readonly validator: ElementOptionsValidator;

  constructor(
    private currentTheme: ThemeDefinition | null,
    private readonly logger: IThemeLogger,
    private readonly performanceMonitor: IPerformanceMonitor
  ) {
    this.roleHandler = new SemanticRoleHandler();
    this.validator = new ElementOptionsValidator();
  }

  /**
   * Set the current theme
   */
  public setCurrentTheme(theme: ThemeDefinition | null): void {
    this.currentTheme = theme;
    this.roleHandler.clearCaches();
  }

  /**
   * Create a single themed element
   */
  public async createElement<T extends HTMLElement = HTMLElement>(options: ElementOptions<T>): Promise<T> {
    return this.performanceMonitor.measureOperation(
      ThemeOperation.ELEMENT_CREATE,
      async () => {
        this.logger.logOperationStart(ThemeOperation.ELEMENT_CREATE, {
          operation: ThemeOperation.ELEMENT_CREATE,
          themeId: this.currentTheme?.id,
          additionalData: {
            tagName: options.tagName,
            role: options.role
          }
        });

        try {
          // Validate options
          const validation = await this.validator.validateOptions(options);
          if (!validation.isValid) {
            throw new Error(`Invalid element options: ${validation.errors.join(', ')}`);
          }

          // Log warnings
          if (validation.warnings.length > 0) {
            this.logger.logUserAction('element_create_warnings', this.currentTheme?.id, {
              warnings: validation.warnings
            });
          }

          // Create the element
          const element = document.createElement(options.tagName) as T;

          // Apply theme role attribute for tracking
          element.setAttribute('data-theme-role', options.role);

          // Apply CSS classes
          if (options.className) {
            const classes = Array.isArray(options.className) ? options.className : [options.className];
            element.classList.add(...classes);
          }

          // Apply theme styling
          await this.applyThemeToElement(element, options.role, options.themeOverride);

          // Set attributes
          if (options.attributes) {
            for (const [key, value] of Object.entries(options.attributes)) {
              element.setAttribute(key, String(value));
            }
          }

          // Set ARIA attributes
          if (options.ariaAttributes) {
            for (const [key, value] of Object.entries(options.ariaAttributes)) {
              element.setAttribute(key, value);
            }
          }

          // Add role-specific ARIA attributes
          const roleAriaAttributes = this.roleHandler.getRoleAriaAttributes(options.role);
          for (const [key, value] of Object.entries(roleAriaAttributes)) {
            if (!element.hasAttribute(key)) {
              element.setAttribute(key, value);
            }
          }

          // Set text content
          if (options.textContent) {
            element.textContent = options.textContent;
          }

          // Set HTML content
          if (options.innerHTML) {
            element.innerHTML = options.innerHTML;
          }

          // Add event listeners
          if (options.eventListeners) {
            for (const [event, listener] of Object.entries(options.eventListeners)) {
              element.addEventListener(event, listener);
            }
          }

          // Add children
          if (options.children) {
            for (const child of options.children) {
              if (typeof child === 'string') {
                element.appendChild(document.createTextNode(child));
              } else {
                element.appendChild(child);
              }
            }
          }

          this.logger.logOperationComplete(
            ThemeOperation.ELEMENT_CREATE,
            true,
            {
              startTime: Date.now(),
              endTime: Date.now(),
              duration: 0,
              elementCount: 1,
              meetsBenchmark: true,
              benchmark: 100
            },
            {
              themeId: this.currentTheme?.id,
              additionalData: {
                tagName: options.tagName,
                role: options.role
              }
            }
          );

          return element;
        } catch (error) {
          this.logger.logUserAction('element_create_failed', this.currentTheme?.id, {
            error: error instanceof Error ? error.message : 'Unknown error',
            elementType: options.tagName,
            role: options.role
          });
          throw error;
        }
      }
    );
  }

  /**
   * Create multiple elements efficiently
   */
  public async createElements<T extends HTMLElement = HTMLElement>(optionsArray: ElementOptions<T>[]): Promise<T[]> {
    return this.performanceMonitor.measureOperation(
      ThemeOperation.ELEMENT_CREATE,
      async () => {
        this.logger.logOperationStart(ThemeOperation.ELEMENT_CREATE, {
          operation: ThemeOperation.ELEMENT_CREATE,
          themeId: this.currentTheme?.id,
          additionalData: {
            elementCount: optionsArray.length,
            batchOperation: true
          }
        });

        try {
          const elements: T[] = [];

          // Create elements in parallel for better performance
          const promises = optionsArray.map(options => this.createElement(options));
          const results = await Promise.all(promises);

          elements.push(...results);

          this.logger.logOperationComplete(
            ThemeOperation.ELEMENT_CREATE,
            true,
            {
              startTime: Date.now(),
              endTime: Date.now(),
              duration: 0,
              elementCount: elements.length,
              meetsBenchmark: true,
              benchmark: 100
            },
            {
              themeId: this.currentTheme?.id,
              additionalData: {
                elementCount: elements.length,
                batchOperation: true
              }
            }
          );

          return elements;
        } catch (error) {
          this.logger.logUserAction('element_create_batch_failed', this.currentTheme?.id, {
            error: error instanceof Error ? error.message : 'Unknown error',
            elementCount: optionsArray.length,
            batchOperation: true
          });
          throw error;
        }
      }
    );
  }

  /**
   * Apply theme to an existing element
   */
  public async applyThemeToElement(element: HTMLElement, role: SemanticRole, themeOverride?: Partial<ThemeDefinition>): Promise<void> {
    const theme = themeOverride ? { ...this.currentTheme, ...themeOverride } : this.currentTheme;

    if (!theme) {
      this.logger.logUserAction('theme_apply_skipped', undefined, {
        reason: 'No theme available',
        elementType: element.tagName,
        role
      });
      return;
    }

    // Get theme classes for the semantic role
    const themeClasses = this.roleHandler.getThemeClasses(role, theme as ThemeDefinition);
    element.classList.add(...themeClasses);

    // Apply CSS variables for the semantic role
    const cssVariables = this.roleHandler.getCSSVariables(role, theme as ThemeDefinition);
    for (const [property, value] of Object.entries(cssVariables)) {
      element.style.setProperty(property, value);
    }

    // Add theme identifier for debugging
    element.setAttribute('data-theme-id', theme.id || 'unknown');
    element.setAttribute('data-theme-version', theme.version || '1.0.0');
  }

  /**
   * Remove theme from an element
   */
  public async removeThemeFromElement(element: HTMLElement): Promise<void> {
    // Remove theme classes (classes starting with theme prefix)
    const classesToRemove = Array.from(element.classList).filter(cls =>
      cls.startsWith('theme-') || cls.startsWith('semantic-')
    );
    element.classList.remove(...classesToRemove);

    // Remove theme-related CSS variables
    const style = element.style;
    for (let i = style.length - 1; i >= 0; i--) {
      const property = style.item(i);
      if (property.startsWith('--theme-')) {
        style.removeProperty(property);
      }
    }

    // Remove theme attributes
    element.removeAttribute('data-theme-id');
    element.removeAttribute('data-theme-version');
    element.removeAttribute('data-theme-role');
  }

  /**
   * Update element theme when theme changes
   */
  public async updateElementTheme(element: HTMLElement, newTheme: ThemeDefinition): Promise<void> {
    const role = element.getAttribute('data-theme-role') as SemanticRole;
    if (!role) {
      this.logger.logUserAction('theme_update_skipped', newTheme.id, {
        reason: 'No semantic role found',
        elementType: element.tagName
      });
      return;
    }

    // Remove old theme
    await this.removeThemeFromElement(element);

    // Apply new theme
    await this.applyThemeToElement(element, role);
  }

  /**
   * Get theme classes for a semantic role
   */
  public getThemeClasses(role: SemanticRole, theme?: ThemeDefinition): string[] {
    const currentTheme = theme || this.currentTheme;
    if (!currentTheme) return [];

    return this.roleHandler.getThemeClasses(role, currentTheme);
  }

  /**
   * Get CSS variables for a semantic role
   */
  public getCSSVariables(role: SemanticRole, theme?: ThemeDefinition): Record<string, string> {
    const currentTheme = theme || this.currentTheme;
    if (!currentTheme) return {};

    return this.roleHandler.getCSSVariables(role, currentTheme);
  }

  /**
   * Validate element options
   */
  public async validateOptions<T extends HTMLElement>(options: ElementOptions<T>): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    return this.validator.validateOptions(options);
  }
}
