/**
 * Element Factory Interfaces and Types
 *
 * Defines all interfaces and types for the theme-aware element factory system.
 * Follows Interface Segregation Principle by providing focused, specific interfaces.
 */

import { ThemeDefinition } from '../theme-types';

/**
 * Semantic roles for theme-aware elements
 * Defines the semantic purpose of elements for proper theme application
 */
export enum SemanticRole {
  SURFACE = 'surface',
  BUTTON = 'button',
  INPUT = 'input',
  TEXT = 'text',
  HEADER = 'header',
  NAVIGATION = 'navigation',
  DIALOG = 'dialog',
  OVERLAY = 'overlay',
  CARD = 'card',
  LIST = 'list',
  MENU = 'menu'
}

/**
 * Configuration options for creating elements
 * @template T - The specific HTML element type being created
 */
export interface ElementOptions<T extends HTMLElement = HTMLElement> {
  /** HTML tag name */
  tagName: keyof HTMLElementTagNameMap;
  /** Semantic role for theme application */
  role: SemanticRole;
  /** CSS classes to apply */
  className?: string | string[];
  /** HTML attributes to set */
  attributes?: Record<string, string | number | boolean>;
  /** ARIA attributes for accessibility */
  ariaAttributes?: Record<string, string>;
  /** Event listeners to attach */
  eventListeners?: Record<string, (event: Event) => void>;
  /** Text content */
  textContent?: string;
  /** HTML content */
  innerHTML?: string;
  /** Child elements */
  children?: (HTMLElement | string)[];
  /** Theme override for this element */
  themeOverride?: Partial<ThemeDefinition>;
  /** Element-specific properties (for type safety) */
  elementSpecific?: Partial<T>;
}

/**
 * Core element factory interface
 * Responsible for basic element creation and theme application
 */
export interface IElementFactory {
  /**
   * Create a single themed element
   */
  createElement<T extends HTMLElement = HTMLElement>(options: ElementOptions<T>): Promise<T>;

  /**
   * Create multiple themed elements efficiently
   */
  createElements<T extends HTMLElement = HTMLElement>(optionsArray: ElementOptions<T>[]): Promise<T[]>;

  /**
   * Apply theme to an existing element
   */
  applyThemeToElement(element: HTMLElement, role: SemanticRole, themeOverride?: Partial<ThemeDefinition>): Promise<void>;

  /**
   * Remove theme from an element
   */
  removeThemeFromElement(element: HTMLElement): Promise<void>;

  /**
   * Update element theme when theme changes
   */
  updateElementTheme(element: HTMLElement, newTheme: ThemeDefinition): Promise<void>;

  /**
   * Get theme classes for a semantic role
   */
  getThemeClasses(role: SemanticRole, theme?: ThemeDefinition): string[];

  /**
   * Get CSS variables for a semantic role
   */
  getCSSVariables(role: SemanticRole, theme?: ThemeDefinition): Record<string, string>;

  /**
   * Validate element options
   */
  validateOptions<T extends HTMLElement>(options: ElementOptions<T>): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>;
}

/**
 * UI pattern factory interface
 * Responsible for creating common UI patterns with pre-configured options
 */
export interface IUIPatternFactory {
  /**
   * Create a themed dialog element
   */
  createDialog(options: {
    title?: string;
    content?: string | HTMLElement;
    actions?: Array<{
      label: string;
      action: () => void;
      variant?: 'primary' | 'secondary' | 'danger';
    }>;
    modal?: boolean;
    closable?: boolean;
    size?: 'small' | 'medium' | 'large';
  }): Promise<HTMLDialogElement>;

  /**
   * Create a themed button element
   */
  createButton(options: {
    label: string;
    variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    icon?: string;
    onClick?: () => void;
  }): Promise<HTMLButtonElement>;

  /**
   * Create a themed input element
   */
  createInput(options: {
    type?: string;
    placeholder?: string;
    label?: string;
    value?: string;
    required?: boolean;
    disabled?: boolean;
    validation?: {
      pattern?: string;
      minLength?: number;
      maxLength?: number;
      customValidator?: (value: string) => boolean;
    };
    onChange?: (value: string) => void;
  }): Promise<HTMLInputElement>;

  /**
   * Create a themed panel/card element
   */
  createPanel(options: {
    title?: string;
    content?: string | HTMLElement;
    collapsible?: boolean;
    collapsed?: boolean;
    variant?: 'default' | 'elevated' | 'outlined';
    padding?: 'none' | 'small' | 'medium' | 'large';
  }): Promise<HTMLDivElement>;
}

/**
 * Element composition interface
 * Responsible for combining and organizing elements
 */
export interface IElementComposer {
  /**
   * Compose multiple elements into a container with layout options
   */
  composeElements(
    container: HTMLElement,
    elements: HTMLElement[],
    layout?: 'flex' | 'grid' | 'stack'
  ): Promise<HTMLElement>;

  /**
   * Create element hierarchy with theme inheritance
   */
  createHierarchy(
    rootOptions: ElementOptions,
    childrenOptions: ElementOptions[]
  ): Promise<HTMLElement>;

  /**
   * Clone element while preserving theme state
   */
  cloneElement(
    element: HTMLElement,
    preserveTheme?: boolean
  ): Promise<HTMLElement>;
}
