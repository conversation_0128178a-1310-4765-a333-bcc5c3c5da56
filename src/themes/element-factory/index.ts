/**
 * Theme-Aware Element Factory
 *
 * Main entry point for the modular element factory system.
 * Combines all factory components into a unified interface while maintaining SOLID principles.
 */

import { ThemeDefinition } from '../theme-types';
import { IThemeLogger, IPerformanceMonitor } from '../interfaces/core-interfaces';

// Import interfaces
export * from './interfaces';

// Import individual components
import { CoreElementFactory } from './core-factory';
import { UIPatternFactory } from './ui-patterns';
import { ElementComposer } from './composition';
import { SemanticRoleHandler } from './semantic-roles';
import { ElementOptionsValidator } from './validation';

// Re-export components for direct use
export { CoreElementFactory, UIPatternFactory, ElementComposer, SemanticRoleHandler, ElementOptionsValidator };

// Import interfaces for the main factory
import {
  ElementOptions,
  SemanticRole,
  IElementFactory,
  IUIPatternFactory,
  IElementComposer
} from './interfaces';

/**
 * Main theme-aware element factory
 * Combines all factory capabilities into a single, easy-to-use interface
 * Follows Facade pattern to provide a simplified interface to the complex subsystem
 */
export class ThemeElementFactory implements IElementFactory, IUIPatternFactory, IElementComposer {
  private readonly coreFactory: CoreElementFactory;
  private readonly uiPatternFactory: UIPatternFactory;
  private readonly elementComposer: ElementComposer;
  private readonly roleHandler: SemanticRoleHandler;

  constructor(
    private currentTheme: ThemeDefinition | null,
    logger: IThemeLogger,
    performanceMonitor: IPerformanceMonitor
  ) {
    // Initialize core components
    this.coreFactory = new CoreElementFactory(currentTheme, logger, performanceMonitor);
    this.uiPatternFactory = new UIPatternFactory(this.coreFactory);
    this.elementComposer = new ElementComposer(this.coreFactory);
    this.roleHandler = new SemanticRoleHandler();
  }

  /**
   * Update the current theme for all factory components
   */
  public setCurrentTheme(theme: ThemeDefinition | null): void {
    this.currentTheme = theme;
    this.coreFactory.setCurrentTheme(theme);
    this.roleHandler.clearCaches();
  }

  /**
   * Get the current theme
   */
  public getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  // ===== Core Element Factory Methods =====

  /**
   * Create a single themed element
   */
  public async createElement<T extends HTMLElement = HTMLElement>(options: ElementOptions<T>): Promise<T> {
    return this.coreFactory.createElement(options);
  }

  /**
   * Create multiple themed elements efficiently
   */
  public async createElements<T extends HTMLElement = HTMLElement>(optionsArray: ElementOptions<T>[]): Promise<T[]> {
    return this.coreFactory.createElements(optionsArray);
  }

  /**
   * Apply theme to an existing element
   */
  public async applyThemeToElement(element: HTMLElement, role: SemanticRole, themeOverride?: Partial<ThemeDefinition>): Promise<void> {
    return this.coreFactory.applyThemeToElement(element, role, themeOverride);
  }

  /**
   * Remove theme from an element
   */
  public async removeThemeFromElement(element: HTMLElement): Promise<void> {
    return this.coreFactory.removeThemeFromElement(element);
  }

  /**
   * Update element theme when theme changes
   */
  public async updateElementTheme(element: HTMLElement, newTheme: ThemeDefinition): Promise<void> {
    return this.coreFactory.updateElementTheme(element, newTheme);
  }

  /**
   * Get theme classes for a semantic role
   */
  public getThemeClasses(role: SemanticRole, theme?: ThemeDefinition): string[] {
    return this.coreFactory.getThemeClasses(role, theme);
  }

  /**
   * Get CSS variables for a semantic role
   */
  public getCSSVariables(role: SemanticRole, theme?: ThemeDefinition): Record<string, string> {
    return this.coreFactory.getCSSVariables(role, theme);
  }

  /**
   * Validate element options
   */
  public async validateOptions<T extends HTMLElement>(options: ElementOptions<T>): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    return this.coreFactory.validateOptions(options);
  }

  // ===== UI Pattern Factory Methods =====

  /**
   * Create a themed dialog element
   */
  public async createDialog(options: {
    title?: string;
    content?: string | HTMLElement;
    actions?: Array<{
      label: string;
      action: () => void;
      variant?: 'primary' | 'secondary' | 'danger';
    }>;
    modal?: boolean;
    closable?: boolean;
    size?: 'small' | 'medium' | 'large';
  }): Promise<HTMLDialogElement> {
    return this.uiPatternFactory.createDialog(options);
  }

  /**
   * Create a themed button element
   */
  public async createButton(options: {
    label: string;
    variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    icon?: string;
    onClick?: () => void;
  }): Promise<HTMLButtonElement> {
    return this.uiPatternFactory.createButton(options);
  }

  /**
   * Create a themed input element
   */
  public async createInput(options: {
    type?: string;
    placeholder?: string;
    label?: string;
    value?: string;
    required?: boolean;
    disabled?: boolean;
    validation?: {
      pattern?: string;
      minLength?: number;
      maxLength?: number;
      customValidator?: (value: string) => boolean;
    };
    onChange?: (value: string) => void;
  }): Promise<HTMLInputElement> {
    return this.uiPatternFactory.createInput(options);
  }

  /**
   * Create a themed panel/card element
   */
  public async createPanel(options: {
    title?: string;
    content?: string | HTMLElement;
    collapsible?: boolean;
    collapsed?: boolean;
    variant?: 'default' | 'elevated' | 'outlined';
    padding?: 'none' | 'small' | 'medium' | 'large';
  }): Promise<HTMLDivElement> {
    return this.uiPatternFactory.createPanel(options);
  }

  // ===== Element Composer Methods =====

  /**
   * Compose multiple elements into a container with layout options
   */
  public async composeElements(
    container: HTMLElement,
    elements: HTMLElement[],
    layout?: 'flex' | 'grid' | 'stack'
  ): Promise<HTMLElement> {
    return this.elementComposer.composeElements(container, elements, layout);
  }

  /**
   * Create element hierarchy with theme inheritance
   */
  public async createHierarchy(
    rootOptions: ElementOptions,
    childrenOptions: ElementOptions[]
  ): Promise<HTMLElement> {
    return this.elementComposer.createHierarchy(rootOptions, childrenOptions);
  }

  /**
   * Clone element while preserving theme state
   */
  public async cloneElement(
    element: HTMLElement,
    preserveTheme?: boolean
  ): Promise<HTMLElement> {
    return this.elementComposer.cloneElement(element, preserveTheme);
  }

  // ===== Additional Utility Methods =====

  /**
   * Get cache statistics for debugging
   */
  public getCacheStats(): {
    themeClassCacheSize: number;
    cssVariableCacheSize: number;
  } {
    return this.roleHandler.getCacheStats();
  }

  /**
   * Clear all caches
   */
  public clearCaches(): void {
    this.roleHandler.clearCaches();
  }

  /**
   * Get all available semantic roles
   */
  public getAllSemanticRoles(): SemanticRole[] {
    return this.roleHandler.getAllRoles();
  }

  /**
   * Validate semantic role
   */
  public isValidSemanticRole(role: string): role is SemanticRole {
    return this.roleHandler.isValidRole(role);
  }

  /**
   * Get role-specific ARIA attributes
   */
  public getRoleAriaAttributes(role: SemanticRole): Record<string, string> {
    return this.roleHandler.getRoleAriaAttributes(role);
  }

  /**
   * Batch update themes for multiple elements
   */
  public async updateMultipleElementThemes(elements: HTMLElement[], newTheme: ThemeDefinition): Promise<void> {
    const promises = elements.map(element => this.updateElementTheme(element, newTheme));
    await Promise.all(promises);
  }

  /**
   * Create a themed element tree from a configuration object
   */
  public async createElementTree(config: {
    root: ElementOptions;
    children?: Array<{
      element: ElementOptions;
      children?: unknown;
    }>;
  }): Promise<HTMLElement> {
    const root = await this.createElement(config.root);

    if (config.children) {
      for (const childConfig of config.children) {
        const child = await this.createElementTree({
          root: childConfig.element,
          children: childConfig.children as any
        });
        root.appendChild(child);
      }
    }

    return root;
  }
}
