/**
 * Element Options Validation
 * 
 * Handles validation of element creation options.
 * Follows Single Responsibility Principle by focusing solely on validation logic.
 */

import { ElementOptions, SemanticRole } from './interfaces';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Element options validator
 * Provides comprehensive validation for element creation options
 */
export class ElementOptionsValidator {
  /**
   * Validate element options
   */
  public async validateOptions<T extends HTMLElement>(options: ElementOptions<T>): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required fields
    this.validateRequiredFields(options, errors);

    // Validate tag name
    this.validateTagName(options, errors);

    // Validate semantic role
    this.validateSemanticRole(options, errors);

    // Validate className
    this.validateClassName(options, errors);

    // Validate attributes
    this.validateAttributes(options, errors);

    // Validate ARIA attributes
    this.validateAriaAttributes(options, errors);

    // Validate event listeners
    this.validateEventListeners(options, errors);

    // Validate children
    this.validateChildren(options, errors, warnings);

    // Performance warnings
    this.addPerformanceWarnings(options, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate required fields
   */
  private validateRequiredFields<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (!options.tagName) {
      errors.push('tagName is required');
    }

    if (!options.role) {
      errors.push('role is required');
    }
  }

  /**
   * Validate tag name
   */
  private validateTagName<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (options.tagName && typeof options.tagName !== 'string') {
      errors.push('tagName must be a string');
    }

    // Check if it's a valid HTML tag name
    if (options.tagName && typeof options.tagName === 'string') {
      const validTags = [
        'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'button', 'input', 'textarea', 'select', 'option',
        'form', 'label', 'fieldset', 'legend',
        'ul', 'ol', 'li', 'dl', 'dt', 'dd',
        'table', 'thead', 'tbody', 'tfoot', 'tr', 'th', 'td',
        'header', 'footer', 'nav', 'main', 'section', 'article', 'aside',
        'dialog', 'details', 'summary',
        'a', 'img', 'video', 'audio', 'canvas', 'svg',
        'strong', 'em', 'code', 'pre', 'blockquote',
        'time', 'mark', 'small', 'sub', 'sup'
      ];

      if (!validTags.includes(options.tagName as string)) {
        errors.push(`Unsupported tag name: ${options.tagName}`);
      }
    }
  }

  /**
   * Validate semantic role
   */
  private validateSemanticRole<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (options.role && !Object.values(SemanticRole).includes(options.role)) {
      errors.push(`Invalid semantic role: ${options.role}`);
    }
  }

  /**
   * Validate className
   */
  private validateClassName<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (options.className) {
      if (typeof options.className !== 'string' && !Array.isArray(options.className)) {
        errors.push('className must be a string or array of strings');
      }

      if (Array.isArray(options.className)) {
        for (let i = 0; i < options.className.length; i++) {
          if (typeof options.className[i] !== 'string') {
            errors.push(`className[${i}] must be a string`);
          }
        }
      }
    }
  }

  /**
   * Validate attributes
   */
  private validateAttributes<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (options.attributes) {
      if (typeof options.attributes !== 'object' || Array.isArray(options.attributes)) {
        errors.push('attributes must be an object');
        return;
      }

      // Validate attribute values
      for (const [key, value] of Object.entries(options.attributes)) {
        if (typeof key !== 'string') {
          errors.push('Attribute keys must be strings');
        }

        if (typeof value !== 'string' && typeof value !== 'number' && typeof value !== 'boolean') {
          errors.push(`Attribute '${key}' value must be string, number, or boolean`);
        }

        // Check for dangerous attributes
        const dangerousAttributes = ['onclick', 'onload', 'onerror', 'javascript:'];
        if (dangerousAttributes.some(dangerous => key.toLowerCase().includes(dangerous))) {
          errors.push(`Potentially dangerous attribute: ${key}`);
        }
      }
    }
  }

  /**
   * Validate ARIA attributes
   */
  private validateAriaAttributes<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (options.ariaAttributes) {
      if (typeof options.ariaAttributes !== 'object' || Array.isArray(options.ariaAttributes)) {
        errors.push('ariaAttributes must be an object');
        return;
      }

      for (const [key, value] of Object.entries(options.ariaAttributes)) {
        if (!key.startsWith('aria-')) {
          errors.push(`ARIA attribute '${key}' must start with 'aria-'`);
        }

        if (typeof value !== 'string') {
          errors.push(`ARIA attribute '${key}' value must be a string`);
        }
      }
    }
  }

  /**
   * Validate event listeners
   */
  private validateEventListeners<T extends HTMLElement>(options: ElementOptions<T>, errors: string[]): void {
    if (options.eventListeners) {
      if (typeof options.eventListeners !== 'object' || Array.isArray(options.eventListeners)) {
        errors.push('eventListeners must be an object');
        return;
      }

      for (const [event, listener] of Object.entries(options.eventListeners)) {
        if (typeof listener !== 'function') {
          errors.push(`Event listener for '${event}' must be a function`);
        }

        // Validate event names
        const validEvents = [
          'click', 'dblclick', 'mousedown', 'mouseup', 'mouseover', 'mouseout', 'mousemove',
          'keydown', 'keyup', 'keypress',
          'focus', 'blur', 'focusin', 'focusout',
          'input', 'change', 'submit', 'reset',
          'load', 'unload', 'resize', 'scroll',
          'touchstart', 'touchend', 'touchmove', 'touchcancel'
        ];

        if (!validEvents.includes(event)) {
          errors.push(`Unsupported event type: ${event}`);
        }
      }
    }
  }

  /**
   * Validate children
   */
  private validateChildren<T extends HTMLElement>(
    options: ElementOptions<T>, 
    errors: string[], 
    warnings: string[]
  ): void {
    if (options.children) {
      if (!Array.isArray(options.children)) {
        errors.push('children must be an array');
        return;
      }

      for (let i = 0; i < options.children.length; i++) {
        const child = options.children[i];
        if (typeof child !== 'string' && !(child instanceof HTMLElement)) {
          errors.push(`Child at index ${i} must be a string or HTMLElement`);
        }
      }

      // Performance warning for large number of children
      if (options.children.length > 50) {
        warnings.push('Large number of children may impact performance');
      }
    }
  }

  /**
   * Add performance warnings
   */
  private addPerformanceWarnings<T extends HTMLElement>(options: ElementOptions<T>, warnings: string[]): void {
    if (options.innerHTML && options.children) {
      warnings.push('Both innerHTML and children specified; children will be appended after innerHTML');
    }

    if (options.innerHTML && options.textContent) {
      warnings.push('Both innerHTML and textContent specified; innerHTML will take precedence');
    }

    if (options.eventListeners && Object.keys(options.eventListeners).length > 10) {
      warnings.push('Large number of event listeners may impact performance');
    }
  }

  /**
   * Validate theme override
   */
  public validateThemeOverride(themeOverride: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (themeOverride && typeof themeOverride !== 'object') {
      errors.push('themeOverride must be an object');
    }

    if (themeOverride && themeOverride.colors) {
      if (typeof themeOverride.colors !== 'object') {
        errors.push('themeOverride.colors must be an object');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
