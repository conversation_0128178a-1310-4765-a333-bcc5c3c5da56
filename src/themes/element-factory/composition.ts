/**
 * Element Composition Implementation
 *
 * Handles element composition and organization functionality.
 * Follows Single Responsibility Principle by focusing on element relationships and layout.
 */

import { IElementComposer, ElementOptions, SemanticRole } from './interfaces';
import { CoreElementFactory } from './core-factory';

/**
 * Element composer implementation
 * Handles combining and organizing elements with proper theme inheritance
 */
export class ElementComposer implements IElementComposer {
  constructor(private readonly coreFactory: CoreElementFactory) {}

  /**
   * Compose multiple elements into a container with layout options
   */
  public async composeElements(
    container: HTMLElement,
    elements: HTMLElement[],
    layout: 'flex' | 'grid' | 'stack' = 'flex'
  ): Promise<HTMLElement> {
    // Apply layout classes
    container.classList.add('theme-layout', `layout-${layout}`);

    // Add layout-specific attributes
    switch (layout) {
      case 'flex':
        container.style.display = 'flex';
        container.style.flexWrap = 'wrap';
        container.style.gap = 'var(--theme-spacing-medium, 1rem)';
        break;
      case 'grid':
        container.style.display = 'grid';
        container.style.gridTemplateColumns = `repeat(auto-fit, minmax(200px, 1fr))`;
        container.style.gap = 'var(--theme-spacing-medium, 1rem)';
        break;
      case 'stack':
        container.style.display = 'flex';
        container.style.flexDirection = 'column';
        container.style.gap = 'var(--theme-spacing-small, 0.5rem)';
        break;
    }

    // Append all elements
    for (const element of elements) {
      container.appendChild(element);
    }

    return container;
  }

  /**
   * Create element hierarchy with theme inheritance
   */
  public async createHierarchy(
    rootOptions: ElementOptions,
    childrenOptions: ElementOptions[]
  ): Promise<HTMLElement> {
    // Create root element
    const root = await this.coreFactory.createElement(rootOptions);

    // Create children and append to root
    for (const childOptions of childrenOptions) {
      // Inherit theme from parent if not specified
      const childOptionsWithTheme = {
        ...childOptions,
        themeOverride: childOptions.themeOverride || rootOptions.themeOverride
      };

      const child = await this.coreFactory.createElement(childOptionsWithTheme);
      root.appendChild(child);
    }

    return root;
  }

  /**
   * Clone element while preserving theme state
   */
  public async cloneElement(
    element: HTMLElement,
    preserveTheme: boolean = true
  ): Promise<HTMLElement> {
    // Clone the element
    const cloned = element.cloneNode(true) as HTMLElement;

    if (preserveTheme) {
      // Preserve theme-related attributes and classes
      const themeId = element.getAttribute('data-theme-id');
      const themeRole = element.getAttribute('data-theme-role');
      const themeVersion = element.getAttribute('data-theme-version');

      if (themeId) cloned.setAttribute('data-theme-id', themeId);
      if (themeRole) cloned.setAttribute('data-theme-role', themeRole);
      if (themeVersion) cloned.setAttribute('data-theme-version', themeVersion);

      // Copy theme-related CSS classes
      const themeClasses = Array.from(element.classList).filter(cls =>
        cls.startsWith('theme-') || cls.startsWith('semantic-')
      );
      cloned.classList.add(...themeClasses);

      // Copy theme-related CSS variables
      const computedStyle = window.getComputedStyle(element);
      for (let i = 0; i < computedStyle.length; i++) {
        const property = computedStyle.item(i);
        if (property.startsWith('--theme-')) {
          const value = computedStyle.getPropertyValue(property);
          cloned.style.setProperty(property, value);
        }
      }
    } else {
      // Remove theme-related attributes and classes
      cloned.removeAttribute('data-theme-id');
      cloned.removeAttribute('data-theme-role');
      cloned.removeAttribute('data-theme-version');

      const themeClasses = Array.from(cloned.classList).filter(cls =>
        cls.startsWith('theme-') || cls.startsWith('semantic-')
      );
      cloned.classList.remove(...themeClasses);

      // Remove theme-related CSS variables
      const style = cloned.style;
      for (let i = style.length - 1; i >= 0; i--) {
        const property = style.item(i);
        if (property.startsWith('--theme-')) {
          style.removeProperty(property);
        }
      }
    }

    return cloned;
  }

  /**
   * Create a responsive layout container
   */
  public async createResponsiveLayout(options: {
    breakpoints?: {
      mobile?: string;
      tablet?: string;
      desktop?: string;
    };
    columns?: {
      mobile?: number;
      tablet?: number;
      desktop?: number;
    };
    gap?: string;
  }): Promise<HTMLElement> {
    const {
      breakpoints = {
        mobile: '768px',
        tablet: '1024px',
        desktop: '1200px'
      },
      columns = {
        mobile: 1,
        tablet: 2,
        desktop: 3
      },
      gap = 'var(--theme-spacing-medium, 1rem)'
    } = options;

    const container = await this.coreFactory.createElement({
      tagName: 'div',
      role: SemanticRole.SURFACE,
      className: 'responsive-layout'
    });

    // Apply responsive grid styles
    container.style.display = 'grid';
    container.style.gap = gap;
    container.style.gridTemplateColumns = `repeat(${columns.mobile}, 1fr)`;

    // Add media query styles
    const style = document.createElement('style');
    style.textContent = `
      @media (min-width: ${breakpoints.mobile}) {
        .responsive-layout {
          grid-template-columns: repeat(${columns.tablet}, 1fr);
        }
      }
      @media (min-width: ${breakpoints.desktop}) {
        .responsive-layout {
          grid-template-columns: repeat(${columns.desktop}, 1fr);
        }
      }
    `;

    // Append style to head if not already present
    if (!document.querySelector('style[data-responsive-layout]')) {
      style.setAttribute('data-responsive-layout', 'true');
      document.head.appendChild(style);
    }

    return container;
  }

  /**
   * Create a flex layout with common patterns
   */
  public async createFlexLayout(options: {
    direction?: 'row' | 'column';
    justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';
    align?: 'start' | 'center' | 'end' | 'stretch';
    wrap?: boolean;
    gap?: string;
  }): Promise<HTMLElement> {
    const {
      direction = 'row',
      justify = 'start',
      align = 'stretch',
      wrap = false,
      gap = 'var(--theme-spacing-medium, 1rem)'
    } = options;

    const container = await this.coreFactory.createElement({
      tagName: 'div',
      role: SemanticRole.SURFACE,
      className: 'flex-layout'
    });

    // Apply flex styles
    container.style.display = 'flex';
    container.style.flexDirection = direction;
    container.style.justifyContent = justify === 'start' ? 'flex-start' :
                                   justify === 'end' ? 'flex-end' : justify;
    container.style.alignItems = align === 'start' ? 'flex-start' :
                                align === 'end' ? 'flex-end' : align;
    container.style.flexWrap = wrap ? 'wrap' : 'nowrap';
    container.style.gap = gap;

    return container;
  }

  /**
   * Create a masonry layout for dynamic content
   */
  public async createMasonryLayout(options: {
    columns?: number;
    gap?: string;
    minItemWidth?: string;
  }): Promise<HTMLElement> {
    const {
      gap = 'var(--theme-spacing-medium, 1rem)',
      minItemWidth = '200px'
    } = options;

    const container = await this.coreFactory.createElement({
      tagName: 'div',
      role: SemanticRole.SURFACE,
      className: 'masonry-layout'
    });

    // Apply masonry styles using CSS Grid
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`;
    container.style.gap = gap;
    container.style.gridAutoRows = 'max-content';

    return container;
  }

  /**
   * Create a card grid layout
   */
  public async createCardGrid(options: {
    minCardWidth?: string;
    maxCardWidth?: string;
    gap?: string;
    aspectRatio?: string;
  }): Promise<HTMLElement> {
    const {
      minCardWidth = '250px',
      maxCardWidth = '350px',
      gap = 'var(--theme-spacing-medium, 1rem)',
      aspectRatio = 'auto'
    } = options;

    const container = await this.coreFactory.createElement({
      tagName: 'div',
      role: SemanticRole.SURFACE,
      className: 'card-grid'
    });

    // Apply card grid styles
    container.style.display = 'grid';
    container.style.gridTemplateColumns = `repeat(auto-fit, minmax(${minCardWidth}, ${maxCardWidth}))`;
    container.style.gap = gap;
    container.style.justifyContent = 'center';

    if (aspectRatio !== 'auto') {
      container.style.gridAutoRows = `minmax(calc(${minCardWidth} * ${aspectRatio}), auto)`;
    }

    return container;
  }
}
