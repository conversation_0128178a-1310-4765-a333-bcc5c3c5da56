/**
 * UI Pattern Factory Implementation
 *
 * Handles creation of common UI patterns with pre-configured options.
 * Follows Single Responsibility Principle by focusing on high-level UI components.
 */

import { IUIPatternFactory, SemanticRole } from './interfaces';
import { CoreElementFactory } from './core-factory';

/**
 * UI pattern factory implementation
 * Creates common UI patterns with proper accessibility and theming
 */
export class UIPatternFactory implements IUIPatternFactory {
  constructor(private readonly coreFactory: CoreElementFactory) {}

  /**
   * Create a themed dialog element with proper ARIA attributes and accessibility
   */
  public async createDialog(options: {
    title?: string;
    content?: string | HTMLElement;
    actions?: Array<{
      label: string;
      action: () => void;
      variant?: 'primary' | 'secondary' | 'danger';
    }>;
    modal?: boolean;
    closable?: boolean;
    size?: 'small' | 'medium' | 'large';
  }): Promise<HTMLDialogElement> {
    const {
      title,
      content,
      actions = [],
      modal = true,
      closable = true,
      size = 'medium'
    } = options;

    // Create the dialog element
    const dialog = await this.coreFactory.createElement<HTMLDialogElement>({
      tagName: 'dialog',
      role: SemanticRole.DIALOG,
      className: [`dialog-${size}`, 'theme-dialog'],
      ariaAttributes: {
        'aria-modal': modal ? 'true' : 'false',
        ...(title && { 'aria-labelledby': 'dialog-title' }),
        ...(content && { 'aria-describedby': 'dialog-content' })
      }
    });

    // Create dialog container
    const container = await this.coreFactory.createElement({
      tagName: 'div',
      role: SemanticRole.SURFACE,
      className: 'dialog-container'
    });

    // Create header if title is provided
    if (title) {
      const header = await this.coreFactory.createElement({
        tagName: 'header',
        role: SemanticRole.HEADER,
        className: 'dialog-header'
      });

      const titleElement = await this.coreFactory.createElement({
        tagName: 'h2',
        role: SemanticRole.HEADER,
        className: 'dialog-title',
        attributes: { id: 'dialog-title' },
        textContent: title
      });

      header.appendChild(titleElement);

      // Add close button if closable
      if (closable) {
        const closeButton = await this.createButton({
          label: '×',
          variant: 'ghost',
          size: 'small',
          onClick: () => dialog.close()
        });
        closeButton.classList.add('dialog-close');
        closeButton.setAttribute('aria-label', 'Close dialog');
        header.appendChild(closeButton);
      }

      container.appendChild(header);
    }

    // Create content area
    if (content) {
      const contentElement = await this.coreFactory.createElement({
        tagName: 'div',
        role: SemanticRole.TEXT,
        className: 'dialog-content',
        attributes: { id: 'dialog-content' }
      });

      if (typeof content === 'string') {
        contentElement.textContent = content;
      } else {
        contentElement.appendChild(content);
      }

      container.appendChild(contentElement);
    }

    // Create actions footer
    if (actions.length > 0) {
      const footer = await this.coreFactory.createElement({
        tagName: 'footer',
        role: SemanticRole.NAVIGATION,
        className: 'dialog-actions'
      });

      for (const actionConfig of actions) {
        const button = await this.createButton({
          label: actionConfig.label,
          variant: actionConfig.variant || 'secondary',
          onClick: actionConfig.action
        });
        footer.appendChild(button);
      }

      container.appendChild(footer);
    }

    dialog.appendChild(container);

    // Add keyboard event handling for accessibility
    dialog.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && closable) {
        dialog.close();
      }
    });

    // Focus management
    dialog.addEventListener('show', () => {
      const firstFocusable = dialog.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])') as HTMLElement;
      if (firstFocusable) {
        firstFocusable.focus();
      }
    });

    return dialog;
  }

  /**
   * Create a themed button element with variants and proper accessibility
   */
  public async createButton(options: {
    label: string;
    variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    icon?: string;
    onClick?: () => void;
  }): Promise<HTMLButtonElement> {
    const {
      label,
      variant = 'secondary',
      size = 'medium',
      disabled = false,
      icon,
      onClick
    } = options;

    // Create the button element
    const button = await this.coreFactory.createElement<HTMLButtonElement>({
      tagName: 'button',
      role: SemanticRole.BUTTON,
      className: [
        'theme-button',
        `button-${variant}`,
        `button-${size}`,
        ...(icon ? ['button-with-icon'] : [])
      ],
      attributes: {
        type: 'button',
        disabled
      },
      eventListeners: {
        ...(onClick && { click: onClick })
      }
    });

    // Create button content container
    const content = await this.coreFactory.createElement({
      tagName: 'span',
      role: SemanticRole.TEXT,
      className: 'button-content'
    });

    // Add icon if provided
    if (icon) {
      const iconElement = await this.coreFactory.createElement({
        tagName: 'span',
        role: SemanticRole.TEXT,
        className: 'button-icon',
        ariaAttributes: { 'aria-hidden': 'true' },
        textContent: icon
      });
      content.appendChild(iconElement);
    }

    // Add label
    const labelElement = await this.coreFactory.createElement({
      tagName: 'span',
      role: SemanticRole.TEXT,
      className: 'button-label',
      textContent: label
    });
    content.appendChild(labelElement);

    button.appendChild(content);

    // Add ripple effect for interactive feedback
    button.addEventListener('click', (event) => {
      if (disabled) return;

      const ripple = document.createElement('span');
      ripple.className = 'button-ripple';

      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - size / 2;
      const y = event.clientY - rect.top - size / 2;

      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';

      button.appendChild(ripple);

      // Remove ripple after animation
      setTimeout(() => {
        if (ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
      }, 300);
    });

    return button;
  }

  /**
   * Create a themed input element with validation and proper labeling
   */
  public async createInput(options: {
    type?: string;
    placeholder?: string;
    label?: string;
    value?: string;
    required?: boolean;
    disabled?: boolean;
    validation?: {
      pattern?: string;
      minLength?: number;
      maxLength?: number;
      customValidator?: (value: string) => boolean;
    };
    onChange?: (value: string) => void;
  }): Promise<HTMLInputElement> {
    const {
      type = 'text',
      placeholder,
      label,
      value = '',
      required = false,
      disabled = false,
      validation,
      onChange
    } = options;

    // Generate unique ID for input-label association
    const inputId = `input-${Math.random().toString(36).substring(2, 11)}`;

    // Create input wrapper
    const wrapper = await this.coreFactory.createElement({
      tagName: 'div',
      role: SemanticRole.INPUT,
      className: 'input-wrapper'
    });

    // Create label if provided
    if (label) {
      const labelElement = await this.coreFactory.createElement({
        tagName: 'label',
        role: SemanticRole.TEXT,
        className: 'input-label',
        attributes: { for: inputId },
        textContent: label + (required ? ' *' : '')
      });
      wrapper.appendChild(labelElement);
    }

    // Create the input element
    const input = await this.coreFactory.createElement<HTMLInputElement>({
      tagName: 'input',
      role: SemanticRole.INPUT,
      className: 'theme-input',
      attributes: {
        id: inputId,
        type,
        placeholder: placeholder || '',
        value,
        required,
        disabled,
        ...(validation?.pattern && { pattern: validation.pattern }),
        ...(validation?.minLength && { minlength: validation.minLength }),
        ...(validation?.maxLength && { maxlength: validation.maxLength })
      },
      ariaAttributes: {
        'aria-required': required ? 'true' : 'false',
        'aria-invalid': 'false',
        ...(label && { 'aria-labelledby': `${inputId}-label` })
      },
      eventListeners: {
        input: (event) => {
          const target = event.target as HTMLInputElement;
          const currentValue = target.value;

          // Run validation
          let isValid = true;
          let errorMessage = '';

          if (required && !currentValue.trim()) {
            isValid = false;
            errorMessage = 'This field is required';
          } else if (validation?.customValidator && !validation.customValidator(currentValue)) {
            isValid = false;
            errorMessage = 'Invalid input';
          }

          // Update ARIA attributes
          target.setAttribute('aria-invalid', isValid ? 'false' : 'true');

          // Update visual state
          target.classList.toggle('input-error', !isValid);

          // Show/hide error message
          const existingError = wrapper.querySelector('.input-error-message');
          if (!isValid && !existingError) {
            const errorElement = document.createElement('span');
            errorElement.className = 'input-error-message';
            errorElement.textContent = errorMessage;
            errorElement.setAttribute('role', 'alert');
            wrapper.appendChild(errorElement);
          } else if (isValid && existingError) {
            existingError.remove();
          }

          // Call onChange callback
          if (onChange) {
            onChange(currentValue);
          }
        },
        focus: () => {
          wrapper.classList.add('input-focused');
        },
        blur: () => {
          wrapper.classList.remove('input-focused');
        }
      }
    });

    wrapper.appendChild(input);

    // Return the input element (not the wrapper) to match the interface
    return input;
  }

  /**
   * Create a themed panel/card element with collapsible functionality
   */
  public async createPanel(options: {
    title?: string;
    content?: string | HTMLElement;
    collapsible?: boolean;
    collapsed?: boolean;
    variant?: 'default' | 'elevated' | 'outlined';
    padding?: 'none' | 'small' | 'medium' | 'large';
  }): Promise<HTMLDivElement> {
    const {
      title,
      content,
      collapsible = false,
      collapsed = false,
      variant = 'default',
      padding = 'medium'
    } = options;

    // Create the panel element
    const panel = await this.coreFactory.createElement<HTMLDivElement>({
      tagName: 'div',
      role: SemanticRole.SURFACE,
      className: [
        'theme-panel',
        `panel-${variant}`,
        `panel-padding-${padding}`,
        ...(collapsible ? ['panel-collapsible'] : []),
        ...(collapsed ? ['panel-collapsed'] : [])
      ]
    });

    // Create header if title is provided
    if (title) {
      const header = await this.coreFactory.createElement({
        tagName: 'header',
        role: SemanticRole.HEADER,
        className: 'panel-header'
      });

      if (collapsible) {
        // Create collapsible header button
        const headerButton = await this.coreFactory.createElement({
          tagName: 'button',
          role: SemanticRole.BUTTON,
          className: 'panel-header-button',
          attributes: {
            type: 'button',
            'aria-expanded': collapsed ? 'false' : 'true'
          },
          eventListeners: {
            click: () => {
              const isCollapsed = panel.classList.contains('panel-collapsed');
              panel.classList.toggle('panel-collapsed');
              headerButton.setAttribute('aria-expanded', isCollapsed ? 'true' : 'false');

              // Animate content visibility
              const contentElement = panel.querySelector('.panel-content') as HTMLElement;
              if (contentElement) {
                if (isCollapsed) {
                  contentElement.style.display = 'block';
                  contentElement.style.maxHeight = contentElement.scrollHeight + 'px';
                } else {
                  contentElement.style.maxHeight = '0';
                  setTimeout(() => {
                    contentElement.style.display = 'none';
                  }, 300);
                }
              }
            }
          }
        });

        // Add title and collapse indicator
        const titleElement = await this.coreFactory.createElement({
          tagName: 'h3',
          role: SemanticRole.HEADER,
          className: 'panel-title',
          textContent: title
        });

        const indicator = await this.coreFactory.createElement({
          tagName: 'span',
          role: SemanticRole.TEXT,
          className: 'panel-collapse-indicator',
          ariaAttributes: { 'aria-hidden': 'true' },
          textContent: collapsed ? '▶' : '▼'
        });

        headerButton.appendChild(titleElement);
        headerButton.appendChild(indicator);
        header.appendChild(headerButton);
      } else {
        // Create simple title
        const titleElement = await this.coreFactory.createElement({
          tagName: 'h3',
          role: SemanticRole.HEADER,
          className: 'panel-title',
          textContent: title
        });
        header.appendChild(titleElement);
      }

      panel.appendChild(header);
    }

    // Create content area
    if (content) {
      const contentElement = await this.coreFactory.createElement({
        tagName: 'div',
        role: SemanticRole.TEXT,
        className: 'panel-content',
        ...(collapsed && {
          attributes: { style: 'display: none; max-height: 0;' }
        })
      });

      if (typeof content === 'string') {
        contentElement.textContent = content;
      } else {
        contentElement.appendChild(content);
      }

      panel.appendChild(contentElement);
    }

    return panel;
  }
}
