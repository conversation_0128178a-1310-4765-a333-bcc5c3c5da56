/**
 * Semantic Role Handling
 * 
 * Manages semantic role system and theme class/CSS variable generation.
 * Follows Single Responsibility Principle by focusing on role-based theme mapping.
 */

import { ThemeDefinition } from '../theme-types';
import { SemanticRole } from './interfaces';

/**
 * Semantic role handler for theme-aware elements
 * Manages the mapping between semantic roles and theme styling
 */
export class SemanticRoleHandler {
  private readonly themeClassCache = new Map<string, string[]>();
  private readonly cssVariableCache = new Map<string, Record<string, string>>();

  /**
   * Get theme classes for a semantic role
   */
  public getThemeClasses(role: SemanticRole, theme: ThemeDefinition): string[] {
    const cacheKey = `${theme.id}-${role}`;
    if (this.themeClassCache.has(cacheKey)) {
      return this.themeClassCache.get(cacheKey)!;
    }

    const classes: string[] = [
      `theme-${theme.id}`,
      `semantic-${role}`,
      `theme-role-${role}`
    ];

    // Add variant classes based on role
    switch (role) {
      case SemanticRole.SURFACE:
        classes.push('theme-surface', 'theme-elevated');
        break;
      case SemanticRole.BUTTON:
        classes.push('theme-interactive', 'theme-focusable');
        break;
      case SemanticRole.INPUT:
        classes.push('theme-input', 'theme-focusable');
        break;
      case SemanticRole.DIALOG:
        classes.push('theme-overlay', 'theme-modal');
        break;
      case SemanticRole.OVERLAY:
        classes.push('theme-overlay', 'theme-floating');
        break;
      case SemanticRole.CARD:
        classes.push('theme-surface', 'theme-card');
        break;
      case SemanticRole.LIST:
        classes.push('theme-list', 'theme-structured');
        break;
      case SemanticRole.MENU:
        classes.push('theme-menu', 'theme-interactive');
        break;
      case SemanticRole.NAVIGATION:
        classes.push('theme-navigation', 'theme-interactive');
        break;
      case SemanticRole.HEADER:
        classes.push('theme-header', 'theme-prominent');
        break;
      default:
        classes.push('theme-content');
        break;
    }

    this.themeClassCache.set(cacheKey, classes);
    return classes;
  }

  /**
   * Get CSS variables for a semantic role
   */
  public getCSSVariables(role: SemanticRole, theme: ThemeDefinition): Record<string, string> {
    const cacheKey = `${theme.id}-${role}`;
    if (this.cssVariableCache.has(cacheKey)) {
      return this.cssVariableCache.get(cacheKey)!;
    }

    const variables: Record<string, string> = {};
    const prefix = theme.cssVariablePrefix || 'theme';

    // Base theme variables
    variables[`--${prefix}-primary`] = theme.colors.primary;
    variables[`--${prefix}-secondary`] = theme.colors.secondary;
    variables[`--${prefix}-background`] = theme.colors.background;
    variables[`--${prefix}-surface`] = theme.colors.surface;
    variables[`--${prefix}-text`] = theme.colors.text;
    variables[`--${prefix}-border`] = theme.colors.border;

    // Role-specific variables
    switch (role) {
      case SemanticRole.SURFACE:
        variables[`--${prefix}-surface-bg`] = theme.colors.surface;
        variables[`--${prefix}-surface-border`] = theme.colors.border;
        break;
      case SemanticRole.BUTTON:
        variables[`--${prefix}-button-bg`] = theme.colors.primary;
        variables[`--${prefix}-button-text`] = theme.colors.background;
        variables[`--${prefix}-button-hover-bg`] = theme.stateColors.hover.background;
        break;
      case SemanticRole.INPUT:
        variables[`--${prefix}-input-bg`] = theme.colors.background;
        variables[`--${prefix}-input-border`] = theme.colors.border;
        variables[`--${prefix}-input-focus-border`] = theme.stateColors.focus.border;
        break;
      case SemanticRole.DIALOG:
        variables[`--${prefix}-dialog-bg`] = theme.colors.surface;
        variables[`--${prefix}-dialog-shadow`] = theme.pluginColors.palette.shadow;
        break;
      case SemanticRole.OVERLAY:
        variables[`--${prefix}-overlay-bg`] = theme.colors.surface;
        variables[`--${prefix}-overlay-shadow`] = theme.pluginColors.palette.shadow;
        break;
      case SemanticRole.CARD:
        variables[`--${prefix}-card-bg`] = theme.colors.surface;
        variables[`--${prefix}-card-border`] = theme.colors.border;
        break;
      case SemanticRole.LIST:
        variables[`--${prefix}-list-bg`] = theme.colors.background;
        variables[`--${prefix}-list-border`] = theme.colors.border;
        break;
      case SemanticRole.MENU:
        variables[`--${prefix}-menu-bg`] = theme.colors.surface;
        variables[`--${prefix}-menu-border`] = theme.colors.border;
        variables[`--${prefix}-menu-hover-bg`] = theme.stateColors.hover.background;
        break;
      case SemanticRole.NAVIGATION:
        variables[`--${prefix}-nav-bg`] = theme.colors.surface;
        variables[`--${prefix}-nav-border`] = theme.colors.border;
        variables[`--${prefix}-nav-active-bg`] = theme.stateColors.active.background;
        break;
      case SemanticRole.HEADER:
        variables[`--${prefix}-header-bg`] = theme.colors.surface;
        variables[`--${prefix}-header-text`] = theme.colors.text;
        variables[`--${prefix}-header-border`] = theme.colors.border;
        break;
    }

    this.cssVariableCache.set(cacheKey, variables);
    return variables;
  }

  /**
   * Clear caches when theme changes
   */
  public clearCaches(): void {
    this.themeClassCache.clear();
    this.cssVariableCache.clear();
  }

  /**
   * Get cache statistics for debugging
   */
  public getCacheStats(): {
    themeClassCacheSize: number;
    cssVariableCacheSize: number;
  } {
    return {
      themeClassCacheSize: this.themeClassCache.size,
      cssVariableCacheSize: this.cssVariableCache.size
    };
  }

  /**
   * Validate semantic role
   */
  public isValidRole(role: string): role is SemanticRole {
    return Object.values(SemanticRole).includes(role as SemanticRole);
  }

  /**
   * Get all available semantic roles
   */
  public getAllRoles(): SemanticRole[] {
    return Object.values(SemanticRole);
  }

  /**
   * Get role-specific ARIA attributes
   */
  public getRoleAriaAttributes(role: SemanticRole): Record<string, string> {
    const attributes: Record<string, string> = {};

    switch (role) {
      case SemanticRole.BUTTON:
        attributes['role'] = 'button';
        break;
      case SemanticRole.DIALOG:
        attributes['role'] = 'dialog';
        break;
      case SemanticRole.NAVIGATION:
        attributes['role'] = 'navigation';
        break;
      case SemanticRole.LIST:
        attributes['role'] = 'list';
        break;
      case SemanticRole.MENU:
        attributes['role'] = 'menu';
        break;
      case SemanticRole.HEADER:
        attributes['role'] = 'banner';
        break;
      // Other roles use semantic HTML tags and don't need explicit role attributes
    }

    return attributes;
  }
}
