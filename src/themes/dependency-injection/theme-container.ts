/**
 * Theme System Dependency Injection Container
 * Implements the Dependency Inversion Principle (DIP) by providing a centralized
 * container for managing dependencies and their lifecycles.
 */

import { IThemeDependencyContainer } from '../interfaces/core-interfaces';

/**
 * Service registration information
 */
interface ServiceRegistration<T = unknown> {
  factory: () => T;
  singleton: boolean;
  instance?: T;
}

/**
 * Theme dependency injection container implementation
 * Follows SOLID principles with proper error handling and lifecycle management
 */
export class ThemeDependencyContainer implements IThemeDependencyContainer {
  private services = new Map<string, ServiceRegistration>();
  private resolutionStack = new Set<string>();

  /**
   * Register a transient service with the container
   * A new instance is created each time the service is resolved
   */
  public register<T>(token: string, factory: () => T): void {
    if (this.services.has(token)) {
      throw new Error(`Service with token '${token}' is already registered`);
    }

    this.services.set(token, {
      factory,
      singleton: false
    });
  }

  /**
   * Register a singleton service with the container
   * The same instance is returned each time the service is resolved
   */
  public registerSingleton<T>(token: string, factory: () => T): void {
    if (this.services.has(token)) {
      throw new Error(`Service with token '${token}' is already registered`);
    }

    this.services.set(token, {
      factory,
      singleton: true
    });
  }

  /**
   * Resolve a service from the container
   * Handles circular dependency detection and proper error reporting
   */
  public resolve<T>(token: string): T {
    // Check for circular dependencies
    if (this.resolutionStack.has(token)) {
      const stackArray = Array.from(this.resolutionStack);
      throw new Error(
        `Circular dependency detected: ${stackArray.join(' -> ')} -> ${token}`
      );
    }

    const registration = this.services.get(token);
    if (!registration) {
      throw new Error(`Service with token '${token}' is not registered`);
    }

    // For singletons, return existing instance if available
    if (registration.singleton && registration.instance) {
      return registration.instance as T;
    }

    // Add to resolution stack for circular dependency detection
    this.resolutionStack.add(token);

    try {
      const instance = registration.factory() as T;

      // Store singleton instance
      if (registration.singleton) {
        registration.instance = instance;
      }

      return instance;
    } catch (error) {
      throw new Error(
        `Failed to resolve service '${token}': ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      // Remove from resolution stack
      this.resolutionStack.delete(token);
    }
  }

  /**
   * Check if a service is registered
   */
  public isRegistered(token: string): boolean {
    return this.services.has(token);
  }

  /**
   * Clear all service registrations
   * Useful for testing and cleanup
   */
  public clear(): void {
    this.services.clear();
    this.resolutionStack.clear();
  }

  /**
   * Get all registered service tokens
   * Useful for debugging and introspection
   */
  public getRegisteredTokens(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Replace an existing service registration
   * Useful for testing and mocking
   */
  public replace<T>(token: string, factory: () => T, singleton = false): void {
    if (!this.services.has(token)) {
      throw new Error(`Service with token '${token}' is not registered and cannot be replaced`);
    }

    this.services.set(token, {
      factory,
      singleton,
      instance: undefined // Clear any existing singleton instance
    });
  }

  /**
   * Create a scoped container that inherits from this container
   * Useful for creating isolated scopes with overrides
   */
  public createScope(): ThemeDependencyContainer {
    const scopedContainer = new ThemeDependencyContainer();
    
    // Copy all registrations to the scoped container
    this.services.forEach((registration, token) => {
      scopedContainer.services.set(token, {
        factory: registration.factory,
        singleton: registration.singleton,
        instance: registration.instance // Share singleton instances
      });
    });

    return scopedContainer;
  }

  /**
   * Dispose of all singleton instances that implement IDisposable
   * Useful for cleanup and resource management
   */
  public dispose(): void {
    this.services.forEach((registration) => {
      if (registration.singleton && registration.instance) {
        const instance = registration.instance as unknown;
        
        // Check if instance has a dispose method
        if (typeof instance === 'object' && instance !== null && 'dispose' in instance) {
          const disposable = instance as { dispose(): void };
          if (typeof disposable.dispose === 'function') {
            try {
              disposable.dispose();
            } catch (error) {
              console.warn('Error disposing service instance:', error);
            }
          }
        }
      }
    });

    this.clear();
  }

  /**
   * Get service registration information for debugging
   */
  public getServiceInfo(token: string): {
    registered: boolean;
    singleton: boolean;
    hasInstance: boolean;
  } | null {
    const registration = this.services.get(token);
    if (!registration) {
      return null;
    }

    return {
      registered: true,
      singleton: registration.singleton,
      hasInstance: registration.singleton && !!registration.instance
    };
  }

  /**
   * Validate all service registrations by attempting to resolve them
   * Useful for startup validation and detecting configuration issues
   */
  public validateRegistrations(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const tokens = this.getRegisteredTokens();

    for (const token of tokens) {
      try {
        this.resolve(token);
      } catch (error) {
        errors.push(`Failed to resolve '${token}': ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

/**
 * Global theme dependency container instance
 * This provides a default container for the theme system
 */
export const themeDependencyContainer = new ThemeDependencyContainer();
