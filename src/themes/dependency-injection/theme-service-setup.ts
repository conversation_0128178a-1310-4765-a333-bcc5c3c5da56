/**
 * Theme System Service Setup
 * Configures the dependency injection container with all theme system services.
 * Follows Dependency Inversion Principle (DIP) by registering interfaces and their implementations.
 */

import {
  IThemeErrorFactory,
  IRecoveryStrategyManager,
  IErrorHistoryManager,
  IThemeErrorHandler,
  IThemeValidator,
  IThemeConfigManager,
  ICSSVariableInjector,
  IThemeStorage,
  SERVICE_TOKENS
} from '../interfaces/core-interfaces';

import { ThemeDependencyContainer } from './theme-container';
import { ThemeErrorFactory } from '../error-handling/theme-error-factory';
import { RecoveryStrategyManager } from '../error-handling/recovery-strategy-manager';
import { ErrorHistoryManager } from '../error-handling/error-history-manager';
import { ThemeErrorHandlerImpl } from '../error-handling/theme-error-handler-impl';
import { createBasicRecoveryStrategies } from '../strategies/basic-recovery-strategies';
import { ThemeValidator } from '../theme-validator';
import { ThemeConfigManager, CSSVariableInjector, LocalStorageThemeStorage } from '../theme-config';

/**
 * Configuration options for theme services
 */
export interface ThemeServiceConfig {
  /** Maximum number of errors to keep in history */
  maxErrorHistory?: number;
  /** Default theme for fallback recovery */
  defaultFallbackTheme?: 'light' | 'dark';
  /** Whether to register basic recovery strategies */
  registerBasicStrategies?: boolean;
  /** Custom recovery strategies to register */
  customStrategies?: Array<() => import('../interfaces/core-interfaces').IRecoveryStrategy>;
}

/**
 * Sets up the theme system dependency injection container
 */
export function setupThemeServices(
  container: ThemeDependencyContainer,
  config: ThemeServiceConfig = {}
): void {
  const {
    maxErrorHistory = 100,
    registerBasicStrategies = true,
    customStrategies = []
  } = config;

  // Register Error Factory as singleton
  container.registerSingleton<IThemeErrorFactory>(
    SERVICE_TOKENS.ERROR_FACTORY,
    () => new ThemeErrorFactory()
  );

  // Register Error History Manager as singleton
  container.registerSingleton<IErrorHistoryManager>(
    SERVICE_TOKENS.ERROR_HISTORY_MANAGER,
    () => {
      const historyManager = new ErrorHistoryManager();
      historyManager.setMaxHistorySize(maxErrorHistory);
      return historyManager;
    }
  );

  // Register Recovery Strategy Manager as singleton
  container.registerSingleton<IRecoveryStrategyManager>(
    SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER,
    () => {
      const recoveryManager = new RecoveryStrategyManager();

      // Register basic strategies if enabled
      if (registerBasicStrategies) {
        const basicStrategies = createBasicRecoveryStrategies();
        basicStrategies.forEach(strategy => {
          recoveryManager.registerStrategy(strategy);
        });
      }

      // Register custom strategies
      customStrategies.forEach(strategyFactory => {
        const strategy = strategyFactory();
        recoveryManager.registerStrategy(strategy);
      });

      return recoveryManager;
    }
  );

  // Register Theme Error Handler as singleton
  container.registerSingleton<IThemeErrorHandler>(
    SERVICE_TOKENS.ERROR_HANDLER,
    () => {
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);
      const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);

      return new ThemeErrorHandlerImpl(errorFactory, recoveryManager, historyManager);
    }
  );

  // Register Theme Validator as singleton
  container.registerSingleton<IThemeValidator>(
    SERVICE_TOKENS.VALIDATOR,
    () => new ThemeValidator()
  );

  // Register Theme Storage as singleton
  container.registerSingleton<IThemeStorage>(
    SERVICE_TOKENS.THEME_STORAGE,
    () => {
      // Use window.localStorage if available, otherwise create a mock for tests
      const storage = (typeof window !== 'undefined' && window.localStorage)
        ? window.localStorage
        : {
            getItem: () => null,
            setItem: () => {},
            removeItem: () => {},
            clear: () => {},
            length: 0,
            key: () => null
          } as Storage;
      return new LocalStorageThemeStorage(storage);
    }
  );

  // Register CSS Variable Injector as singleton
  container.registerSingleton<ICSSVariableInjector>(
    SERVICE_TOKENS.CSS_VARIABLE_INJECTOR,
    () => new CSSVariableInjector()
  );

  // Register Theme Config Manager as singleton
  container.registerSingleton<IThemeConfigManager>(
    SERVICE_TOKENS.CONFIG_MANAGER,
    () => {
      const storage = container.resolve<IThemeStorage>(SERVICE_TOKENS.THEME_STORAGE);
      const validator = container.resolve<IThemeValidator>(SERVICE_TOKENS.VALIDATOR);

      return new ThemeConfigManager(storage, validator);
    }
  );
}

/**
 * Creates a fully configured theme dependency container
 */
export function createConfiguredThemeContainer(config: ThemeServiceConfig = {}): ThemeDependencyContainer {
  const container = new ThemeDependencyContainer();
  setupThemeServices(container, config);
  return container;
}

/**
 * Validates that all required services are properly registered and can be resolved
 */
export function validateThemeServices(container: ThemeDependencyContainer): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check that all required services are registered
  const requiredServices = [
    SERVICE_TOKENS.ERROR_FACTORY,
    SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER,
    SERVICE_TOKENS.ERROR_HISTORY_MANAGER,
    SERVICE_TOKENS.ERROR_HANDLER,
    SERVICE_TOKENS.VALIDATOR,
    SERVICE_TOKENS.THEME_STORAGE,
    SERVICE_TOKENS.CSS_VARIABLE_INJECTOR,
    SERVICE_TOKENS.CONFIG_MANAGER
  ];

  for (const serviceToken of requiredServices) {
    if (!container.isRegistered(serviceToken)) {
      errors.push(`Required service '${serviceToken}' is not registered`);
    }
  }

  // Try to resolve all services to check for configuration issues
  for (const serviceToken of requiredServices) {
    if (container.isRegistered(serviceToken)) {
      try {
        container.resolve(serviceToken);
      } catch (error) {
        errors.push(`Failed to resolve service '${serviceToken}': ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  // Check recovery strategies
  try {
    const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);
    if ('getRegisteredStrategyNames' in recoveryManager) {
      const strategies = (recoveryManager as any).getRegisteredStrategyNames();
      if (strategies.length === 0) {
        warnings.push('No recovery strategies are registered');
      } else if (strategies.length < 3) {
        warnings.push(`Only ${strategies.length} recovery strategies registered, consider adding more for better error handling`);
      }
    }
  } catch (error) {
    // Error already captured above
  }

  // Check error history configuration
  try {
    const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);
    if ('getMaxHistorySize' in historyManager) {
      const maxSize = (historyManager as any).getMaxHistorySize();
      if (maxSize < 10) {
        warnings.push(`Error history size is very small (${maxSize}), consider increasing for better debugging`);
      }
    }
  } catch (error) {
    // Error already captured above
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Gets diagnostic information about the theme services
 */
export function getThemeServicesDiagnostics(container: ThemeDependencyContainer): {
  registeredServices: string[];
  serviceInfo: Record<string, {
    registered: boolean;
    singleton: boolean;
    hasInstance: boolean;
  }>;
  recoveryStrategies: string[];
  errorHistorySize: number;
  containerValidation: ReturnType<typeof validateThemeServices>;
} {
  const registeredServices = container.getRegisteredTokens();
  const serviceInfo: Record<string, any> = {};

  // Get info for each service
  for (const token of registeredServices) {
    serviceInfo[token] = container.getServiceInfo(token);
  }

  // Get recovery strategies info
  let recoveryStrategies: string[] = [];
  try {
    const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);
    if ('getRegisteredStrategyNames' in recoveryManager) {
      recoveryStrategies = (recoveryManager as any).getRegisteredStrategyNames();
    }
  } catch (error) {
    // Ignore resolution errors for diagnostics
  }

  // Get error history size
  let errorHistorySize = 0;
  try {
    const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);
    if ('getMaxHistorySize' in historyManager) {
      errorHistorySize = (historyManager as any).getMaxHistorySize();
    }
  } catch (error) {
    // Ignore resolution errors for diagnostics
  }

  return {
    registeredServices,
    serviceInfo,
    recoveryStrategies,
    errorHistorySize,
    containerValidation: validateThemeServices(container)
  };
}

/**
 * Resets the theme services container to a clean state
 */
export function resetThemeServices(container: ThemeDependencyContainer, config: ThemeServiceConfig = {}): void {
  container.clear();
  setupThemeServices(container, config);
}

/**
 * Creates a test container with mock services for testing
 */
export function createTestThemeContainer(): ThemeDependencyContainer {
  const container = new ThemeDependencyContainer();

  // Register minimal test services
  setupThemeServices(container, {
    maxErrorHistory: 10,
    registerBasicStrategies: false, // Don't register strategies that might have side effects in tests
    customStrategies: []
  });

  return container;
}
