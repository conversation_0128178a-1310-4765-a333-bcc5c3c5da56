/**
 * Theme Validation System
 *
 * Provides comprehensive theme validation with color format checking,
 * contrast ratio validation, and accessibility compliance verification.
 *
 * SOLID Compliance:
 * - Single Responsibility: Focused on theme validation only
 * - Open/Closed: Extensible through validation rule composition
 * - Liskov Substitution: Implements IThemeValidator interface
 * - Interface Segregation: Focused validation interface
 * - Dependency Inversion: Depends on logger and error handler abstractions
 */

import { ThemeDefinition, ThemeOperation } from './theme-types';
import { IThemeValidator, IThemeLogger, IThemeErrorHandler } from './interfaces/core-interfaces';

/**
 * Color format validation patterns
 */
const COLOR_PATTERNS = {
  HEX: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  RGB: /^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/,
  RGBA: /^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(0|1|0?\.\d+)\s*\)$/,
  HSL: /^hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)$/,
  HSLA: /^hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*(0|1|0?\.\d+)\s*\)$/,
  CSS_VAR: /^var\(--[\w-]+\)$/
};

/**
 * WCAG contrast ratio thresholds
 */
const CONTRAST_THRESHOLDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5
};

/**
 * Theme Validator Implementation
 * Validates theme definitions for correctness, accessibility, and completeness
 */
export class ThemeValidator implements IThemeValidator {
  constructor(
    private readonly logger?: IThemeLogger,
    private readonly errorHandler?: IThemeErrorHandler
  ) {}

  /**
   * Wrap async operations with error handling
   */
  private async wrapAsync<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    context?: { themeId?: string; additionalData?: Record<string, unknown> }
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      this.logger?.logUserAction('operation_failed', context?.themeId, {
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context?.additionalData
      });

      if (this.errorHandler) {
        const handled = await this.errorHandler.wrapAsync(fn, operation, context);
        if (handled !== null) {
          return handled;
        }
      }

      throw error;
    }
  }

  /**
   * Validate a complete theme definition
   */
  public async validateTheme(theme: ThemeDefinition): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    return this.wrapAsync(async () => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate basic structure
      this.validateBasicStructure(theme, errors);

      // Validate colors
      await this.validateColors(theme, errors, warnings);

      // Validate state colors
      await this.validateStateColors(theme, errors, warnings);

      // Validate plugin colors
      await this.validatePluginColors(theme, errors, warnings);

      // Validate animation config
      this.validateAnimationConfig(theme, errors, warnings);

      // Validate accessibility
      await this.validateAccessibility(theme, errors, warnings);

      const isValid = errors.length === 0;

      this.logValidation(theme.id, isValid, errors, warnings);

      return { isValid, errors, warnings };
    }, ThemeOperation.THEME_VALIDATE, { themeId: theme.id }) ||
    { isValid: false, errors: ['Validation failed'], warnings: [] };
  }

  /**
   * Validate color format (hex, rgb, hsl, css variables)
   */
  public validateColorFormat(color: string): boolean {
    if (!color || typeof color !== 'string') {
      return false;
    }

    const trimmedColor = color.trim();

    return Object.values(COLOR_PATTERNS).some(pattern => pattern.test(trimmedColor));
  }

  /**
   * Validate contrast ratios for accessibility compliance
   */
  public async validateContrastRatio(foreground: string, background: string): Promise<{
    ratio: number;
    meetsWCAG_AA: boolean;
    meetsWCAG_AAA: boolean;
  }> {
    return this.wrapAsync(async () => {
      const fgLuminance = this.calculateLuminance(foreground);
      const bgLuminance = this.calculateLuminance(background);

      const ratio = this.calculateContrastRatio(fgLuminance, bgLuminance);

      return {
        ratio,
        meetsWCAG_AA: ratio >= CONTRAST_THRESHOLDS.AA_NORMAL,
        meetsWCAG_AAA: ratio >= CONTRAST_THRESHOLDS.AAA_NORMAL
      };
    }, ThemeOperation.THEME_VALIDATE, {
      additionalData: { operation: 'validateContrastRatio', foreground, background }
    }) || { ratio: 0, meetsWCAG_AA: false, meetsWCAG_AAA: false };
  }

  /**
   * Log theme validation results
   */
  public logValidation(
    themeId: string,
    isValid: boolean,
    errors?: string[],
    warnings?: string[]
  ): void {
    this.logger?.logUserAction('theme_validation_complete', themeId, {
      isValid,
      errorCount: errors?.length || 0,
      warningCount: warnings?.length || 0,
      errors: errors?.slice(0, 5), // Log first 5 errors to avoid spam
      warnings: warnings?.slice(0, 5) // Log first 5 warnings
    });
  }

  /**
   * Validate basic theme structure
   */
  private validateBasicStructure(theme: ThemeDefinition, errors: string[]): void {
    if (!theme.id || typeof theme.id !== 'string') {
      errors.push('Theme must have a valid id string');
    }

    if (!theme.name || typeof theme.name !== 'string') {
      errors.push('Theme must have a valid name string');
    }

    if (!theme.version || typeof theme.version !== 'string') {
      errors.push('Theme must have a valid version string');
    }

    if (!theme.colors || typeof theme.colors !== 'object') {
      errors.push('Theme must have a colors object');
    }

    if (!theme.stateColors || typeof theme.stateColors !== 'object') {
      errors.push('Theme must have a stateColors object');
    }

    if (!theme.pluginColors || typeof theme.pluginColors !== 'object') {
      errors.push('Theme must have a pluginColors object');
    }

    if (!theme.animation || typeof theme.animation !== 'object') {
      errors.push('Theme must have an animation object');
    }
  }

  /**
   * Validate core colors
   */
  private async validateColors(theme: ThemeDefinition, errors: string[], _warnings: string[]): Promise<void> {
    if (!theme.colors) return;

    const requiredColors = ['text', 'background', 'surface', 'border', 'primary', 'secondary', 'success', 'warning', 'error', 'info'];

    for (const colorName of requiredColors) {
      const color = theme.colors[colorName as keyof typeof theme.colors];

      if (!color) {
        errors.push(`Missing required color: ${colorName}`);
        continue;
      }

      if (!this.validateColorFormat(color)) {
        errors.push(`Invalid color format for ${colorName}: ${color}`);
      }
    }
  }

  /**
   * Validate state colors
   */
  private async validateStateColors(theme: ThemeDefinition, errors: string[], _warnings: string[]): Promise<void> {
    if (!theme.stateColors) return;

    const requiredStates = ['hover', 'focus', 'active', 'disabled'];

    for (const state of requiredStates) {
      const stateColors = theme.stateColors[state as keyof typeof theme.stateColors];

      if (!stateColors) {
        errors.push(`Missing state colors for: ${state}`);
        continue;
      }

      // Define required properties per state type
      let requiredProperties: string[];
      if (state === 'focus') {
        requiredProperties = ['background', 'border']; // Focus doesn't require text, but has ring
      } else {
        requiredProperties = ['background', 'border', 'text'];
      }

      for (const property of requiredProperties) {
        const color = stateColors[property as keyof typeof stateColors];

        if (!color) {
          errors.push(`Missing ${property} color for ${state} state`);
          continue;
        }

        if (!this.validateColorFormat(color)) {
          errors.push(`Invalid color format for ${state}.${property}: ${color}`);
        }
      }

      // Special validation for focus ring
      if (state === 'focus' && 'ring' in stateColors && stateColors.ring && !this.validateColorFormat(stateColors.ring)) {
        errors.push(`Invalid color format for focus.ring: ${stateColors.ring}`);
      }
    }
  }

  /**
   * Validate plugin colors
   */
  private async validatePluginColors(theme: ThemeDefinition, errors: string[], _warnings: string[]): Promise<void> {
    if (!theme.pluginColors) return;

    // Validate each plugin's color configuration
    Object.entries(theme.pluginColors).forEach(([pluginName, colors]) => {
      if (!colors || typeof colors !== 'object') {
        errors.push(`Invalid plugin colors for ${pluginName}`);
        return;
      }

      Object.entries(colors).forEach(([property, value]) => {
        if (Array.isArray(value)) {
          // Handle array values (like chart data colors)
          value.forEach((color, index) => {
            if (!this.validateColorFormat(color)) {
              errors.push(`Invalid color format for ${pluginName}.${property}[${index}]: ${color}`);
            }
          });
        } else if (typeof value === 'string') {
          if (!this.validateColorFormat(value)) {
            errors.push(`Invalid color format for ${pluginName}.${property}: ${value}`);
          }
        }
      });
    });
  }

  /**
   * Validate animation configuration
   */
  private validateAnimationConfig(theme: ThemeDefinition, errors: string[], warnings: string[]): void {
    if (!theme.animation) return;

    if (typeof theme.animation.duration !== 'number' || theme.animation.duration < 0) {
      errors.push('Animation duration must be a non-negative number');
    }

    if (!theme.animation.easing || typeof theme.animation.easing !== 'string') {
      errors.push('Animation easing must be a valid string');
    }

    if (typeof theme.animation.respectReducedMotion !== 'boolean') {
      warnings.push('Animation respectReducedMotion should be a boolean');
    }

    // Warn about very long animations
    if (theme.animation.duration > 1000) {
      warnings.push('Animation duration is very long (>1000ms), consider shorter duration for better UX');
    }
  }

  /**
   * Validate accessibility requirements
   */
  private async validateAccessibility(theme: ThemeDefinition, errors: string[], warnings: string[]): Promise<void> {
    if (!theme.colors) return;

    // Check critical contrast ratios
    const criticalPairs = [
      { fg: theme.colors.text, bg: theme.colors.background, name: 'text/background' },
      { fg: theme.colors.text, bg: theme.colors.surface, name: 'text/surface' },
      { fg: theme.colors.primary, bg: theme.colors.background, name: 'primary/background' }
    ];

    for (const pair of criticalPairs) {
      try {
        const contrast = await this.validateContrastRatio(pair.fg, pair.bg);

        if (!contrast.meetsWCAG_AA) {
          errors.push(`Insufficient contrast ratio for ${pair.name}: ${contrast.ratio.toFixed(2)} (minimum: ${CONTRAST_THRESHOLDS.AA_NORMAL})`);
        } else if (!contrast.meetsWCAG_AAA) {
          warnings.push(`Contrast ratio for ${pair.name} meets WCAG AA but not AAA: ${contrast.ratio.toFixed(2)}`);
        }
      } catch (error) {
        warnings.push(`Could not validate contrast ratio for ${pair.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  /**
   * Calculate relative luminance of a color
   */
  private calculateLuminance(color: string): number {
    // This is a simplified implementation
    // In a real implementation, you'd want to use a proper color parsing library
    const rgb = this.parseColorToRGB(color);
    if (!rgb) return 0;

    const [r, g, b] = rgb.map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /**
   * Calculate contrast ratio between two luminance values
   */
  private calculateContrastRatio(luminance1: number, luminance2: number): number {
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Parse color string to RGB values
   */
  private parseColorToRGB(color: string): [number, number, number] | null {
    // Simplified color parsing - in production, use a proper color library
    const hex = color.match(COLOR_PATTERNS.HEX);
    if (hex) {
      const hexValue = hex[1];
      if (hexValue.length === 3) {
        return [
          parseInt(hexValue[0] + hexValue[0], 16),
          parseInt(hexValue[1] + hexValue[1], 16),
          parseInt(hexValue[2] + hexValue[2], 16)
        ];
      } else {
        return [
          parseInt(hexValue.substring(0, 2), 16),
          parseInt(hexValue.substring(2, 4), 16),
          parseInt(hexValue.substring(4, 6), 16)
        ];
      }
    }

    const rgb = color.match(COLOR_PATTERNS.RGB);
    if (rgb) {
      return [parseInt(rgb[1]), parseInt(rgb[2]), parseInt(rgb[3])];
    }

    return null;
  }
}
