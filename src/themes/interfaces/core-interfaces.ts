/**
 * Core Theme System Interfaces
 * Defines the fundamental contracts for the theme system following SOLID principles.
 * These interfaces establish the foundation for dependency injection and loose coupling.
 */

import { ThemeOperation, ThemeErrorCategory, ThemeErrorContext } from '../theme-types';
import { ThemeLogContext, ThemePerformanceMetrics, ThemeDefinition, CSSVariableMapping } from '../theme-types';

/**
 * Interface for error factory - responsible for creating theme errors
 * Follows Single Responsibility Principle (SRP)
 */
export interface IThemeErrorFactory {
  /**
   * Create a theme error with proper context
   */
  createError(
    message: string,
    category: ThemeErrorCategory,
    operation: ThemeOperation,
    additionalContext?: Partial<ThemeErrorContext>
  ): IThemeError;

  /**
   * Convert any error to a theme error
   */
  normalizeToThemeError(
    error: unknown,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): IThemeError;

  /**
   * Categorize an error based on its characteristics
   */
  categorizeError(error: Error, operation: ThemeOperation): ThemeErrorCategory;
}

/**
 * Interface for theme error objects
 */
export interface IThemeError extends Error {
  /** Error category for recovery strategy selection */
  readonly category: ThemeErrorCategory;
  /** Theme-specific context */
  readonly context: ThemeErrorContext;
  /** Whether this error is recoverable */
  readonly recoverable: boolean;
  /** Suggested recovery actions */
  readonly recoveryActions: string[];

  /**
   * Get a user-friendly error message
   */
  getUserFriendlyMessage(): string;

  /**
   * Convert to JSON for serialization
   */
  toJSON(): Record<string, unknown>;
}

/**
 * Interface for recovery strategy management
 * Follows Single Responsibility Principle (SRP)
 */
export interface IRecoveryStrategyManager {
  /**
   * Register a recovery strategy
   */
  registerStrategy(strategy: IRecoveryStrategy): void;

  /**
   * Unregister a recovery strategy
   */
  unregisterStrategy(strategyName: string): void;

  /**
   * Get all strategies that can handle an error
   */
  getStrategiesForError(error: IThemeError): IRecoveryStrategy[];

  /**
   * Execute recovery strategies for an error
   */
  executeRecovery(error: IThemeError): Promise<boolean>;
}

/**
 * Interface for individual recovery strategies
 */
export interface IRecoveryStrategy {
  /** Strategy name */
  readonly name: string;
  /** Priority of this strategy (higher = more preferred) */
  readonly priority: number;

  /**
   * Whether this strategy can handle the error
   */
  canHandle(error: IThemeError): boolean;

  /**
   * Execute the recovery strategy
   */
  execute(error: IThemeError): Promise<boolean>;
}

/**
 * Interface for error history management
 * Follows Single Responsibility Principle (SRP)
 */
export interface IErrorHistoryManager {
  /**
   * Add error to history
   */
  addError(error: IThemeError): void;

  /**
   * Get error history
   */
  getHistory(): ReadonlyArray<IThemeError>;

  /**
   * Clear error history
   */
  clearHistory(): void;

  /**
   * Get error statistics
   */
  getStatistics(): {
    total: number;
    byCategory: Record<ThemeErrorCategory, number>;
    byOperation: Record<ThemeOperation, number>;
    recoveryRate: number;
  };

  /**
   * Set maximum history size
   */
  setMaxHistorySize(size: number): void;
}

/**
 * Interface for theme error handling orchestration
 * Follows Single Responsibility Principle (SRP) and Dependency Inversion Principle (DIP)
 */
export interface IThemeErrorHandler {
  /**
   * Handle a theme error with automatic recovery attempts
   */
  handleError(error: IThemeError): Promise<boolean>;

  /**
   * Wrap an async function with error handling
   */
  wrapAsync<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): Promise<T | null>;

  /**
   * Wrap a synchronous function with error handling
   */
  wrap<T>(
    fn: () => T,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): T | null;
}

/**
 * Interface for theme logging operations
 * Follows Interface Segregation Principle (ISP)
 */
export interface IThemeLogger {
  /**
   * Log theme operation start
   */
  logOperationStart(operation: ThemeOperation, context?: Partial<ThemeLogContext>): void;

  /**
   * Log theme operation completion
   */
  logOperationComplete(
    operation: ThemeOperation,
    success: boolean,
    metrics: ThemePerformanceMetrics,
    context?: Partial<ThemeLogContext>
  ): void;

  /**
   * Log theme error
   */
  logError(error: IThemeError, context?: Partial<ThemeLogContext>): void;

  /**
   * Log user action
   */
  logUserAction(action: string, themeId?: string, additionalData?: Record<string, unknown>): void;

  /**
   * Create a child logger
   */
  createChild(source: string, additionalMetadata?: Record<string, unknown>): IThemeLogger;
}

/**
 * Interface for performance monitoring
 * Follows Interface Segregation Principle (ISP)
 */
export interface IPerformanceMonitor {
  /**
   * Measure and log async operation performance
   */
  measureOperation<T>(
    operation: ThemeOperation,
    fn: () => Promise<T>,
    context?: Partial<ThemeLogContext>
  ): Promise<T>;

  /**
   * Measure and log sync operation performance
   */
  measureOperationSync<T>(
    operation: ThemeOperation,
    fn: () => T,
    context?: Partial<ThemeLogContext>
  ): T;

  /**
   * Set performance threshold for an operation
   */
  setPerformanceThreshold(operation: ThemeOperation, thresholdMs: number): void;

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    operationCounts: Record<string, number>;
    averageDurations: Record<string, number>;
    benchmarkViolations: Record<string, number>;
    memoryTrend: 'increasing' | 'decreasing' | 'stable' | 'unknown';
  };
}

/**
 * Interface for theme validation
 * Follows Interface Segregation Principle (ISP)
 */
export interface IThemeValidator {
  /**
   * Validate a theme definition
   */
  validateTheme(theme: ThemeDefinition): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }>;

  /**
   * Validate color format (hex, rgb, hsl, css variables)
   */
  validateColorFormat(color: string): boolean;

  /**
   * Validate contrast ratios for accessibility compliance
   */
  validateContrastRatio(foreground: string, background: string): Promise<{
    ratio: number;
    meetsWCAG_AA: boolean;
    meetsWCAG_AAA: boolean;
  }>;

  /**
   * Log theme validation results
   */
  logValidation(
    themeId: string,
    isValid: boolean,
    errors?: string[],
    warnings?: string[]
  ): void;
}

/**
 * Interface for theme configuration management
 * Follows Single Responsibility Principle (SRP)
 */
export interface IThemeConfigManager {
  /**
   * Load a theme configuration by ID
   */
  loadTheme(themeId: string): Promise<ThemeDefinition | null>;

  /**
   * Save a theme configuration
   */
  saveTheme(theme: ThemeDefinition): Promise<void>;

  /**
   * Get all available themes
   */
  getAvailableThemes(): Promise<ThemeDefinition[]>;

  /**
   * Register a new theme
   */
  registerTheme(theme: ThemeDefinition): Promise<void>;

  /**
   * Unregister a theme
   */
  unregisterTheme(themeId: string): Promise<void>;

  /**
   * Get built-in theme definitions
   */
  getBuiltInThemes(): ThemeDefinition[];
}

/**
 * Interface for CSS variable injection
 * Follows Single Responsibility Principle (SRP)
 */
export interface ICSSVariableInjector {
  /**
   * Inject CSS variables for a theme
   */
  injectVariables(theme: ThemeDefinition, target?: Element): Promise<void>;

  /**
   * Update specific CSS variables
   */
  updateVariables(variables: CSSVariableMapping[], target?: Element): Promise<void>;

  /**
   * Remove CSS variables for a theme
   */
  removeVariables(themeId: string, target?: Element): Promise<void>;

  /**
   * Generate CSS variable mappings from theme definition
   */
  generateVariableMappings(theme: ThemeDefinition): CSSVariableMapping[];

  /**
   * Get current CSS variable values
   */
  getCurrentVariables(target?: Element): Record<string, string>;
}

/**
 * Interface for theme storage and persistence
 * Follows Single Responsibility Principle (SRP)
 */
export interface IThemeStorage {
  /**
   * Store theme data
   */
  store(key: string, theme: ThemeDefinition): Promise<void>;

  /**
   * Retrieve theme data
   */
  retrieve(key: string): Promise<ThemeDefinition | null>;

  /**
   * Remove theme data
   */
  remove(key: string): Promise<void>;

  /**
   * List all stored theme keys
   */
  listKeys(): Promise<string[]>;

  /**
   * Check if storage is available
   */
  isAvailable(): boolean;

  /**
   * Get storage usage information
   */
  getStorageInfo(): Promise<{
    used: number;
    available: number;
    total: number;
  }>;
}

/**
 * Interface for dependency injection container
 * Follows Dependency Inversion Principle (DIP)
 */
export interface IThemeDependencyContainer {
  /**
   * Register a service with the container
   */
  register<T>(token: string, factory: () => T): void;

  /**
   * Register a singleton service
   */
  registerSingleton<T>(token: string, factory: () => T): void;

  /**
   * Resolve a service from the container
   */
  resolve<T>(token: string): T;

  /**
   * Check if a service is registered
   */
  isRegistered(token: string): boolean;

  /**
   * Clear all registrations
   */
  clear(): void;
}

/**
 * Service tokens for dependency injection
 */
export const SERVICE_TOKENS = {
  ERROR_FACTORY: 'IThemeErrorFactory',
  RECOVERY_STRATEGY_MANAGER: 'IRecoveryStrategyManager',
  ERROR_HISTORY_MANAGER: 'IErrorHistoryManager',
  ERROR_HANDLER: 'IThemeErrorHandler',
  LOGGER: 'IThemeLogger',
  PERFORMANCE_MONITOR: 'IPerformanceMonitor',
  VALIDATOR: 'IThemeValidator',
  CONFIG_MANAGER: 'IThemeConfigManager',
  CSS_VARIABLE_INJECTOR: 'ICSSVariableInjector',
  THEME_STORAGE: 'IThemeStorage'
} as const;
