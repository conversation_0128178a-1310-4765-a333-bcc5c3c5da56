/**
 * Enhanced FeatherJS Theme Manager
 *
 * Complete rewrite supporting full ThemeDefinition objects with comprehensive theme management.
 *
 * SOLID Compliance:
 * - Single Responsibility: Manages theme registration, application, and persistence
 * - Open/Closed: Extensible through dependency injection and strategy patterns
 * - Liskov Substitution: Maintains interface contracts for substitutability
 * - Interface Segregation: Uses focused interfaces for different responsibilities
 * - Dependency Inversion: Depends on abstractions through dependency injection
 */

import {
  IThemeLogger,
  IThemeErrorHandler,
  IPerformanceMonitor,
  IThemeConfigManager,
  ICSSVariableInjector,
  IThemeStorage,
  IThemeValidator
} from './interfaces/core-interfaces';
import {
  ThemeDefinition,
  ThemeOperation,
  CSSVariableMapping
} from './theme-types';
import { LIGHT_THEME, DARK_THEME } from './theme-config';

/**
 * Enhanced Theme Manager with comprehensive theme management capabilities
 * Supports full ThemeDefinition objects, theme registration, CSS variable injection,
 * and performance-optimized theme switching.
 */
export class ThemeManager {
  private readonly themeKey = 'featherjs-current-theme';
  private readonly registeredThemes = new Map<string, ThemeDefinition>();
  private currentTheme: ThemeDefinition | null = null;
  // private isInitialized = false; // TODO: Use this in Phase 1.4 for proper initialization tracking

  constructor(
    private readonly configManager: IThemeConfigManager,
    private readonly cssInjector: ICSSVariableInjector,
    private readonly storage: IThemeStorage,
    private readonly validator: IThemeValidator,
    private readonly doc: Document = document,
    private readonly win: Window = window,
    private readonly logger?: IThemeLogger,
    private readonly errorHandler?: IThemeErrorHandler,
    private readonly performanceMonitor?: IPerformanceMonitor
  ) {
    // Register built-in themes synchronously
    this.registerBuiltInThemesSync();

    // Log initialization
    this.logger?.logUserAction('enhanced_theme_manager_init', undefined, {
      builtInThemes: [LIGHT_THEME.id, DARK_THEME.id],
      themeKey: this.themeKey
    });
  }

  /**
   * Wrap async operations with error handling
   */
  private async wrapAsync<T>(
    operation: () => Promise<T>,
    themeOperation: ThemeOperation,
    context?: { themeId?: string; additionalData?: Record<string, unknown> }
  ): Promise<T> {
    if (this.errorHandler) {
      const result = await this.errorHandler.wrapAsync(operation, themeOperation, context);
      if (result === null) {
        throw new Error(`Operation ${themeOperation} failed and returned null`);
      }
      return result;
    }
    return operation();
  }

  /**
   * Register built-in themes synchronously during initialization
   * Built-in themes are pre-validated and don't require async validation
   */
  private registerBuiltInThemesSync(): void {
    try {
      // Register built-in themes directly without async validation
      // These themes are pre-validated and trusted
      this.registeredThemes.set(LIGHT_THEME.id, LIGHT_THEME);
      this.registeredThemes.set(DARK_THEME.id, DARK_THEME);

      this.logger?.logUserAction('builtin_themes_registered', undefined, {
        themes: [LIGHT_THEME.id, DARK_THEME.id]
      });
    } catch (error) {
      this.logger?.logUserAction('builtin_themes_registration_failed', undefined, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get the current active theme
   */
  public getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Get the current theme ID (for backward compatibility)
   */
  public getCurrentThemeId(): string | null {
    return this.currentTheme?.id || null;
  }

  /**
   * Apply theme classes to a dynamically created element
   * Enhanced version that works with full ThemeDefinition objects
   */
  public async applyThemeToElement(element: HTMLElement, themeOverride?: ThemeDefinition): Promise<void> {
    const theme = themeOverride || this.currentTheme;
    if (!theme) {
      this.logger?.logUserAction('theme_apply_element_skipped', undefined, {
        reason: 'No theme available',
        elementType: element.tagName
      });
      return Promise.resolve();
    }

    return this.wrapAsync(async () => {
      // Remove any existing theme classes
      element.classList.remove('theme-light', 'theme-dark', 'dark');

      // Add the current theme class
      element.classList.add(`theme-${theme.id}`);

      // Add dark class for Tailwind if the theme is dark
      if (theme.id === 'dark') {
        element.classList.add('dark');
      }

      // Apply CSS variables to the element
      await this.cssInjector.injectVariables(theme, element);

      this.logger?.logUserAction('theme_applied_to_element', theme.id, {
        elementType: element.tagName,
        themeOverride: !!themeOverride
      });
    }, ThemeOperation.ELEMENT_CREATE, { themeId: theme.id });
  }

  /**
   * Initialize theme handling with enhanced capabilities
   */
  public async init(defaultThemeId?: string): Promise<void> {
    const operation = ThemeOperation.THEME_LOAD;

    try {
      if (this.performanceMonitor) {
        await this.performanceMonitor.measureOperation(operation, async () => {
          await this.initializeTheme(defaultThemeId);
        });
      } else {
        await this.initializeTheme(defaultThemeId);
      }

      // this.isInitialized = true; // TODO: Use this in Phase 1.4 for proper initialization tracking
      this.logger?.logUserAction('theme_init_complete', undefined, {
        defaultThemeId,
        currentTheme: this.currentTheme?.id,
        success: true
      });
    } catch (error) {
      this.logger?.logUserAction('theme_init_failed', undefined, {
        defaultThemeId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      if (this.errorHandler) {
        await this.errorHandler.wrapAsync(
          async () => { throw error; },
          operation,
          { additionalData: { defaultThemeId } }
        );
      } else {
        throw error;
      }
    }
  }

  /**
   * Initialize theme with stored preference or default
   */
  private async initializeTheme(defaultThemeId?: string): Promise<void> {
    // Check if user has a stored preference
    const storedThemeId = this.win.localStorage.getItem(this.themeKey);

    // Determine the theme to use and whether to persist it
    let themeIdToApply: string;
    let shouldPersist = true;
    let isUsingSystemPreference = false;

    if (storedThemeId && this.registeredThemes.has(storedThemeId)) {
      // Use stored preference if available and valid
      themeIdToApply = storedThemeId;
      this.logger?.logUserAction('theme_preference_loaded', themeIdToApply, {
        source: 'stored_preference'
      });
    } else if (defaultThemeId && this.registeredThemes.has(defaultThemeId)) {
      // Use provided default if no stored preference
      themeIdToApply = defaultThemeId;
      this.logger?.logUserAction('theme_preference_loaded', themeIdToApply, {
        source: 'provided_default'
      });
    } else {
      // Use system preference if no stored preference or default
      const prefersDark = this.win.matchMedia('(prefers-color-scheme: dark)');
      themeIdToApply = prefersDark.matches ? 'dark' : 'light';
      shouldPersist = false; // Don't persist system preference initially
      isUsingSystemPreference = true;
      this.logger?.logUserAction('theme_preference_loaded', themeIdToApply, {
        source: 'system_preference'
      });
    }

    // Set up system preference listener BEFORE applying theme if using system preference
    if (isUsingSystemPreference) {
      this.setupSystemPreferenceListener();
    }

    // Apply the determined theme
    await this.applyTheme(this.registeredThemes.get(themeIdToApply)!, shouldPersist);

    // Set up theme switcher
    const switcher = this.doc.getElementById('theme-switcher') as HTMLSelectElement | null;
    if (switcher) {
      switcher.value = themeIdToApply;
      switcher.addEventListener('change', async (e) => {
        const value = (e.target as HTMLSelectElement).value;
        // When user explicitly changes theme, always persist
        await this.setTheme(value);
      });
    }

    // Set up system preference listener if not already done
    if (!isUsingSystemPreference) {
      this.setupSystemPreferenceListener();
    }
  }

  /**
   * Register a new theme
   */
  public async registerTheme(theme: ThemeDefinition): Promise<void> {
    return this.wrapAsync(async () => {
      // Check for conflicts (skip validation for built-in themes that are already registered)
      if (this.registeredThemes.has(theme.id)) {
        // Allow re-registration of built-in themes during initialization
        if (theme.isBuiltIn && (theme.id === 'light' || theme.id === 'dark')) {
          this.logger?.logUserAction('builtin_theme_reregistration_skipped', theme.id);
          return;
        }
        throw new Error(`Theme with ID '${theme.id}' is already registered`);
      }

      // Validate the theme (built-in themes are pre-validated)
      if (!theme.isBuiltIn) {
        const validation = await this.validator.validateTheme(theme);
        if (!validation.isValid) {
          throw new Error(`Theme validation failed: ${validation.errors.join(', ')}`);
        }
      }

      // Register the theme
      this.registeredThemes.set(theme.id, theme);

      // Save to storage if not built-in
      if (!theme.isBuiltIn) {
        await this.configManager.saveTheme(theme);
      }

      this.logger?.logUserAction('theme_registered', theme.id, {
        isBuiltIn: theme.isBuiltIn,
        version: theme.version
      });
    }, ThemeOperation.CUSTOM_THEME_REGISTER, { themeId: theme.id });
  }

  /**
   * Unregister a theme
   */
  public async unregisterTheme(themeId: string): Promise<void> {
    return this.wrapAsync(async () => {
      const theme = this.registeredThemes.get(themeId);
      if (!theme) {
        throw new Error(`Theme '${themeId}' is not registered`);
      }

      if (theme.isBuiltIn) {
        throw new Error(`Cannot unregister built-in theme '${themeId}'`);
      }

      // If this is the current theme, switch to default
      if (this.currentTheme?.id === themeId) {
        await this.setTheme(LIGHT_THEME.id);
      }

      // Remove from registration and storage
      this.registeredThemes.delete(themeId);
      await this.storage.remove(themeId);

      this.logger?.logUserAction('theme_unregistered', themeId);
    }, ThemeOperation.CUSTOM_THEME_REGISTER, { themeId });
  }

  /**
   * Get all available themes
   */
  public async getAvailableThemes(): Promise<ThemeDefinition[]> {
    return Array.from(this.registeredThemes.values());
  }

  /**
   * Set the current theme by ID
   */
  public async setTheme(themeId: string): Promise<void> {
    const operation = ThemeOperation.THEME_SWITCH;

    try {
      // Find the theme
      const theme = this.registeredThemes.get(themeId);
      if (!theme) {
        throw new Error(`Theme '${themeId}' is not registered`);
      }

      if (this.performanceMonitor) {
        await this.performanceMonitor.measureOperation(operation, async () => {
          await this.applyTheme(theme);
        });
      } else {
        await this.applyTheme(theme);
      }

      this.logger?.logUserAction('theme_set_programmatically', themeId, {
        success: true
      });
    } catch (error) {
      this.logger?.logUserAction('theme_set_failed', themeId, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      if (this.errorHandler) {
        await this.errorHandler.wrapAsync(
          async () => { throw error; },
          operation,
          { themeId, additionalData: { method: 'setTheme' } }
        );
      } else {
        throw error;
      }
    }
  }

  /**
   * Inject CSS variables for a theme
   */
  public async injectCSSVariables(theme: ThemeDefinition, target?: Element): Promise<void> {
    return this.wrapAsync(async () => {
      await this.cssInjector.injectVariables(theme, target);

      this.logger?.logUserAction('css_variables_injected', theme.id, {
        target: target?.tagName || 'document',
        variableCount: Object.keys(theme.colors).length
      });
    }, ThemeOperation.CSS_VARIABLE_INJECT, { themeId: theme.id });
  }

  /**
   * Update specific CSS variables
   */
  public async updateCSSVariables(variables: Array<{name: string; value: string; category?: 'core' | 'state' | 'plugin'}>, target?: Element): Promise<void> {
    return this.wrapAsync(async () => {
      const mappings: CSSVariableMapping[] = variables.map(v => ({
        name: v.name,
        value: v.value,
        category: v.category || 'plugin',
        variable: v.name
      }));
      await this.cssInjector.updateVariables(mappings, target);

      this.logger?.logUserAction('css_variables_updated', undefined, {
        target: target?.tagName || 'document',
        variableCount: variables.length
      });
    }, ThemeOperation.CSS_VARIABLE_INJECT);
  }

  /**
   * Listen for theme changes. Returns an unsubscribe function.
   */
  public watch(callback: (theme: ThemeDefinition) => void): () => void {
    const handler = (e: Event): void => {
      callback((e as CustomEvent<ThemeDefinition>).detail);
    };
    this.doc.addEventListener('themechange', handler);
    return () => this.doc.removeEventListener('themechange', handler);
  }

  /* ------------------------------------------------------------------ */

  /**
   * Apply a theme to the document
   */
  private async applyTheme(theme: ThemeDefinition, persist = true): Promise<void> {
    const operation = ThemeOperation.THEME_SWITCH;

    try {
      if (this.performanceMonitor) {
        await this.performanceMonitor.measureOperation(operation, async () => {
          await this.executeThemeApplication(theme, persist);
        });
      } else {
        await this.executeThemeApplication(theme, persist);
      }

      this.logger?.logUserAction('theme_applied', theme.id, {
        persist,
        success: true
      });
    } catch (error) {
      this.logger?.logUserAction('theme_apply_failed', theme.id, {
        persist,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      if (this.errorHandler) {
        await this.errorHandler.wrapAsync(
          async () => { throw error; },
          operation,
          { themeId: theme.id, additionalData: { persist, method: 'applyTheme' } }
        );
      } else {
        throw error;
      }
    }
  }

  /**
   * Execute the actual theme application
   */
  private async executeThemeApplication(theme: ThemeDefinition, persist: boolean): Promise<void> {
    this.logger?.logUserAction('theme_apply_start', theme.id, { persist });

    // Set current theme
    this.currentTheme = theme;

    const rootEl = this.doc.documentElement;
    this.logger?.logUserAction('theme_dom_manipulation_start', theme.id, {
      beforeClasses: rootEl.classList.toString()
    });

    // Clear previous theme classes from body and root, and data-theme attribute
    const allThemeIds = Array.from(this.registeredThemes.keys());
    this.doc.body.classList.remove(...allThemeIds.map((t) => `theme-${t}`));
    rootEl.classList.remove('dark', ...allThemeIds.map(t => `theme-${t}`));
    rootEl.removeAttribute('data-theme');

    // Apply theme-specific class to both body and root element for consistent CSS variable application
    this.doc.body.classList.add(`theme-${theme.id}`);
    rootEl.classList.add(`theme-${theme.id}`);

    // Apply Tailwind dark class to root element for dark mode
    if (theme.id === 'dark') {
      rootEl.classList.add('dark');
    }

    // Inject CSS variables
    await this.cssInjector.injectVariables(theme);

    this.logger?.logUserAction('theme_dom_manipulation_complete', theme.id, {
      afterClasses: rootEl.classList.toString()
    });

    // Persist selection
    if (persist) {
      try {
        // Store theme preference in localStorage directly for simplicity
        this.win.localStorage.setItem(this.themeKey, theme.id);
        this.logger?.logUserAction('theme_persisted', theme.id);
      } catch (error) {
        this.logger?.logUserAction('theme_persist_failed', theme.id, {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        // Don't throw here - persistence failure shouldn't break theme application
      }
    }

    // Mirror value in select if present
    const switcher = this.doc.getElementById('theme-switcher') as HTMLSelectElement | null;
    if (switcher && switcher.value !== theme.id) {
      switcher.value = theme.id;
      this.logger?.logUserAction('theme_switcher_updated', theme.id);
    }

    // Notify listeners
    this.doc.dispatchEvent(new CustomEvent<ThemeDefinition>('themechange', {
      detail: theme,
      bubbles: true,
      composed: true
    }));

    // Dispatch a global theme change event for plugins to listen for
    this.win.dispatchEvent(new CustomEvent('feather:themechange', {
      detail: { theme }
    }));

    this.logger?.logUserAction('theme_events_dispatched', theme.id);
  }

  /**
   * Set up system preference listener
   */
  private setupSystemPreferenceListener(): void {
    // If user already has a stored preference, don't set up the system preference listener
    const storedTheme = this.win.localStorage.getItem(this.themeKey);
    if (storedTheme) {
      this.logger?.logUserAction('system_preference_listener_skipped', undefined, {
        reason: 'user_has_explicit_preference'
      });
      return;
    }

    const prefersDark = this.win.matchMedia('(prefers-color-scheme: dark)');

    // Add event listener for system preference changes
    const handleColorSchemeChange = (e: MediaQueryListEvent) => {
      // Double-check that user hasn't set a preference since we set up the listener
      const currentStored = this.win.localStorage.getItem(this.themeKey);
      if (!currentStored) {
        const newTheme = e.matches ? 'dark' : 'light';
        this.logger?.logUserAction('system_preference_changed', newTheme);

        // Apply new system preference without persisting (maintain system preference mode)
        this.applyTheme(this.registeredThemes.get(newTheme)!, false).catch((error: unknown) => {
          this.logger?.logUserAction('system_preference_change_apply_failed', newTheme, {
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        });
      } else {
        this.logger?.logUserAction('system_preference_change_ignored', undefined, {
          reason: 'user_has_explicit_preference'
        });
      }
    };

    // Use the standard event listener API
    prefersDark.addEventListener('change', handleColorSchemeChange);
    this.logger?.logUserAction('system_preference_listener_setup', undefined, {
      initialPreference: prefersDark.matches ? 'dark' : 'light'
    });
  }
}
