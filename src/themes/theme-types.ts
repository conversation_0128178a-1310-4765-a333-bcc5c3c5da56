/**
 * Shared Theme System Types and Enums
 * Contains common types used across the theme system for consistency and reusability.
 */

/**
 * Theme-specific error categories for better error handling and recovery
 */
export enum ThemeErrorCategory {
  VALIDATION = 'validation',
  PERFORMANCE = 'performance',
  NETWORK = 'network',
  USER = 'user',
  SYSTEM = 'system'
}

/**
 * Theme operation types for error context
 */
export enum ThemeOperation {
  THEME_SWITCH = 'theme_switch',
  THEME_LOAD = 'theme_load',
  THEME_SAVE = 'theme_save',
  THEME_VALIDATE = 'theme_validate',
  ELEMENT_CREATE = 'element_create',
  CSS_INJECT = 'css_inject',
  PLUGIN_APPLY = 'plugin_apply',
  CUSTOM_THEME_REGISTER = 'custom_theme_register',
  THEME_CONFIG_LOAD = 'theme_config_load',
  THEME_CONFIG_SAVE = 'theme_config_save',
  CSS_VARIABLE_INJECT = 'css_variable_inject'
}

/**
 * Core color properties that every theme must define
 */
export interface ThemeCoreColors {
  /** Primary text color */
  text: string;
  /** Primary background color */
  background: string;
  /** Secondary background color for surfaces */
  surface: string;
  /** Border color for UI elements */
  border: string;
  /** Primary accent color */
  primary: string;
  /** Secondary accent color */
  secondary: string;
  /** Success state color */
  success: string;
  /** Warning state color */
  warning: string;
  /** Error state color */
  error: string;
  /** Information state color */
  info: string;
}

/**
 * State-specific color variations
 */
export interface ThemeStateColors {
  /** Hover state colors */
  hover: {
    background: string;
    border: string;
    text: string;
  };
  /** Focus state colors */
  focus: {
    background: string;
    border: string;
    ring: string;
  };
  /** Active state colors */
  active: {
    background: string;
    border: string;
    text: string;
  };
  /** Disabled state colors */
  disabled: {
    background: string;
    border: string;
    text: string;
  };
}

/**
 * Plugin-specific color configurations
 */
export interface ThemePluginColors {
  /** Color palette plugin colors */
  palette: {
    background: string;
    border: string;
    shadow: string;
    tabActiveBorder: string;
    swatchBorder: string;
  };
  /** Chart plugin colors */
  chart: {
    background: string;
    gridLines: string;
    dataColors: string[];
  };
  /** Code block plugin colors */
  code: {
    background: string;
    border: string;
    text: string;
    keyword: string;
    string: string;
    comment: string;
  };
  /** Table plugin colors */
  table: {
    background: string;
    border: string;
    headerBackground: string;
    alternateRowBackground: string;
  };
  /** Comment plugin colors */
  comments: {
    background: string;
    border: string;
    highlightBackground: string;
    avatarBackground: string;
  };
}

/**
 * Animation and transition configuration
 */
export interface ThemeAnimationConfig {
  /** Default transition duration in milliseconds */
  duration: number;
  /** Transition easing function */
  easing: string;
  /** Whether to respect reduced motion preferences */
  respectReducedMotion: boolean;
}

/**
 * Complete theme definition interface
 */
export interface ThemeDefinition {
  /** Unique theme identifier */
  id: string;
  /** Human-readable theme name */
  name: string;
  /** Theme description */
  description?: string;
  /** Theme version for migration support */
  version: string;
  /** Theme author information */
  author?: string;
  /** Core color definitions */
  colors: ThemeCoreColors;
  /** State-specific color variations */
  stateColors: ThemeStateColors;
  /** Plugin-specific colors */
  pluginColors: ThemePluginColors;
  /** Animation configuration */
  animation: ThemeAnimationConfig;
  /** CSS variable prefix (defaults to 'theme') */
  cssVariablePrefix?: string;
  /** Whether this is a built-in theme */
  isBuiltIn?: boolean;
  /** Creation timestamp */
  createdAt?: Date;
  /** Last modified timestamp */
  modifiedAt?: Date;
}

/**
 * CSS variable mapping configuration
 */
export interface CSSVariableMapping {
  /** Variable name (without prefix) */
  name: string;
  /** Color value */
  value: string;
  /** Fallback value if primary value fails */
  fallback?: string;
  /** Variable category for organization */
  category: 'core' | 'state' | 'plugin';
  /** Description of the variable's purpose */
  description?: string;
}

/**
 * Interface for theme error context data
 */
export interface ThemeErrorContext {
  /** The theme ID involved in the error */
  themeId?: string;
  /** The operation that failed */
  operation: ThemeOperation;
  /** User state when error occurred */
  userState?: {
    currentTheme?: string;
    isCustomTheme?: boolean;
    elementCount?: number;
  };
  /** Performance metrics at time of error */
  performance?: {
    memoryUsage?: number;
    executionTime?: number;
    elementCount?: number;
  };
  /** Additional context data */
  additionalData?: Record<string, unknown>;
}

/**
 * Recovery strategy interface
 */
export interface RecoveryStrategy {
  /** Strategy name */
  name: string;
  /** Whether this strategy can handle the error */
  canHandle(error: any): boolean;
  /** Execute the recovery strategy */
  execute(error: any): Promise<boolean>;
  /** Priority of this strategy (higher = more preferred) */
  priority: number;
}

/**
 * Theme operation performance metrics
 */
export interface ThemePerformanceMetrics {
  /** Operation start time */
  startTime: number;
  /** Operation end time */
  endTime: number;
  /** Duration in milliseconds */
  duration: number;
  /** Memory usage before operation */
  memoryBefore?: number;
  /** Memory usage after operation */
  memoryAfter?: number;
  /** Number of elements affected */
  elementCount?: number;
  /** Whether operation met performance benchmarks */
  meetsBenchmark: boolean;
  /** Performance benchmark threshold */
  benchmark: number;
}

/**
 * Theme logging context for structured logging
 */
export interface ThemeLogContext {
  /** Theme ID involved in the operation */
  themeId?: string;
  /** Operation being performed */
  operation: ThemeOperation;
  /** User state during operation */
  userState?: {
    currentTheme?: string;
    isCustomTheme?: boolean;
    elementCount?: number;
    sessionId?: string;
  };
  /** Performance metrics */
  performance?: ThemePerformanceMetrics;
  /** Plugin context if applicable */
  pluginContext?: {
    pluginId?: string;
    pluginType?: string;
  };
  /** Additional context data */
  additionalData?: Record<string, unknown>;
}
