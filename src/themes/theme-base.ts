/**
 * Theme Base Class and Integration Patterns
 * Provides standardized error handling and logging patterns for all theme system components.
 * Establishes consistent patterns for try-catch-log-recover operations.
 *
 * SOLID Compliance:
 * - Single Responsibility: Provides base functionality for theme components
 * - Dependency Inversion: Depends on interfaces, not concrete implementations
 * - Open/Closed: Extensible through inheritance and composition
 */

import { ThemeOperation, ThemeErrorCategory, ThemeLogContext } from './theme-types';
import { IThemeLogger, IThemeErrorHandler, IPerformanceMonitor, IThemeErrorFactory, IThemeError } from './interfaces/core-interfaces';

/**
 * Performance monitoring decorator for theme operations
 * Requires the class to have a performanceMonitor property
 */
export function ThemePerformanceMonitor(operation: ThemeOperation) {
  return function (_target: unknown, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (this: { performanceMonitor: IPerformanceMonitor }, ...args: unknown[]) {
      if (!this.performanceMonitor) {
        throw new Error(`Performance monitor not injected for ${propertyName}`);
      }

      const context: Partial<ThemeLogContext> = {
        operation,
        additionalData: { method: propertyName, args: args.length }
      };

      return await this.performanceMonitor.measureOperation(operation, async () => {
        return await method.apply(this, args);
      }, context);
    };

    return descriptor;
  };
}

/**
 * Error handling decorator for theme operations
 * Requires the class to have errorHandler and logger properties
 */
export function ThemeErrorHandlerDecorator(operation: ThemeOperation, category: ThemeErrorCategory = ThemeErrorCategory.SYSTEM) {
  return function (_target: unknown, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (this: { errorHandler: IThemeErrorHandler; logger: IThemeLogger; errorFactory: IThemeErrorFactory }, ...args: unknown[]) {
      if (!this.errorHandler) {
        throw new Error(`Error handler not injected for ${propertyName}`);
      }
      if (!this.logger) {
        throw new Error(`Logger not injected for ${propertyName}`);
      }
      if (!this.errorFactory) {
        throw new Error(`Error factory not injected for ${propertyName}`);
      }

      try {
        return await method.apply(this, args);
      } catch (error) {
        const themeError = this.errorFactory.createError(
          `Error in ${propertyName}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          category,
          operation,
          { additionalData: { method: propertyName, args: args.length } }
        );

        this.logger.logError(themeError);

        const recovered = await this.errorHandler.handleError(themeError);
        if (!recovered) {
          throw themeError;
        }

        return null;
      }
    };

    return descriptor;
  };
}

/**
 * Base class for all theme system components
 * Uses dependency injection for all dependencies
 */
export abstract class ThemeBase {
  protected logger: IThemeLogger;
  protected errorHandler: IThemeErrorHandler;
  protected errorFactory: IThemeErrorFactory;
  protected performanceMonitor: IPerformanceMonitor;
  protected componentName: string;

  constructor(
    componentName: string,
    logger: IThemeLogger,
    errorHandler: IThemeErrorHandler,
    errorFactory: IThemeErrorFactory,
    performanceMonitor: IPerformanceMonitor
  ) {
    this.componentName = componentName;
    this.logger = logger;
    this.errorHandler = errorHandler;
    this.errorFactory = errorFactory;
    this.performanceMonitor = performanceMonitor;
  }

  /**
   * Execute a theme operation with integrated error handling and logging
   */
  protected async executeThemeOperation<T>(
    operation: ThemeOperation,
    fn: () => Promise<T>,
    context?: Partial<ThemeLogContext>
  ): Promise<T | null> {
    const fullContext: Partial<ThemeLogContext> = {
      ...context,
      operation,
      additionalData: {
        component: this.componentName,
        ...context?.additionalData
      }
    };

    try {
      return await this.performanceMonitor.measureOperation(operation, fn, fullContext);
    } catch (error) {
      const themeError = this.errorFactory.createError(
        error instanceof Error ? error.message : 'Unknown error occurred',
        this.categorizeError(error, operation),
        operation,
        fullContext
      );

      this.logger.logError(themeError, fullContext);

      const recovered = await this.errorHandler.handleError(themeError);
      if (!recovered) {
        throw themeError;
      }

      return null;
    }
  }

  /**
   * Execute a synchronous theme operation with integrated error handling and logging
   */
  protected executeThemeOperationSync<T>(
    operation: ThemeOperation,
    fn: () => T,
    context?: Partial<ThemeLogContext>
  ): T | null {
    const fullContext: Partial<ThemeLogContext> = {
      ...context,
      operation,
      additionalData: {
        component: this.componentName,
        ...context?.additionalData
      }
    };

    try {
      return this.performanceMonitor.measureOperationSync(operation, fn, fullContext);
    } catch (error) {
      const themeError = this.errorFactory.createError(
        error instanceof Error ? error.message : 'Unknown error occurred',
        this.categorizeError(error, operation),
        operation,
        fullContext
      );

      this.logger.logError(themeError, fullContext);
      throw themeError;
    }
  }

  /**
   * Safely execute a function with error handling but no performance monitoring
   */
  protected async safeExecute<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    fallbackValue?: T
  ): Promise<T | undefined> {
    try {
      return await fn();
    } catch (error) {
      const themeError = this.errorFactory.createError(
        error instanceof Error ? error.message : 'Safe execution failed',
        ThemeErrorCategory.SYSTEM,
        operation,
        { additionalData: { component: this.componentName } }
      );

      this.logger.logError(themeError);
      return fallbackValue;
    }
  }

  /**
   * Safely execute a synchronous function with error handling
   */
  protected safeExecuteSync<T>(
    fn: () => T,
    operation: ThemeOperation,
    fallbackValue?: T
  ): T | undefined {
    try {
      return fn();
    } catch (error) {
      const themeError = this.errorFactory.createError(
        error instanceof Error ? error.message : 'Safe execution failed',
        ThemeErrorCategory.SYSTEM,
        operation,
        { additionalData: { component: this.componentName } }
      );

      this.logger.logError(themeError);
      return fallbackValue;
    }
  }

  /**
   * Log user action with context
   */
  protected logUserAction(action: string, additionalData?: Record<string, unknown>): void {
    this.logger.logUserAction(action, undefined, {
      component: this.componentName,
      ...additionalData
    });
  }

  /**
   * Create a theme error with component context
   */
  protected createError(
    message: string,
    category: ThemeErrorCategory,
    operation: ThemeOperation,
    additionalContext?: Record<string, unknown>
  ): IThemeError {
    return this.errorFactory.createError(message, category, operation, {
      additionalData: {
        component: this.componentName,
        ...additionalContext
      }
    });
  }

  /**
   * Categorize an error based on its characteristics
   */
  private categorizeError(error: unknown, operation: ThemeOperation): ThemeErrorCategory {
    if (error instanceof Error) {
      // Check for specific error patterns
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return ThemeErrorCategory.VALIDATION;
      }
      if (error.message.includes('network') || error.message.includes('fetch')) {
        return ThemeErrorCategory.NETWORK;
      }
      if (error.message.includes('performance') || error.message.includes('timeout')) {
        return ThemeErrorCategory.PERFORMANCE;
      }
      if (error.message.includes('permission') || error.message.includes('access')) {
        return ThemeErrorCategory.USER;
      }
    }

    // Categorize by operation type
    switch (operation) {
      case ThemeOperation.THEME_VALIDATE:
        return ThemeErrorCategory.VALIDATION;
      case ThemeOperation.THEME_LOAD:
      case ThemeOperation.THEME_SAVE:
        return ThemeErrorCategory.NETWORK;
      case ThemeOperation.THEME_SWITCH:
      case ThemeOperation.CSS_INJECT:
        return ThemeErrorCategory.PERFORMANCE;
      default:
        return ThemeErrorCategory.SYSTEM;
    }
  }

  /**
   * Cleanup resources (to be called when component is destroyed)
   */
  protected cleanup(): void {
    // Override in subclasses to provide specific cleanup logic
    this.logger.logUserAction('cleanup', undefined, { component: this.componentName });
  }
}

/**
 * Utility functions for theme operations
 */
export class ThemeUtils {
  /**
   * Retry a theme operation with exponential backoff
   */
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    baseDelay = 100,
    logger?: IThemeLogger
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt === maxRetries) {
          break;
        }

        const delay = baseDelay * Math.pow(2, attempt - 1);
        if (logger) {
          logger.logUserAction('retry_operation', undefined, {
            attempt,
            maxRetries,
            delay,
            error: lastError.message
          });
        }

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Debounce theme operations to prevent excessive calls
   */
  static debounce<T extends (...args: unknown[]) => unknown>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  /**
   * Throttle theme operations to limit frequency
   */
  static throttle<T extends (...args: unknown[]) => unknown>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Validate theme data structure
   */
  static validateThemeStructure(theme: unknown): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!theme || typeof theme !== 'object') {
      errors.push('Theme must be an object');
      return { isValid: false, errors };
    }

    const themeObj = theme as Record<string, unknown>;

    if (!themeObj.id || typeof themeObj.id !== 'string') {
      errors.push('Theme must have a valid id string');
    }

    if (!themeObj.name || typeof themeObj.name !== 'string') {
      errors.push('Theme must have a valid name string');
    }

    if (!themeObj.colors || typeof themeObj.colors !== 'object') {
      errors.push('Theme must have a colors object');
    }

    return { isValid: errors.length === 0, errors };
  }
}
