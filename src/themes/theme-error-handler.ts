/**
 * Theme System Error Handling Infrastructure
 *
 * @deprecated This file is maintained for backward compatibility only.
 * For new code, use the SOLID-compliant implementations in the error-handling/ folder:
 * - ThemeErrorFactory for error creation
 * - ThemeErrorHandlerImpl for error handling
 * - RecoveryStrategyManager for recovery strategies
 * - ErrorHistoryManager for error history
 *
 * Shared types have been moved to theme-types.ts for better organization.
 */

// Re-export shared types for backward compatibility
export type {
  ThemeErrorContext,
  RecoveryStrategy
} from './theme-types';

export {
  ThemeErrorCategory,
  ThemeOperation
} from './theme-types';

// Re-export new implementations for easy migration
export { ThemeError } from './error-handling/theme-error-factory';
export { ThemeErrorHandlerImpl } from './error-handling/theme-error-handler-impl';

// Legacy alias for backward compatibility
export { ThemeErrorHandlerImpl as ThemeErrorHandler } from './error-handling/theme-error-handler-impl';
