/**
 * Theme Architecture Tests
 * Validates the new SOLID-compliant theme system architecture
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  IThemeErrorFactory,
  IRecoveryStrategyManager,
  IErrorHistoryManager,
  IThemeErrorHandler,
  SERVICE_TOKENS
} from '../interfaces/core-interfaces';
import { ThemeDependencyContainer } from '../dependency-injection/theme-container';
import { setupThemeServices, validateThemeServices } from '../dependency-injection/theme-service-setup';
import { ThemeErrorCategory, ThemeOperation } from '../theme-types';

describe('Theme Architecture', () => {
  let container: ThemeDependencyContainer;

  beforeEach(() => {
    container = new ThemeDependencyContainer();
    setupThemeServices(container, {
      maxErrorHistory: 10,
      registerBasicStrategies: true
    });
  });

  describe('Dependency Injection Container', () => {
    it('should register and resolve services correctly', () => {
      // Test service registration
      expect(container.isRegistered(SERVICE_TOKENS.ERROR_FACTORY)).toBe(true);
      expect(container.isRegistered(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER)).toBe(true);
      expect(container.isRegistered(SERVICE_TOKENS.ERROR_HISTORY_MANAGER)).toBe(true);
      expect(container.isRegistered(SERVICE_TOKENS.ERROR_HANDLER)).toBe(true);

      // Test service resolution
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);
      const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);
      const errorHandler = container.resolve<IThemeErrorHandler>(SERVICE_TOKENS.ERROR_HANDLER);

      expect(errorFactory).toBeDefined();
      expect(recoveryManager).toBeDefined();
      expect(historyManager).toBeDefined();
      expect(errorHandler).toBeDefined();
    });

    it('should maintain singleton instances', () => {
      const errorFactory1 = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const errorFactory2 = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);

      expect(errorFactory1).toBe(errorFactory2);
    });

    it('should detect circular dependencies', () => {
      const testContainer = new ThemeDependencyContainer();

      testContainer.register('ServiceA', () => testContainer.resolve('ServiceB'));
      testContainer.register('ServiceB', () => testContainer.resolve('ServiceA'));

      expect(() => testContainer.resolve('ServiceA')).toThrow(/circular dependency/i);
    });
  });

  describe('Error Factory (SRP Compliance)', () => {
    it('should create theme errors with proper categorization', () => {
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);

      const error = errorFactory.createError(
        'Test validation error',
        ThemeErrorCategory.VALIDATION,
        ThemeOperation.THEME_VALIDATE,
        { themeId: 'test-theme' }
      );

      expect(error.category).toBe(ThemeErrorCategory.VALIDATION);
      expect(error.context.operation).toBe(ThemeOperation.THEME_VALIDATE);
      expect(error.context.themeId).toBe('test-theme');
      expect(error.recoverable).toBe(true);
      expect(error.recoveryActions).toContain('Check theme configuration format');
    });

    it('should normalize errors correctly', () => {
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);

      const normalError = new Error('Network timeout');
      const themeError = errorFactory.normalizeToThemeError(
        normalError,
        ThemeOperation.THEME_LOAD
      );

      expect(themeError.category).toBe(ThemeErrorCategory.NETWORK);
      expect(themeError.context.operation).toBe(ThemeOperation.THEME_LOAD);
      expect(themeError.message).toBe('Network timeout');
    });
  });

  describe('Recovery Strategy Manager (Strategy Pattern)', () => {
    it('should register and manage recovery strategies', () => {
      const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);

      // Check that basic strategies are registered
      const strategyNames = (recoveryManager as any).getRegisteredStrategyNames();
      expect(strategyNames).toContain('FallbackThemeStrategy');
      expect(strategyNames).toContain('CacheClearStrategy');
      expect(strategyNames).toContain('DOMRefreshStrategy');
      expect(strategyNames).toContain('RetryOperationStrategy');
      expect(strategyNames).toContain('UserNotificationStrategy');
    });

    it('should find appropriate strategies for errors', () => {
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);

      const validationError = errorFactory.createError(
        'Invalid theme format',
        ThemeErrorCategory.VALIDATION,
        ThemeOperation.THEME_VALIDATE
      );

      const strategies = recoveryManager.getStrategiesForError(validationError);
      expect(strategies.length).toBeGreaterThan(0);

      // Should include fallback strategy for validation errors
      const strategyNames = strategies.map(s => s.name);
      expect(strategyNames).toContain('FallbackThemeStrategy');
    });
  });

  describe('Error History Manager (SRP Compliance)', () => {
    it('should track error history and statistics', () => {
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);

      const error1 = errorFactory.createError(
        'Validation error',
        ThemeErrorCategory.VALIDATION,
        ThemeOperation.THEME_VALIDATE
      );

      const error2 = errorFactory.createError(
        'Network error',
        ThemeErrorCategory.NETWORK,
        ThemeOperation.THEME_LOAD
      );

      historyManager.addError(error1);
      historyManager.addError(error2);

      const history = historyManager.getHistory();
      expect(history).toHaveLength(2);

      const stats = historyManager.getStatistics();
      expect(stats.total).toBe(2);
      expect(stats.byCategory[ThemeErrorCategory.VALIDATION]).toBe(1);
      expect(stats.byCategory[ThemeErrorCategory.NETWORK]).toBe(1);
      expect(stats.byOperation[ThemeOperation.THEME_VALIDATE]).toBe(1);
      expect(stats.byOperation[ThemeOperation.THEME_LOAD]).toBe(1);
    });

    it('should respect maximum history size', () => {
      const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);

      // Add more errors than the max size (10)
      for (let i = 0; i < 15; i++) {
        const error = errorFactory.createError(
          `Error ${i}`,
          ThemeErrorCategory.SYSTEM,
          ThemeOperation.THEME_SWITCH
        );
        historyManager.addError(error);
      }

      const history = historyManager.getHistory();
      expect(history).toHaveLength(10);
    });
  });

  describe('Theme Error Handler (DIP Compliance)', () => {
    it('should orchestrate error handling using injected dependencies', async () => {
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const errorHandler = container.resolve<IThemeErrorHandler>(SERVICE_TOKENS.ERROR_HANDLER);
      const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);

      const error = errorFactory.createError(
        'Test error',
        ThemeErrorCategory.VALIDATION,
        ThemeOperation.THEME_VALIDATE
      );

      // Handle the error
      const recovered = await errorHandler.handleError(error);

      // Error should be added to history
      const history = historyManager.getHistory();
      expect(history).toContain(error);

      // Recovery attempt should be made (may or may not succeed)
      expect(typeof recovered).toBe('boolean');
    });

    it('should wrap async functions with error handling', async () => {
      const errorHandler = container.resolve<IThemeErrorHandler>(SERVICE_TOKENS.ERROR_HANDLER);

      // Test successful operation
      const successResult = await errorHandler.wrapAsync(
        async () => 'success',
        ThemeOperation.THEME_SWITCH
      );
      expect(successResult).toBe('success');

      // Test failing operation
      const failResult = await errorHandler.wrapAsync(
        async () => {
          throw new Error('Test failure');
        },
        ThemeOperation.THEME_SWITCH
      );

      // Should return null if recovery succeeds, or throw if recovery fails
      expect(failResult === null || failResult === undefined).toBe(true);
    });
  });

  describe('Service Validation', () => {
    it('should validate service configuration', () => {
      const validation = validateThemeServices(container);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);

      // May have warnings about strategy count, etc.
      if (validation.warnings.length > 0) {
        console.log('Service validation warnings:', validation.warnings);
      }
    });

    it('should detect missing services', () => {
      const emptyContainer = new ThemeDependencyContainer();
      const validation = validateThemeServices(emptyContainer);

      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors.some(error => error.includes('not registered'))).toBe(true);
    });
  });

  describe('SOLID Principles Compliance', () => {
    it('should demonstrate Single Responsibility Principle', () => {
      // Each service has a focused responsibility
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);
      const recoveryManager = container.resolve<IRecoveryStrategyManager>(SERVICE_TOKENS.RECOVERY_STRATEGY_MANAGER);
      const historyManager = container.resolve<IErrorHistoryManager>(SERVICE_TOKENS.ERROR_HISTORY_MANAGER);

      // Error factory only creates errors
      expect(typeof errorFactory.createError).toBe('function');
      expect(typeof errorFactory.normalizeToThemeError).toBe('function');

      // Recovery manager only manages strategies
      expect(typeof recoveryManager.registerStrategy).toBe('function');
      expect(typeof recoveryManager.executeRecovery).toBe('function');

      // History manager only manages history
      expect(typeof historyManager.addError).toBe('function');
      expect(typeof historyManager.getStatistics).toBe('function');
    });

    it('should demonstrate Dependency Inversion Principle', () => {
      // Error handler depends on abstractions, not concretions
      const errorHandler = container.resolve<IThemeErrorHandler>(SERVICE_TOKENS.ERROR_HANDLER);

      // Should have methods that work with interfaces
      expect(typeof errorHandler.handleError).toBe('function');
      expect(typeof errorHandler.wrapAsync).toBe('function');

      // Dependencies are injected, not hard-coded
      const dependencyInfo = (errorHandler as any).getDependencyInfo?.();
      if (dependencyInfo) {
        expect(dependencyInfo.errorFactory).toBeDefined();
        expect(dependencyInfo.recoveryManager).toBeDefined();
        expect(dependencyInfo.historyManager).toBeDefined();
      }
    });

    it('should demonstrate Interface Segregation Principle', () => {
      // Interfaces are focused and don't force clients to depend on unused methods
      const errorFactory = container.resolve<IThemeErrorFactory>(SERVICE_TOKENS.ERROR_FACTORY);

      // Error factory interface only has error-related methods
      const factoryMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(errorFactory));
      expect(factoryMethods).not.toContain('registerStrategy'); // This belongs to recovery manager
      expect(factoryMethods).not.toContain('addError'); // This belongs to history manager
    });
  });
});
