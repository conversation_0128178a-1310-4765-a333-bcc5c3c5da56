/**
 * Tests for Theme Performance Benchmark Utilities
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  ThemeBenchmarkRunner,
  BenchmarkScenario,
  BenchmarkSuite,
  BenchmarkResult
} from '../../performance/benchmark-utilities';
import { ThemeOperation } from '../../theme-types';
import { IPerformanceMonitor, IThemeLogger } from '../../interfaces/core-interfaces';

// Mock implementations
class MockPerformanceMonitor implements IPerformanceMonitor {
  public async measureOperation<T>(
    _operation: ThemeOperation,
    fn: () => Promise<T>
  ): Promise<T> {
    return fn();
  }

  public measureOperationSync<T>(
    _operation: ThemeOperation,
    fn: () => T
  ): T {
    return fn();
  }

  public setPerformanceThreshold(_operation: ThemeOperation, _thresholdMs: number): void {
    // Mock implementation
  }

  public getPerformanceStats() {
    return {
      operationCounts: {},
      averageDurations: {},
      benchmarkViolations: {},
      memoryTrend: 'stable' as const
    };
  }
}

class MockThemeLogger implements IThemeLogger {
  public logOperationStart = vi.fn();
  public logOperationComplete = vi.fn();
  public logError = vi.fn();
  public logUserAction = vi.fn();
  public createChild = vi.fn().mockReturnValue(this);
}

describe('ThemeBenchmarkRunner', () => {
  let benchmarkRunner: ThemeBenchmarkRunner;
  let mockPerformanceMonitor: MockPerformanceMonitor;
  let mockLogger: MockThemeLogger;

  beforeEach(() => {
    mockPerformanceMonitor = new MockPerformanceMonitor();
    mockLogger = new MockThemeLogger();
    benchmarkRunner = new ThemeBenchmarkRunner(mockPerformanceMonitor, mockLogger);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('executeBenchmark', () => {
    it('should execute a simple benchmark scenario', async () => {
      const scenario: BenchmarkScenario = {
        id: 'test-scenario',
        name: 'Test Scenario',
        description: 'A test scenario',
        operation: ThemeOperation.THEME_SWITCH,
        expectedThreshold: 100,
        iterations: 3
      };

      const result = await benchmarkRunner.executeBenchmark(scenario);

      expect(result.scenario).toBe(scenario);
      expect(result.durations).toHaveLength(3);
      expect(result.averageDuration).toBeGreaterThan(0);
      expect(result.minDuration).toBeGreaterThanOrEqual(0);
      expect(result.maxDuration).toBeGreaterThanOrEqual(result.minDuration);
      expect(result.errors).toHaveLength(0);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'benchmark_start',
        undefined,
        expect.objectContaining({
          scenarioId: 'test-scenario',
          scenarioName: 'Test Scenario',
          iterations: 3
        })
      );
    });

    it('should handle benchmark scenarios with setup and cleanup', async () => {
      const setupSpy = vi.fn();
      const cleanupSpy = vi.fn();

      const scenario: BenchmarkScenario = {
        id: 'test-scenario-with-lifecycle',
        name: 'Test Scenario with Lifecycle',
        description: 'A test scenario with setup and cleanup',
        operation: ThemeOperation.CSS_INJECT,
        expectedThreshold: 10,
        iterations: 2,
        setup: setupSpy,
        cleanup: cleanupSpy
      };

      const result = await benchmarkRunner.executeBenchmark(scenario);

      expect(setupSpy).toHaveBeenCalledOnce();
      expect(cleanupSpy).toHaveBeenCalledOnce();
      expect(result.durations).toHaveLength(2);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle benchmark scenarios with custom validation', async () => {
      const customValidator = vi.fn().mockReturnValue(false);

      const scenario: BenchmarkScenario = {
        id: 'test-scenario-validation',
        name: 'Test Scenario with Validation',
        description: 'A test scenario with custom validation',
        operation: ThemeOperation.ELEMENT_CREATE,
        expectedThreshold: 1,
        iterations: 1,
        validate: customValidator
      };

      const result = await benchmarkRunner.executeBenchmark(scenario);

      expect(customValidator).toHaveBeenCalledWith(result);
      expect(result.passed).toBe(false);
      expect(result.errors).toContain('Custom validation failed');
    });

    it('should handle errors during benchmark execution', async () => {
      const errorSetup = vi.fn().mockRejectedValue(new Error('Setup failed'));

      const scenario: BenchmarkScenario = {
        id: 'test-scenario-error',
        name: 'Test Scenario with Error',
        description: 'A test scenario that fails',
        operation: ThemeOperation.THEME_VALIDATE,
        expectedThreshold: 10,
        iterations: 1,
        setup: errorSetup
      };

      const result = await benchmarkRunner.executeBenchmark(scenario);

      expect(result.errors).toContain('Setup/cleanup error: Setup failed');
      expect(result.passed).toBe(false);
    });

    it('should calculate statistics correctly', async () => {
      // Mock performance.now to return predictable values
      const originalNow = performance.now;
      let timeCounter = 0;
      performance.now = vi.fn(() => {
        timeCounter += 10; // Each call advances by 10ms
        return timeCounter;
      });

      const scenario: BenchmarkScenario = {
        id: 'test-statistics',
        name: 'Statistics Test',
        description: 'Test statistics calculation',
        operation: ThemeOperation.THEME_SWITCH,
        expectedThreshold: 50,
        iterations: 3
      };

      const result = await benchmarkRunner.executeBenchmark(scenario);

      expect(result.durations).toEqual([10, 10, 10]); // Each iteration takes 10ms
      expect(result.averageDuration).toBe(10);
      expect(result.minDuration).toBe(10);
      expect(result.maxDuration).toBe(10);
      expect(result.standardDeviation).toBe(0);
      expect(result.passed).toBe(true); // 10ms < 50ms threshold

      // Restore original performance.now
      performance.now = originalNow;
    });
  });

  describe('executeSuite', () => {
    it('should execute a benchmark suite with multiple scenarios', async () => {
      const globalSetup = vi.fn();
      const globalCleanup = vi.fn();

      const suite: BenchmarkSuite = {
        id: 'test-suite',
        name: 'Test Suite',
        description: 'A test suite',
        scenarios: [
          {
            id: 'scenario-1',
            name: 'Scenario 1',
            description: 'First scenario',
            operation: ThemeOperation.THEME_SWITCH,
            expectedThreshold: 100,
            iterations: 1
          },
          {
            id: 'scenario-2',
            name: 'Scenario 2',
            description: 'Second scenario',
            operation: ThemeOperation.CSS_INJECT,
            expectedThreshold: 10,
            iterations: 1
          }
        ],
        globalSetup,
        globalCleanup
      };

      const result = await benchmarkRunner.executeSuite(suite);

      expect(globalSetup).toHaveBeenCalledOnce();
      expect(globalCleanup).toHaveBeenCalledOnce();
      expect(result.suite).toBe(suite);
      expect(result.results).toHaveLength(2);
      expect(result.statistics.totalScenarios).toBe(2);
      expect(result.statistics.totalDuration).toBeGreaterThan(0);
    });

    it('should calculate suite statistics correctly', async () => {
      const suite: BenchmarkSuite = {
        id: 'stats-suite',
        name: 'Statistics Suite',
        description: 'Suite for testing statistics',
        scenarios: [
          {
            id: 'passing-scenario',
            name: 'Passing Scenario',
            description: 'This should pass',
            operation: ThemeOperation.ELEMENT_CREATE,
            expectedThreshold: 1000, // Very lenient threshold
            iterations: 1
          },
          {
            id: 'failing-scenario',
            name: 'Failing Scenario',
            description: 'This should fail',
            operation: ThemeOperation.THEME_SWITCH,
            expectedThreshold: 0.001, // Very strict threshold
            iterations: 1
          }
        ]
      };

      const result = await benchmarkRunner.executeSuite(suite);

      expect(result.statistics.totalScenarios).toBe(2);
      expect(result.statistics.passedScenarios).toBe(1);
      expect(result.statistics.failedScenarios).toBe(1);
      expect(result.statistics.averageDuration).toBeGreaterThan(0);
    });
  });

  describe('detectRegression', () => {
    it('should detect no regression with insufficient data', () => {
      const result: BenchmarkResult = {
        scenario: {
          id: 'test-scenario',
          name: 'Test',
          description: 'Test',
          operation: ThemeOperation.THEME_SWITCH,
          expectedThreshold: 100,
          iterations: 1
        },
        durations: [50],
        averageDuration: 50,
        minDuration: 50,
        maxDuration: 50,
        standardDeviation: 0,
        passed: true,
        timestamp: Date.now(),
        errors: []
      };

      const regression = benchmarkRunner.detectRegression('test-scenario', result);

      expect(regression.isRegression).toBe(false);
      expect(regression.message).toContain('Insufficient historical data');
    });

    it('should detect regression when performance degrades significantly', async () => {
      // First, execute some benchmarks to build history
      const scenario: BenchmarkScenario = {
        id: 'regression-test',
        name: 'Regression Test',
        description: 'Test for regression detection',
        operation: ThemeOperation.THEME_SWITCH,
        expectedThreshold: 1000,
        iterations: 1
      };

      // Mock performance.now to simulate consistent fast performance
      const originalNow = performance.now;
      let timeCounter = 0;
      performance.now = vi.fn(() => {
        timeCounter += 10; // Each call advances by 10ms (fast performance)
        return timeCounter;
      });

      // Build some historical data
      for (let i = 0; i < 5; i++) {
        await benchmarkRunner.executeBenchmark(scenario);
      }

      // Now simulate a slow result
      timeCounter = 0;
      performance.now = vi.fn(() => {
        timeCounter += 100; // Each call advances by 100ms (slow performance)
        return timeCounter;
      });
      const slowResult = await benchmarkRunner.executeBenchmark(scenario);

      const regression = benchmarkRunner.detectRegression('regression-test', slowResult);

      expect(regression.isRegression).toBe(true);
      expect(regression.percentageChange).toBeGreaterThan(0.15); // More than 15% slower
      expect(regression.message).toContain('Performance regression detected');

      // Restore original performance.now
      performance.now = originalNow;
    });
  });

  describe('getHistoricalResults', () => {
    it('should return empty array for unknown scenario', () => {
      const results = benchmarkRunner.getHistoricalResults('unknown-scenario');
      expect(results).toEqual([]);
    });

    it('should return historical results for known scenario', async () => {
      const scenario: BenchmarkScenario = {
        id: 'historical-test',
        name: 'Historical Test',
        description: 'Test for historical results',
        operation: ThemeOperation.CSS_INJECT,
        expectedThreshold: 100,
        iterations: 1
      };

      await benchmarkRunner.executeBenchmark(scenario);
      await benchmarkRunner.executeBenchmark(scenario);

      const results = benchmarkRunner.getHistoricalResults('historical-test');
      expect(results).toHaveLength(2);
    });
  });

  describe('clearHistoricalResults', () => {
    it('should clear all historical results when no scenario specified', async () => {
      const scenario: BenchmarkScenario = {
        id: 'clear-test',
        name: 'Clear Test',
        description: 'Test for clearing results',
        operation: ThemeOperation.THEME_VALIDATE,
        expectedThreshold: 100,
        iterations: 1
      };

      await benchmarkRunner.executeBenchmark(scenario);
      expect(benchmarkRunner.getHistoricalResults('clear-test')).toHaveLength(1);

      benchmarkRunner.clearHistoricalResults();
      expect(benchmarkRunner.getHistoricalResults('clear-test')).toHaveLength(0);
    });

    it('should clear historical results for specific scenario', async () => {
      const scenario1: BenchmarkScenario = {
        id: 'clear-test-1',
        name: 'Clear Test 1',
        description: 'First test',
        operation: ThemeOperation.THEME_SWITCH,
        expectedThreshold: 100,
        iterations: 1
      };

      const scenario2: BenchmarkScenario = {
        id: 'clear-test-2',
        name: 'Clear Test 2',
        description: 'Second test',
        operation: ThemeOperation.CSS_INJECT,
        expectedThreshold: 100,
        iterations: 1
      };

      await benchmarkRunner.executeBenchmark(scenario1);
      await benchmarkRunner.executeBenchmark(scenario2);

      expect(benchmarkRunner.getHistoricalResults('clear-test-1')).toHaveLength(1);
      expect(benchmarkRunner.getHistoricalResults('clear-test-2')).toHaveLength(1);

      benchmarkRunner.clearHistoricalResults('clear-test-1');

      expect(benchmarkRunner.getHistoricalResults('clear-test-1')).toHaveLength(0);
      expect(benchmarkRunner.getHistoricalResults('clear-test-2')).toHaveLength(1);
    });
  });
});
