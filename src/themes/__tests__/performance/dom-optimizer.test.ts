/**
 * Tests for DOM Manipulation Optimizer
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  DOMOptimizer,
  DOMUpdateType,
  BatchUpdateConfig
} from '../../performance/dom-optimizer';
import { IThemeLogger } from '../../interfaces/core-interfaces';

// Mock implementations
class MockThemeLogger implements IThemeLogger {
  public logOperationStart = vi.fn();
  public logOperationComplete = vi.fn();
  public logError = vi.fn();
  public logUserAction = vi.fn();
  public createChild = vi.fn().mockReturnValue(this);
}

// Mock DOM elements
const createMockElement = () => {
  const element = {
    classList: {
      add: vi.fn(),
      remove: vi.fn(),
      contains: vi.fn()
    },
    style: {
      setProperty: vi.fn(),
      removeProperty: vi.fn()
    },
    setAttribute: vi.fn(),
    removeAttribute: vi.fn(),
    tagName: 'DIV'
  } as unknown as Element;
  return element;
};

// Mock IntersectionObserver
const mockObserverInstance = {
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
  takeRecords: vi.fn().mockReturnValue([]),
  callback: null as IntersectionObserverCallback | null,
  simulateIntersection: (entries: Partial<IntersectionObserverEntry>[]) => {
    if (mockObserverInstance.callback) {
      mockObserverInstance.callback(entries as IntersectionObserverEntry[], mockObserverInstance as unknown as IntersectionObserver);
    }
  }
};

const MockIntersectionObserver = vi.fn().mockImplementation((callback: IntersectionObserverCallback) => {
  mockObserverInstance.callback = callback;
  return mockObserverInstance;
});

describe('DOMOptimizer', () => {
  let optimizer: DOMOptimizer;
  let mockLogger: MockThemeLogger;
  let mockElement: Element;

  beforeEach(() => {
    mockLogger = new MockThemeLogger();
    mockElement = createMockElement();

    // Reset mocks
    vi.clearAllMocks();
    mockObserverInstance.observe.mockClear();
    mockObserverInstance.unobserve.mockClear();
    mockObserverInstance.disconnect.mockClear();
    mockObserverInstance.callback = null;

    // Mock global IntersectionObserver
    global.IntersectionObserver = MockIntersectionObserver as unknown as typeof IntersectionObserver;

    // Mock requestAnimationFrame and cancelAnimationFrame
    global.requestAnimationFrame = vi.fn((callback) => {
      setTimeout(callback, 16);
      return 1;
    });
    global.cancelAnimationFrame = vi.fn();

    optimizer = new DOMOptimizer(mockLogger);
  });

  afterEach(() => {
    optimizer.dispose();
    vi.clearAllMocks();
  });

  describe('element registration', () => {
    it('should register an element for tracking', () => {
      const elementId = optimizer.registerElement(mockElement, 'button');

      expect(elementId).toMatch(/^element_\d+_[a-z0-9]+$/);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_element_registered',
        undefined,
        expect.objectContaining({
          elementId,
          role: 'button',
          tagName: 'DIV'
        })
      );

      const stats = optimizer.getStatistics();
      expect(stats.trackedElements).toBe(1);
    });

    it('should unregister an element', () => {
      const elementId = optimizer.registerElement(mockElement, 'button');

      optimizer.unregisterElement(elementId);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_element_unregistered',
        undefined,
        expect.objectContaining({
          elementId,
          role: 'button',
          updateCount: 0
        })
      );

      const stats = optimizer.getStatistics();
      expect(stats.trackedElements).toBe(0);
    });

    it('should handle unregistering non-existent element gracefully', () => {
      optimizer.unregisterElement('non-existent-id');

      // Should not throw or log anything
      expect(mockLogger.logUserAction).not.toHaveBeenCalledWith(
        'dom_element_unregistered',
        expect.anything(),
        expect.anything()
      );
    });
  });

  describe('DOM update operations', () => {
    it('should queue a class addition operation', () => {
      const operationId = optimizer.addClass(mockElement, 'test-class', 75);

      expect(operationId).toMatch(/^op_\d+_\d+$/);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_update_queued',
        undefined,
        expect.objectContaining({
          operationId,
          type: DOMUpdateType.CLASS_ADD,
          priority: 75,
          queueSize: 1
        })
      );

      const stats = optimizer.getStatistics();
      expect(stats.pendingOperations).toBe(1);
    });

    it('should queue a class removal operation', () => {
      const operationId = optimizer.removeClass(mockElement, 'test-class');

      expect(operationId).toMatch(/^op_\d+_\d+$/);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_update_queued',
        undefined,
        expect.objectContaining({
          type: DOMUpdateType.CLASS_REMOVE,
          priority: 50 // Default priority
        })
      );
    });

    it('should queue a style property operation', () => {
      const operationId = optimizer.setStyle(mockElement, 'color', 'red', 60);

      expect(operationId).toMatch(/^op_\d+_\d+$/);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_update_queued',
        undefined,
        expect.objectContaining({
          type: DOMUpdateType.STYLE_SET,
          priority: 60
        })
      );
    });

    it('should queue an attribute operation', () => {
      const operationId = optimizer.setAttribute(mockElement, 'data-test', 'value');

      expect(operationId).toMatch(/^op_\d+_\d+$/);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_update_queued',
        undefined,
        expect.objectContaining({
          type: DOMUpdateType.ATTRIBUTE_SET
        })
      );
    });
  });

  describe('batch processing', () => {
    it('should process batched operations', async () => {
      // Queue multiple operations
      optimizer.addClass(mockElement, 'class1', 100);
      optimizer.removeClass(mockElement, 'class2', 90);
      optimizer.setStyle(mockElement, 'color', 'blue', 80);

      expect(optimizer.getStatistics().pendingOperations).toBe(3);

      // Force immediate processing
      await optimizer.flushUpdates();

      // Operations should be processed
      expect(mockElement.classList.add).toHaveBeenCalledWith('class1');
      expect(mockElement.classList.remove).toHaveBeenCalledWith('class2');
      expect((mockElement as unknown as HTMLElement).style.setProperty).toHaveBeenCalledWith('color', 'blue');

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_batch_update_start',
        undefined,
        expect.objectContaining({
          operationCount: 3
        })
      );

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_batch_update_complete',
        undefined,
        expect.objectContaining({
          operationCount: 3
        })
      );
    });

    it('should sort operations by priority', async () => {
      // Queue operations with different priorities
      optimizer.addClass(mockElement, 'low', 10);
      optimizer.addClass(mockElement, 'high', 90);
      optimizer.addClass(mockElement, 'medium', 50);

      // Force immediate processing
      await optimizer.flushUpdates();

      // High priority should be processed first
      const addCalls = (mockElement.classList.add as unknown as { mock: { calls: string[][] } }).mock.calls;
      expect(addCalls[0][0]).toBe('high');
      expect(addCalls[1][0]).toBe('medium');
      expect(addCalls[2][0]).toBe('low');
    });

    it('should handle batch processing errors gracefully', async () => {
      // Mock an error in DOM operation
      (mockElement.classList.add as unknown as ReturnType<typeof vi.fn>).mockImplementation(() => {
        throw new Error('DOM operation failed');
      });

      optimizer.addClass(mockElement, 'test-class');

      // Should handle the error
      await expect(optimizer.flushUpdates()).rejects.toThrow('DOM operation failed');

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_operation_error',
        undefined,
        expect.objectContaining({
          error: 'DOM operation failed'
        })
      );
    });
  });

  describe('flush updates', () => {
    it('should immediately process all pending operations', async () => {
      // Queue operations
      optimizer.addClass(mockElement, 'immediate1');
      optimizer.addClass(mockElement, 'immediate2');

      expect(optimizer.getStatistics().pendingOperations).toBe(2);

      // Flush immediately
      await optimizer.flushUpdates();

      expect(optimizer.getStatistics().pendingOperations).toBe(0);
      expect(mockElement.classList.add).toHaveBeenCalledWith('immediate1');
      expect(mockElement.classList.add).toHaveBeenCalledWith('immediate2');
    });

    it('should handle empty queue gracefully', async () => {
      await optimizer.flushUpdates();

      // Should not throw or cause issues
      expect(optimizer.getStatistics().pendingOperations).toBe(0);
    });
  });

  describe('visibility tracking', () => {
    it('should track element visibility with IntersectionObserver', () => {
      optimizer.registerElement(mockElement, 'button');

      // Verify intersection observer was set up
      expect(MockIntersectionObserver).toHaveBeenCalled();
      expect(mockObserverInstance.observe).toHaveBeenCalledWith(mockElement);

      // Simulate visibility change
      mockObserverInstance.simulateIntersection([
        {
          target: mockElement,
          isIntersecting: false
        }
      ]);

      // Element should now be tracked as not visible
      const stats = optimizer.getStatistics();
      expect(stats.visibleElements).toBe(0);
    });
  });

  describe('configuration', () => {
    it('should use custom configuration', () => {
      const customConfig: Partial<BatchUpdateConfig> = {
        maxWaitTime: 32,
        maxBatchSize: 50,
        useAnimationFrame: false,
        prioritizeVisible: false
      };

      const customOptimizer = new DOMOptimizer(mockLogger, customConfig);

      // Configuration should be applied (we can't directly test private config,
      // but we can test behavior that depends on it)
      expect(customOptimizer).toBeDefined();

      customOptimizer.dispose();
    });
  });

  describe('statistics', () => {
    it('should provide accurate statistics', async () => {
      // Register elements
      optimizer.registerElement(mockElement, 'button');
      optimizer.registerElement(createMockElement(), 'input');

      // Queue operations
      optimizer.addClass(mockElement, 'test1');
      optimizer.addClass(mockElement, 'test2');

      let stats = optimizer.getStatistics();
      expect(stats.trackedElements).toBe(2);
      expect(stats.pendingOperations).toBe(2);
      expect(stats.totalUpdates).toBe(0);
      expect(stats.visibleElements).toBe(2); // Assume visible by default

      // Process operations
      await optimizer.flushUpdates();

      stats = optimizer.getStatistics();
      expect(stats.pendingOperations).toBe(0);
      expect(stats.totalUpdates).toBe(2);
      expect(stats.averageUpdatesPerElement).toBe(1);
    });
  });

  describe('disposal', () => {
    it('should clean up resources on disposal', () => {
      optimizer.registerElement(mockElement, 'button');

      optimizer.dispose();

      expect(mockObserverInstance.disconnect).toHaveBeenCalled();
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'dom_optimizer_disposed',
        undefined,
        expect.objectContaining({
          timestamp: expect.any(Number)
        })
      );

      const stats = optimizer.getStatistics();
      expect(stats.trackedElements).toBe(0);
      expect(stats.pendingOperations).toBe(0);
    });
  });

  describe('edge cases', () => {
    it('should handle operations on untracked elements', async () => {
      const untrackedElement = createMockElement();

      optimizer.addClass(untrackedElement, 'test-class');
      await optimizer.flushUpdates();

      // Should still work, just without tracking benefits
      expect(untrackedElement.classList.add).toHaveBeenCalledWith('test-class');
    });

    it('should handle missing IntersectionObserver gracefully', () => {
      // Remove IntersectionObserver
      const originalIntersectionObserver = global.IntersectionObserver;
      delete (global as unknown as { IntersectionObserver?: unknown }).IntersectionObserver;

      const optimizerWithoutObserver = new DOMOptimizer(mockLogger);
      const elementId = optimizerWithoutObserver.registerElement(mockElement, 'button');

      // Should still work without visibility tracking
      expect(elementId).toBeDefined();

      optimizerWithoutObserver.dispose();

      // Restore IntersectionObserver
      global.IntersectionObserver = originalIntersectionObserver;
    });

    it('should handle operations with missing data gracefully', async () => {
      // Queue operation with incomplete data
      optimizer.queueUpdate({
        type: DOMUpdateType.CLASS_ADD,
        element: mockElement,
        data: {}, // Missing className
        priority: 50
      });

      await optimizer.flushUpdates();

      // Should not throw, but also should not call DOM methods with undefined
      expect(mockElement.classList.add).not.toHaveBeenCalled();
    });
  });
});
