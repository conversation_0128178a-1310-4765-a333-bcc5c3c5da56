/**
 * Tests for CSS Performance Optimization
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  CSSOptimizer,
  BatchCSSInjectionStrategy,
  IndividualCSSInjectionStrategy,
  StylesheetCSSInjectionStrategy,
  createCSSOptimizationStrategies,
  measureCSSInjectionPerformance
} from '../../performance/css-optimization';
import { CSSVariableMapping, ThemeDefinition } from '../../theme-types';
import { IThemeLogger } from '../../interfaces/core-interfaces';
import { LIGHT_THEME } from '../../theme-config';

// Mock implementations
class MockThemeLogger implements IThemeLogger {
  public logOperationStart = vi.fn();
  public logOperationComplete = vi.fn();
  public logError = vi.fn();
  public logUserAction = vi.fn();
  public createChild = vi.fn().mockReturnValue(this);
}

// Mock DOM elements
const createMockElement = () => {
  const element = {
    style: {
      cssText: '',
      setProperty: vi.fn(),
      getPropertyValue: vi.fn()
    },
    tagName: 'DIV'
  } as unknown as HTMLElement;
  return element;
};

// Mock document
const mockDocument = {
  documentElement: createMockElement(),
  createElement: vi.fn(() => ({
    id: '',
    sheet: {
      cssRules: [],
      insertRule: vi.fn(),
      deleteRule: vi.fn()
    }
  })),
  head: {
    appendChild: vi.fn()
  }
};

describe('CSS Injection Strategies', () => {
  let mockElement: HTMLElement;

  beforeEach(() => {
    mockElement = createMockElement();
    // Mock global document
    global.document = mockDocument as any;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('BatchCSSInjectionStrategy', () => {
    let strategy: BatchCSSInjectionStrategy;

    beforeEach(() => {
      strategy = new BatchCSSInjectionStrategy();
    });

    it('should handle multiple variables', () => {
      expect(strategy.canHandle(10)).toBe(true);
      expect(strategy.canHandle(3)).toBe(false);
    });

    it('should inject variables as batch CSS text', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' },
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      await strategy.inject(variables, mockElement);

      expect(mockElement.style.cssText).toContain('--primary: #007bff;');
      expect(mockElement.style.cssText).toContain('--secondary: #6c757d;');
    });
  });

  describe('IndividualCSSInjectionStrategy', () => {
    let strategy: IndividualCSSInjectionStrategy;

    beforeEach(() => {
      strategy = new IndividualCSSInjectionStrategy();
    });

    it('should handle few variables', () => {
      expect(strategy.canHandle(3)).toBe(true);
      expect(strategy.canHandle(10)).toBe(false);
    });

    it('should inject variables individually', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' },
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      await strategy.inject(variables, mockElement);

      expect(mockElement.style.setProperty).toHaveBeenCalledWith('--primary', '#007bff');
      expect(mockElement.style.setProperty).toHaveBeenCalledWith('--secondary', '#6c757d');
    });
  });

  describe('StylesheetCSSInjectionStrategy', () => {
    let strategy: StylesheetCSSInjectionStrategy;

    beforeEach(() => {
      strategy = new StylesheetCSSInjectionStrategy();
    });

    it('should handle many variables for document-level injection', () => {
      expect(strategy.canHandle(25)).toBe(true);
      expect(strategy.canHandle(25, mockElement)).toBe(false); // Not for specific elements
      expect(strategy.canHandle(10)).toBe(false);
    });

    it('should create stylesheet and inject variables', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' },
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      await strategy.inject(variables);

      expect(mockDocument.createElement).toHaveBeenCalledWith('style');
      expect(mockDocument.head.appendChild).toHaveBeenCalled();
    });
  });
});

describe('CSSOptimizer', () => {
  let optimizer: CSSOptimizer;
  let mockLogger: MockThemeLogger;

  beforeEach(() => {
    mockLogger = new MockThemeLogger();
    optimizer = new CSSOptimizer(mockLogger);
    global.document = mockDocument as any;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('injectThemeVariables', () => {
    it('should inject theme variables successfully', async () => {
      const theme: ThemeDefinition = {
        ...LIGHT_THEME,
        colors: {
          ...LIGHT_THEME.colors,
          primary: '#007bff',
          secondary: '#6c757d'
        }
      };

      await optimizer.injectThemeVariables(theme);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_optimization_start',
        theme.id,
        expect.objectContaining({
          targetElement: 'document',
          colorCount: expect.any(Number)
        })
      );

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_optimization_complete',
        theme.id,
        expect.objectContaining({
          variableCount: expect.any(Number),
          targetElement: 'document'
        })
      );
    });

    it('should handle errors during injection', async () => {
      const theme: ThemeDefinition = {
        ...LIGHT_THEME,
        colors: {
          ...LIGHT_THEME.colors,
          primary: '#007bff'
        }
      };

      // Mock an error in the injection process
      const originalInject = optimizer.injectVariables;
      optimizer.injectVariables = vi.fn().mockRejectedValue(new Error('Injection failed'));

      await expect(optimizer.injectThemeVariables(theme)).rejects.toThrow('Injection failed');

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_optimization_error',
        theme.id,
        expect.objectContaining({
          error: 'Injection failed'
        })
      );

      // Restore original method
      optimizer.injectVariables = originalInject;
    });
  });

  describe('injectVariables', () => {
    it('should skip injection when no variables have changed', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' }
      ];

      // First injection
      await optimizer.injectVariables(variables);

      // Clear mock calls
      mockLogger.logUserAction.mockClear();

      // Second injection with same variables
      await optimizer.injectVariables(variables);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_optimization_skipped',
        undefined,
        expect.objectContaining({
          reason: 'no_changes',
          totalVariables: 1
        })
      );
    });

    it('should inject only changed variables', async () => {
      const variables1: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' },
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      const variables2: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' }, // Same value
        { name: 'secondary', value: '#28a745', category: 'core' } // Changed value
      ];

      // First injection
      await optimizer.injectVariables(variables1);

      // Clear mock calls
      mockLogger.logUserAction.mockClear();

      // Second injection with one changed variable
      await optimizer.injectVariables(variables2);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_injected',
        undefined,
        expect.objectContaining({
          variableCount: 1, // Only one variable changed
          totalVariables: 2
        })
      );
    });

    it('should select appropriate strategy based on variable count', async () => {
      // Test with few variables (should use individual strategy)
      const fewVariables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' }
      ];

      await optimizer.injectVariables(fewVariables);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_injected',
        undefined,
        expect.objectContaining({
          strategy: 'IndividualCSSInjectionStrategy'
        })
      );

      // Clear mock calls
      mockLogger.logUserAction.mockClear();

      // Test with many variables (should use batch strategy)
      const manyVariables: CSSVariableMapping[] = Array.from({ length: 10 }, (_, i) => ({
        name: `var${i}`,
        value: `#${i.toString(16).padStart(6, '0')}`,
        category: 'core' as const
      }));

      await optimizer.injectVariables(manyVariables);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_injected',
        undefined,
        expect.objectContaining({
          strategy: 'BatchCSSInjectionStrategy'
        })
      );
    });
  });

  describe('updateVariablesDebounced', () => {
    it('should debounce multiple rapid updates', async () => {
      const variables1: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' }
      ];

      const variables2: CSSVariableMapping[] = [
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      // Mock setTimeout to capture the debounced function
      let debouncedCallback: (() => Promise<void>) | null = null;
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = vi.fn((callback: (...args: unknown[]) => unknown, _delay: number) => {
        debouncedCallback = callback as () => Promise<void>;
        return 1 as unknown as NodeJS.Timeout; // Return a timer ID
      }) as unknown as typeof setTimeout;

      // Make rapid updates
      optimizer.updateVariablesDebounced(variables1, undefined, 50);
      optimizer.updateVariablesDebounced(variables2, undefined, 50);

      // Should not have injected yet
      expect(mockLogger.logUserAction).not.toHaveBeenCalledWith(
        'css_variables_injected',
        expect.anything(),
        expect.anything()
      );

      // Execute the debounced callback
      if (debouncedCallback) {
        await (debouncedCallback as () => Promise<void>)();
      }

      // Should have injected both variables in one operation
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_injected',
        undefined,
        expect.objectContaining({
          variableCount: 2
        })
      );

      // Restore original setTimeout
      global.setTimeout = originalSetTimeout;
    });
  });

  describe('cache management', () => {
    it('should cache and retrieve variables', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' }
      ];

      await optimizer.injectVariables(variables);

      const cachedValue = optimizer.getCachedVariable('primary');
      expect(cachedValue).toBe('#007bff');

      const nonExistentValue = optimizer.getCachedVariable('nonexistent');
      expect(nonExistentValue).toBeUndefined();
    });

    it('should clear cache', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' }
      ];

      await optimizer.injectVariables(variables);
      expect(optimizer.getCachedVariable('primary')).toBe('#007bff');

      optimizer.clearCache();
      expect(optimizer.getCachedVariable('primary')).toBeUndefined();

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_cache_cleared',
        undefined,
        expect.objectContaining({
          timestamp: expect.any(Number)
        })
      );
    });

    it('should provide cache statistics', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' },
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      await optimizer.injectVariables(variables);

      // Access cached variables to increase access count
      optimizer.getCachedVariable('primary');
      optimizer.getCachedVariable('primary');
      optimizer.getCachedVariable('secondary');

      const stats = optimizer.getCacheStats();

      expect(stats.size).toBe(2);
      expect(stats.totalAccesses).toBe(3);
      expect(stats.averageAccesses).toBe(1.5);
      expect(stats.oldestEntry).toBeGreaterThan(0);
      expect(stats.newestEntry).toBeGreaterThan(0);
    });
  });

  describe('strategy registration', () => {
    it('should register custom strategies', () => {
      const customStrategy = {
        name: 'CustomStrategy',
        priority: 100,
        canHandle: vi.fn().mockReturnValue(true),
        inject: vi.fn().mockResolvedValue(undefined)
      };

      optimizer.registerStrategy(customStrategy);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_strategy_registered',
        undefined,
        expect.objectContaining({
          strategyName: 'CustomStrategy',
          priority: 100
        })
      );
    });
  });
});

describe('Utility Functions', () => {
  beforeEach(() => {
    global.document = mockDocument as unknown as Document;
  });

  describe('createCSSOptimizationStrategies', () => {
    it('should create all optimization strategies', () => {
      const strategies = createCSSOptimizationStrategies();

      expect(strategies).toHaveLength(3);
      expect(strategies.map(s => s.name)).toEqual([
        'StylesheetCSSInjectionStrategy',
        'BatchCSSInjectionStrategy',
        'IndividualCSSInjectionStrategy'
      ]);
    });
  });

  describe('measureCSSInjectionPerformance', () => {
    it('should measure CSS injection performance', async () => {
      const variables: CSSVariableMapping[] = [
        { name: 'primary', value: '#007bff', category: 'core' },
        { name: 'secondary', value: '#6c757d', category: 'core' }
      ];

      const result = await measureCSSInjectionPerformance(variables);

      expect(result.duration).toBeGreaterThanOrEqual(0);
      expect(result.variableCount).toBe(2);
      expect(result.strategy).toBe('BatchCSSInjectionStrategy');
    });
  });
});
