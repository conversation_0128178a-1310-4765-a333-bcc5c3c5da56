/**
 * Enhanced ThemeManager Tests
 * Comprehensive unit tests for the enhanced ThemeManager class
 * covering all public methods, error handling, and SOLID principles compliance
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ThemeManager } from '../theme-manager';
import {
  <PERSON>hemeLogger,
  IThemeErrorHandler,
  IPerformanceMonitor,
  IThemeConfigManager,
  ICSSVariableInjector,
  IThemeStorage,
  IThemeValidator
} from '../interfaces/core-interfaces';
import {
  ThemeDefinition,
  ThemeOperation
} from '../theme-types';
import { LIGHT_THEME, DARK_THEME } from '../theme-config';

// Mock implementations for testing
class MockThemeConfigManager implements IThemeConfigManager {
  private themes = new Map<string, ThemeDefinition>();

  constructor() {
    this.themes.set('light', LIGHT_THEME);
    this.themes.set('dark', DARK_THEME);
  }

  saveTheme = vi.fn().mockImplementation(async (theme: ThemeDefinition) => {
    this.themes.set(theme.id, theme);
  });

  async loadTheme(themeId: string): Promise<ThemeDefinition | null> {
    return this.themes.get(themeId) || null;
  }

  async getAvailableThemes(): Promise<ThemeDefinition[]> {
    return Array.from(this.themes.values());
  }

  async registerTheme(theme: ThemeDefinition): Promise<void> {
    this.themes.set(theme.id, theme);
  }

  async unregisterTheme(themeId: string): Promise<void> {
    this.themes.delete(themeId);
  }

  getBuiltInThemes(): ThemeDefinition[] {
    return [LIGHT_THEME, DARK_THEME];
  }
}

class MockCSSVariableInjector implements ICSSVariableInjector {
  injectVariables = vi.fn().mockResolvedValue(undefined);
  updateVariables = vi.fn().mockResolvedValue(undefined);
  removeVariables = vi.fn().mockResolvedValue(undefined);
  generateVariableMappings = vi.fn().mockReturnValue([]);
  getCurrentVariables = vi.fn().mockReturnValue({});
}

class MockThemeStorage implements IThemeStorage {
  private storage = new Map<string, ThemeDefinition>();

  async store(key: string, theme: ThemeDefinition): Promise<void> {
    this.storage.set(key, theme);
  }

  async retrieve(key: string): Promise<ThemeDefinition | null> {
    return this.storage.get(key) || null;
  }

  remove = vi.fn().mockImplementation(async (key: string) => {
    this.storage.delete(key);
  });

  async listKeys(): Promise<string[]> {
    return Array.from(this.storage.keys());
  }

  isAvailable(): boolean {
    return true;
  }

  async getStorageInfo(): Promise<{ used: number; available: number; total: number }> {
    return { used: 0, available: 1000000, total: 1000000 };
  }
}

class MockThemeValidator implements IThemeValidator {
  validateTheme = vi.fn().mockResolvedValue({
    isValid: true,
    errors: [],
    warnings: []
  });

  validateColorFormat = vi.fn().mockReturnValue(true);
  validateContrastRatio = vi.fn().mockResolvedValue({
    ratio: 4.5,
    meetsWCAG_AA: true,
    meetsWCAG_AAA: false
  });
  logValidation = vi.fn();
}

class MockThemeLogger implements IThemeLogger {
  logOperationStart = vi.fn();
  logOperationComplete = vi.fn();
  logError = vi.fn();
  logUserAction = vi.fn();
  createChild = vi.fn().mockReturnValue(this);
}

class MockThemeErrorHandler implements IThemeErrorHandler {
  handleError = vi.fn().mockResolvedValue(true);
  wrapAsync = vi.fn().mockImplementation(async (fn) => await fn());
  wrap = vi.fn().mockImplementation((fn) => fn());
}

class MockPerformanceMonitor implements IPerformanceMonitor {
  measureOperation = vi.fn().mockImplementation(async (_operation, fn) => await fn());
  measureOperationSync = vi.fn().mockImplementation((_operation, fn) => fn());
  setPerformanceThreshold = vi.fn();
  getPerformanceStats = vi.fn().mockReturnValue({});
  getMetrics = vi.fn().mockReturnValue({});
  clearMetrics = vi.fn();
}

// Mock DOM elements
const createMockElement = (tagName: string = 'div'): HTMLElement => ({
  tagName: tagName.toUpperCase(),
  classList: {
    add: vi.fn(),
    remove: vi.fn(),
    contains: vi.fn(),
    toggle: vi.fn()
  },
  style: {
    setProperty: vi.fn(),
    removeProperty: vi.fn()
  },
  setAttribute: vi.fn(),
  removeAttribute: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
} as any);

const createMockDocument = (): Document => {
  const mockElement = createMockElement('html');
  const mockBody = createMockElement('body');
  const mockSwitcher = createMockElement('select') as HTMLSelectElement;
  mockSwitcher.value = 'light';

  return {
    documentElement: mockElement,
    body: mockBody,
    getElementById: vi.fn().mockImplementation((id: string) =>
      id === 'theme-switcher' ? mockSwitcher : null
    ),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn().mockReturnValue(true)
  } as any;
};

const createMockLocalStorage = (): Storage => {
  const storage = new Map<string, string>();
  return {
    getItem: vi.fn().mockImplementation((key: string) => storage.get(key) || null),
    setItem: vi.fn().mockImplementation((key: string, value: string) => storage.set(key, value)),
    removeItem: vi.fn().mockImplementation((key: string) => storage.delete(key)),
    clear: vi.fn().mockImplementation(() => storage.clear()),
    key: vi.fn(),
    get length() { return storage.size; }
  };
};

describe('Enhanced ThemeManager', () => {
  let themeManager: ThemeManager;
  let mockConfigManager: MockThemeConfigManager;
  let mockCSSInjector: MockCSSVariableInjector;
  let mockStorage: MockThemeStorage;
  let mockValidator: MockThemeValidator;
  let mockLogger: MockThemeLogger;
  let mockErrorHandler: MockThemeErrorHandler;
  let mockPerformanceMonitor: MockPerformanceMonitor;
  let mockDocument: Document;
  let mockWindow: Window;
  let mockLocalStorage: Storage;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Create shared localStorage mock
    mockLocalStorage = createMockLocalStorage();

    // Create mock dependencies
    mockConfigManager = new MockThemeConfigManager();
    mockCSSInjector = new MockCSSVariableInjector();
    mockStorage = new MockThemeStorage();
    mockValidator = new MockThemeValidator();
    mockLogger = new MockThemeLogger();
    mockErrorHandler = new MockThemeErrorHandler();
    mockPerformanceMonitor = new MockPerformanceMonitor();
    mockDocument = createMockDocument();

    // Create mock window with shared localStorage
    mockWindow = {
      matchMedia: vi.fn().mockReturnValue({
        matches: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      }),
      dispatchEvent: vi.fn().mockReturnValue(true),
      localStorage: mockLocalStorage
    } as any;

    // Mock localStorage globally
    Object.defineProperty(global, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });

    // Create ThemeManager instance
    themeManager = new ThemeManager(
      mockConfigManager,
      mockCSSInjector,
      mockStorage,
      mockValidator,
      mockDocument,
      mockWindow,
      mockLogger,
      mockErrorHandler,
      mockPerformanceMonitor
    );
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Constructor and Initialization', () => {
    it('should initialize with built-in themes registered', () => {
      // Built-in themes are now registered synchronously without validation
      expect(mockValidator.validateTheme).not.toHaveBeenCalledWith(LIGHT_THEME);
      expect(mockValidator.validateTheme).not.toHaveBeenCalledWith(DARK_THEME);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'enhanced_theme_manager_init',
        undefined,
        expect.objectContaining({
          builtInThemes: ['light', 'dark'],
          themeKey: 'featherjs-current-theme'
        })
      );
    });

    it('should log built-in theme registration', () => {
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'builtin_themes_registered',
        undefined,
        expect.objectContaining({
          themes: ['light', 'dark']
        })
      );
    });
  });

  describe('getCurrentTheme() and getCurrentThemeId()', () => {
    it('should return null when no theme is set', () => {
      expect(themeManager.getCurrentTheme()).toBeNull();
      expect(themeManager.getCurrentThemeId()).toBeNull();
    });

    it('should return current theme after setting one', async () => {
      await themeManager.setTheme('light');

      const currentTheme = themeManager.getCurrentTheme();
      expect(currentTheme).toBeDefined();
      expect(currentTheme?.id).toBe('light');
      expect(themeManager.getCurrentThemeId()).toBe('light');
    });

    it('should return theme definition object with all properties', async () => {
      await themeManager.setTheme('dark');

      const currentTheme = themeManager.getCurrentTheme();
      expect(currentTheme).toEqual(DARK_THEME);
      expect(currentTheme?.name).toBe(DARK_THEME.name);
      expect(currentTheme?.colors).toEqual(DARK_THEME.colors);
      expect(currentTheme?.isBuiltIn).toBe(true);
    });
  });

  describe('applyThemeToElement()', () => {
    let mockElement: HTMLElement;

    beforeEach(() => {
      mockElement = createMockElement('div');
    });

    it('should skip application when no theme is available', async () => {
      await themeManager.applyThemeToElement(mockElement);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_apply_element_skipped',
        undefined,
        expect.objectContaining({
          reason: 'No theme available',
          elementType: 'DIV'
        })
      );
      expect(mockElement.classList.add).not.toHaveBeenCalled();
    });

    it('should apply current theme to element', async () => {
      await themeManager.setTheme('light');
      await themeManager.applyThemeToElement(mockElement);

      expect(mockElement.classList.remove).toHaveBeenCalledWith('theme-light', 'theme-dark', 'dark');
      expect(mockElement.classList.add).toHaveBeenCalledWith('theme-light');
      expect(mockCSSInjector.injectVariables).toHaveBeenCalledWith(LIGHT_THEME, mockElement);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_applied_to_element',
        'light',
        expect.objectContaining({
          elementType: 'DIV',
          themeOverride: false
        })
      );
    });

    it('should apply theme override to element', async () => {
      const customTheme: ThemeDefinition = {
        ...DARK_THEME,
        id: 'custom',
        name: 'Custom Theme'
      };

      await themeManager.applyThemeToElement(mockElement, customTheme);

      expect(mockElement.classList.add).toHaveBeenCalledWith('theme-custom');
      expect(mockCSSInjector.injectVariables).toHaveBeenCalledWith(customTheme, mockElement);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_applied_to_element',
        'custom',
        expect.objectContaining({
          themeOverride: true
        })
      );
    });

    it('should add dark class for dark theme', async () => {
      await themeManager.setTheme('dark');
      await themeManager.applyThemeToElement(mockElement);

      expect(mockElement.classList.add).toHaveBeenCalledWith('theme-dark');
      expect(mockElement.classList.add).toHaveBeenCalledWith('dark');
    });

    it('should handle different element types', async () => {
      const buttonElement = createMockElement('button');
      await themeManager.setTheme('light');
      await themeManager.applyThemeToElement(buttonElement);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_applied_to_element',
        'light',
        expect.objectContaining({
          elementType: 'BUTTON'
        })
      );
    });

    it('should use error handler when available', async () => {
      const errorTheme: ThemeDefinition = {
        ...LIGHT_THEME,
        id: 'error-theme'
      };

      mockCSSInjector.injectVariables.mockRejectedValueOnce(new Error('CSS injection failed'));

      await expect(themeManager.applyThemeToElement(mockElement, errorTheme)).rejects.toThrow();
      expect(mockErrorHandler.wrapAsync).toHaveBeenCalled();
    });
  });

  describe('init()', () => {
    it('should initialize with default theme when no stored preference', async () => {
      await themeManager.init('dark');

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_preference_loaded',
        'dark',
        expect.objectContaining({
          source: 'provided_default'
        })
      );
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_init_complete',
        undefined,
        expect.objectContaining({
          defaultThemeId: 'dark',
          currentTheme: 'dark',
          success: true
        })
      );
    });

    it('should use stored preference over default', async () => {
      mockLocalStorage.setItem('featherjs-current-theme', 'light');

      await themeManager.init('dark');

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_preference_loaded',
        'light',
        expect.objectContaining({
          source: 'stored_preference'
        })
      );
    });

    it('should use system preference when no stored preference or default', async () => {
      mockWindow.matchMedia = vi.fn().mockReturnValue({
        matches: true,
        addEventListener: vi.fn()
      });

      await themeManager.init();

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_preference_loaded',
        'dark',
        expect.objectContaining({
          source: 'system_preference'
        })
      );
    });

    it('should set up theme switcher when present', async () => {
      const mockSwitcher = mockDocument.getElementById('theme-switcher') as HTMLSelectElement;

      await themeManager.init('light');

      expect(mockSwitcher.value).toBe('light');
      expect(mockSwitcher.addEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });

    it('should use performance monitor when available', async () => {
      await themeManager.init('light');

      expect(mockPerformanceMonitor.measureOperation).toHaveBeenCalledWith(
        ThemeOperation.THEME_LOAD,
        expect.any(Function)
      );
    });

    it('should handle initialization errors', async () => {
      const initError = new Error('Initialization failed');
      mockCSSInjector.injectVariables.mockRejectedValueOnce(initError);

      await expect(themeManager.init('light')).rejects.toThrow();

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_init_failed',
        undefined,
        expect.objectContaining({
          error: 'Initialization failed'
        })
      );
      expect(mockErrorHandler.wrapAsync).toHaveBeenCalled();
    });

    it('should handle invalid default theme gracefully', async () => {
      await themeManager.init('nonexistent');

      // Should fall back to system preference
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_preference_loaded',
        'light', // system preference default (dark: false)
        expect.objectContaining({
          source: 'system_preference'
        })
      );
    });
  });

  describe('registerTheme() and unregisterTheme()', () => {
    const customTheme: ThemeDefinition = {
      ...LIGHT_THEME,
      id: 'custom',
      name: 'Custom Theme',
      isBuiltIn: false,
      version: '1.0.0'
    };

    it('should register a valid custom theme', async () => {
      await themeManager.registerTheme(customTheme);

      expect(mockValidator.validateTheme).toHaveBeenCalledWith(customTheme);
      expect(mockConfigManager.saveTheme).toHaveBeenCalledWith(customTheme);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_registered',
        'custom',
        expect.objectContaining({
          isBuiltIn: false,
          version: '1.0.0'
        })
      );
    });

    it('should reject invalid themes', async () => {
      mockValidator.validateTheme.mockResolvedValueOnce({
        isValid: false,
        errors: ['Invalid color format'],
        warnings: []
      });

      await expect(themeManager.registerTheme(customTheme)).rejects.toThrow(
        'Theme validation failed: Invalid color format'
      );
    });

    it('should reject duplicate theme IDs', async () => {
      await themeManager.registerTheme(customTheme);

      await expect(themeManager.registerTheme(customTheme)).rejects.toThrow(
        "Theme with ID 'custom' is already registered"
      );
    });

    it('should not save built-in themes to storage', async () => {
      const builtInTheme: ThemeDefinition = {
        ...LIGHT_THEME,
        id: 'builtin-test',
        isBuiltIn: true
      };

      await themeManager.registerTheme(builtInTheme);

      expect(mockConfigManager.saveTheme).not.toHaveBeenCalledWith(builtInTheme);
    });

    it('should unregister custom themes', async () => {
      await themeManager.registerTheme(customTheme);
      await themeManager.unregisterTheme('custom');

      expect(mockStorage.remove).toHaveBeenCalledWith('custom');
      expect(mockLogger.logUserAction).toHaveBeenCalledWith('theme_unregistered', 'custom');
    });

    it('should not allow unregistering built-in themes', async () => {
      await expect(themeManager.unregisterTheme('light')).rejects.toThrow(
        "Cannot unregister built-in theme 'light'"
      );
    });

    it('should not allow unregistering non-existent themes', async () => {
      await expect(themeManager.unregisterTheme('nonexistent')).rejects.toThrow(
        "Theme 'nonexistent' is not registered"
      );
    });

    it('should switch to default theme when unregistering current theme', async () => {
      await themeManager.registerTheme(customTheme);
      await themeManager.setTheme('custom');

      expect(themeManager.getCurrentThemeId()).toBe('custom');

      await themeManager.unregisterTheme('custom');

      expect(themeManager.getCurrentThemeId()).toBe('light');
    });
  });

  describe('getAvailableThemes()', () => {
    it('should return built-in themes by default', async () => {
      const themes = await themeManager.getAvailableThemes();

      expect(themes).toHaveLength(2);
      expect(themes.map(t => t.id)).toEqual(['light', 'dark']);
    });

    it('should include registered custom themes', async () => {
      const customTheme: ThemeDefinition = {
        ...LIGHT_THEME,
        id: 'custom',
        name: 'Custom Theme',
        isBuiltIn: false
      };

      await themeManager.registerTheme(customTheme);
      const themes = await themeManager.getAvailableThemes();

      expect(themes).toHaveLength(3);
      expect(themes.map(t => t.id)).toContain('custom');
    });

    it('should return theme objects with all properties', async () => {
      const themes = await themeManager.getAvailableThemes();

      themes.forEach(theme => {
        expect(theme).toHaveProperty('id');
        expect(theme).toHaveProperty('name');
        expect(theme).toHaveProperty('colors');
        expect(theme).toHaveProperty('isBuiltIn');
      });
    });
  });

  describe('setTheme()', () => {
    it('should set a valid theme', async () => {
      await themeManager.setTheme('dark');

      expect(themeManager.getCurrentThemeId()).toBe('dark');
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_set_programmatically',
        'dark',
        expect.objectContaining({
          success: true
        })
      );
    });

    it('should reject invalid theme IDs', async () => {
      await expect(themeManager.setTheme('nonexistent')).rejects.toThrow(
        "Theme 'nonexistent' is not registered"
      );

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_set_failed',
        'nonexistent',
        expect.objectContaining({
          error: "Theme 'nonexistent' is not registered"
        })
      );
    });

    it('should use performance monitor when available', async () => {
      await themeManager.setTheme('light');

      expect(mockPerformanceMonitor.measureOperation).toHaveBeenCalledWith(
        ThemeOperation.THEME_SWITCH,
        expect.any(Function)
      );
    });

    it('should handle theme application errors', async () => {
      const applyError = new Error('Theme application failed');
      mockCSSInjector.injectVariables.mockRejectedValueOnce(applyError);

      await expect(themeManager.setTheme('light')).rejects.toThrow();
      expect(mockErrorHandler.wrapAsync).toHaveBeenCalled();
    });

    it('should update DOM elements correctly', async () => {
      await themeManager.setTheme('dark');

      const rootEl = mockDocument.documentElement;
      const body = mockDocument.body;

      expect(body.classList.remove).toHaveBeenCalled();
      expect(rootEl.classList.remove).toHaveBeenCalled();
      expect(body.classList.add).toHaveBeenCalledWith('theme-dark');
      expect(rootEl.classList.add).toHaveBeenCalledWith('theme-dark');
      expect(rootEl.classList.add).toHaveBeenCalledWith('dark');
    });

    it('should persist theme selection', async () => {
      await themeManager.setTheme('dark');

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('featherjs-current-theme', 'dark');
    });

    it('should update theme switcher value', async () => {
      const mockSwitcher = mockDocument.getElementById('theme-switcher') as HTMLSelectElement;
      mockSwitcher.value = 'light';

      await themeManager.setTheme('dark');

      expect(mockSwitcher.value).toBe('dark');
    });

    it('should dispatch theme change events', async () => {
      await themeManager.setTheme('light');

      expect(mockDocument.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'themechange',
          detail: LIGHT_THEME
        })
      );
      expect(mockWindow.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'feather:themechange',
          detail: { theme: LIGHT_THEME }
        })
      );
    });
  });

  describe('injectCSSVariables() and updateCSSVariables()', () => {
    it('should inject CSS variables for a theme', async () => {
      await themeManager.injectCSSVariables(LIGHT_THEME);

      expect(mockCSSInjector.injectVariables).toHaveBeenCalledWith(LIGHT_THEME, undefined);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_injected',
        'light',
        expect.objectContaining({
          target: 'document',
          variableCount: Object.keys(LIGHT_THEME.colors).length
        })
      );
    });

    it('should inject CSS variables for a specific target element', async () => {
      const mockElement = createMockElement('div');
      await themeManager.injectCSSVariables(DARK_THEME, mockElement);

      expect(mockCSSInjector.injectVariables).toHaveBeenCalledWith(DARK_THEME, mockElement);
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_injected',
        'dark',
        expect.objectContaining({
          target: 'DIV'
        })
      );
    });

    it('should update specific CSS variables', async () => {
      const variables = [
        { name: 'custom-color', value: '#ff0000', category: 'core' as const },
        { name: 'custom-bg', value: '#00ff00' }
      ];

      await themeManager.updateCSSVariables(variables);

      expect(mockCSSInjector.updateVariables).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'custom-color',
            value: '#ff0000',
            category: 'core'
          }),
          expect.objectContaining({
            name: 'custom-bg',
            value: '#00ff00',
            category: 'plugin'
          })
        ]),
        undefined
      );
    });

    it('should update CSS variables for a specific target', async () => {
      const mockElement = createMockElement('section');
      const variables = [{ name: 'test-var', value: '#123456' }];

      await themeManager.updateCSSVariables(variables, mockElement);

      expect(mockCSSInjector.updateVariables).toHaveBeenCalledWith(
        expect.any(Array),
        mockElement
      );
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'css_variables_updated',
        undefined,
        expect.objectContaining({
          target: 'SECTION',
          variableCount: 1
        })
      );
    });

    it('should handle CSS injection errors', async () => {
      mockCSSInjector.injectVariables.mockRejectedValueOnce(new Error('CSS injection failed'));

      await expect(themeManager.injectCSSVariables(LIGHT_THEME)).rejects.toThrow();
      expect(mockErrorHandler.wrapAsync).toHaveBeenCalled();
    });
  });

  describe('watch()', () => {
    it('should register theme change listener', () => {
      const callback = vi.fn();
      const unsubscribe = themeManager.watch(callback);

      expect(mockDocument.addEventListener).toHaveBeenCalledWith('themechange', expect.any(Function));
      expect(typeof unsubscribe).toBe('function');
    });

    it('should call callback when theme changes', async () => {
      const callback = vi.fn();
      themeManager.watch(callback);

      // Get the event handler that was registered
      const eventHandler = (mockDocument.addEventListener as any).mock.calls
        .find((call: any[]) => call[0] === 'themechange')?.[1];

      // Simulate theme change event
      const mockEvent = {
        detail: DARK_THEME
      } as CustomEvent<ThemeDefinition>;

      eventHandler(mockEvent);

      expect(callback).toHaveBeenCalledWith(DARK_THEME);
    });

    it('should unsubscribe from theme change events', () => {
      const callback = vi.fn();
      const unsubscribe = themeManager.watch(callback);

      unsubscribe();

      expect(mockDocument.removeEventListener).toHaveBeenCalledWith('themechange', expect.any(Function));
    });

    it('should handle multiple listeners', () => {
      const callback1 = vi.fn();
      const callback2 = vi.fn();

      const unsubscribe1 = themeManager.watch(callback1);
      const unsubscribe2 = themeManager.watch(callback2);

      expect(mockDocument.addEventListener).toHaveBeenCalledTimes(2);

      unsubscribe1();
      unsubscribe2();

      expect(mockDocument.removeEventListener).toHaveBeenCalledTimes(2);
    });
  });

  describe('System Preference Handling', () => {
    it('should set up system preference listener when no stored preference', async () => {
      // Create a fresh ThemeManager with no stored preference
      const freshLocalStorage = createMockLocalStorage();
      const mockMediaQuery = {
        matches: false,
        addEventListener: vi.fn()
      };

      const freshWindow = {
        matchMedia: vi.fn().mockReturnValue(mockMediaQuery),
        dispatchEvent: vi.fn().mockReturnValue(true),
        localStorage: freshLocalStorage
      } as any;

      // Create fresh config manager with built-in themes
      const freshConfigManager = new MockThemeConfigManager();

      const freshThemeManager = new ThemeManager(
        freshConfigManager,
        mockCSSInjector,
        mockStorage,
        mockValidator,
        mockDocument,
        freshWindow,
        mockLogger,
        mockErrorHandler,
        mockPerformanceMonitor
      );

      await freshThemeManager.init();

      expect(freshWindow.matchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
      expect(mockMediaQuery.addEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    });

    it('should not set up system preference listener when stored preference exists', async () => {
      mockLocalStorage.setItem('featherjs-current-theme', 'dark');

      const mockMediaQuery = {
        matches: false,
        addEventListener: vi.fn()
      };
      mockWindow.matchMedia = vi.fn().mockReturnValue(mockMediaQuery);

      await themeManager.init();

      expect(mockMediaQuery.addEventListener).not.toHaveBeenCalled();
    });

    it('should respond to system preference changes', async () => {
      // Create a fresh ThemeManager with no stored preference
      const freshLocalStorage = createMockLocalStorage();
      const mockMediaQuery = {
        matches: false,
        addEventListener: vi.fn()
      };

      const freshWindow = {
        matchMedia: vi.fn().mockReturnValue(mockMediaQuery),
        dispatchEvent: vi.fn().mockReturnValue(true),
        localStorage: freshLocalStorage
      } as any;

      // Create fresh config manager with built-in themes
      const freshConfigManager = new MockThemeConfigManager();

      const freshThemeManager = new ThemeManager(
        freshConfigManager,
        mockCSSInjector,
        mockStorage,
        mockValidator,
        mockDocument,
        freshWindow,
        mockLogger,
        mockErrorHandler,
        mockPerformanceMonitor
      );

      await freshThemeManager.init();

      // Get the change handler
      const changeHandler = mockMediaQuery.addEventListener.mock.calls[0][1];

      // Simulate system preference change to dark
      await changeHandler({ matches: true });

      expect(freshThemeManager.getCurrentThemeId()).toBe('dark');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle missing dependencies gracefully', () => {
      const minimalManager = new ThemeManager(
        mockConfigManager,
        mockCSSInjector,
        mockStorage,
        mockValidator,
        mockDocument,
        mockWindow
        // No optional dependencies
      );

      expect(minimalManager).toBeDefined();
      expect(minimalManager.getCurrentTheme()).toBeNull();
    });

    it('should handle localStorage errors gracefully', async () => {
      mockLocalStorage.setItem = vi.fn().mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // Should not throw, just log the error
      await themeManager.setTheme('light');

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_persist_failed',
        'light',
        expect.objectContaining({
          error: 'Storage quota exceeded'
        })
      );
    });

    it('should handle missing theme switcher element', async () => {
      mockDocument.getElementById = vi.fn().mockReturnValue(null);

      await themeManager.init('light');

      // Should not throw, just continue without setting up switcher
      expect(themeManager.getCurrentThemeId()).toBe('light');
    });

    it('should handle CSS injection failures gracefully', async () => {
      mockCSSInjector.injectVariables.mockRejectedValueOnce(new Error('CSS injection failed'));

      await expect(themeManager.setTheme('light')).rejects.toThrow();
      expect(mockErrorHandler.wrapAsync).toHaveBeenCalled();
    });

    it('should handle validation failures during registration', async () => {
      const invalidTheme: ThemeDefinition = {
        ...LIGHT_THEME,
        id: 'invalid',
        isBuiltIn: false, // Ensure this is not a built-in theme so validation runs
        colors: {} as any // Invalid colors
      };

      mockValidator.validateTheme.mockResolvedValueOnce({
        isValid: false,
        errors: ['Missing required colors'],
        warnings: []
      });

      await expect(themeManager.registerTheme(invalidTheme)).rejects.toThrow(
        'Theme validation failed: Missing required colors'
      );
    });
  });

  describe('SOLID Principles Compliance', () => {
    it('should demonstrate Single Responsibility Principle', () => {
      // ThemeManager only manages themes, doesn't handle CSS injection, validation, etc.
      expect(typeof themeManager.getCurrentTheme).toBe('function');
      expect(typeof themeManager.setTheme).toBe('function');
      expect(typeof themeManager.registerTheme).toBe('function');

      // CSS injection is delegated to ICSSVariableInjector
      expect(mockCSSInjector.injectVariables).toBeDefined();

      // Validation is delegated to IThemeValidator
      expect(mockValidator.validateTheme).toBeDefined();
    });

    it('should demonstrate Dependency Inversion Principle', () => {
      // ThemeManager depends on abstractions (interfaces), not concretions
      expect(themeManager).toBeDefined();

      // Dependencies are injected, not created internally
      expect(mockConfigManager).toBeDefined();
      expect(mockCSSInjector).toBeDefined();
      expect(mockStorage).toBeDefined();
      expect(mockValidator).toBeDefined();
    });

    it('should demonstrate Open/Closed Principle', async () => {
      // Can extend functionality through dependency injection without modifying ThemeManager
      const customTheme: ThemeDefinition = {
        ...LIGHT_THEME,
        id: 'extended',
        name: 'Extended Theme',
        isBuiltIn: false
      };

      await themeManager.registerTheme(customTheme);
      await themeManager.setTheme('extended');

      expect(themeManager.getCurrentThemeId()).toBe('extended');
    });

    it('should demonstrate Interface Segregation Principle', () => {
      // Each dependency has a focused interface
      // IThemeLogger only has logging methods
      expect(mockLogger.logUserAction).toBeDefined();
      expect(mockLogger.logError).toBeDefined();

      // ICSSVariableInjector only has CSS-related methods
      expect(mockCSSInjector.injectVariables).toBeDefined();
      expect(mockCSSInjector.updateVariables).toBeDefined();

      // IThemeValidator only has validation methods
      expect(mockValidator.validateTheme).toBeDefined();
      expect(mockValidator.validateColorFormat).toBeDefined();
    });
  });
});
