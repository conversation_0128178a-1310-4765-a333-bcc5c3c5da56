/**
 * Core Element Factory Tests
 * Tests core element creation and theme application functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CoreElementFactory } from '../../element-factory/core-factory';
import { SemanticRole, ElementOptions } from '../../element-factory/interfaces';
import { IThemeLogger, IPerformanceMonitor } from '../../interfaces/core-interfaces';
import { ThemeDefinition } from '../../theme-types';
import { LIGHT_THEME } from '../../theme-config';

// Mock implementations
const mockLogger: IThemeLogger = {
  logOperationStart: vi.fn(),
  logOperationComplete: vi.fn(),
  logError: vi.fn(),
  logUserAction: vi.fn(),
  createChild: vi.fn().mockReturnValue({
    logOperationStart: vi.fn(),
    logOperationComplete: vi.fn(),
    logError: vi.fn(),
    logUserAction: vi.fn(),
    createChild: vi.fn()
  })
};

const mockPerformanceMonitor: IPerformanceMonitor = {
  measureOperation: vi.fn().mockImplementation(async (_, fn) => await fn()),
  measureOperationSync: vi.fn().mockImplementation((_, fn) => fn()),
  setPerformanceThreshold: vi.fn(),
  getPerformanceStats: vi.fn().mockReturnValue({
    operationCount: 0,
    totalTime: 0,
    averageTime: 0,
    maxTime: 0,
    minTime: 0
  })
};

describe('CoreElementFactory', () => {
  let factory: CoreElementFactory;
  let testTheme: ThemeDefinition;

  beforeEach(() => {
    testTheme = LIGHT_THEME;
    factory = new CoreElementFactory(
      testTheme,
      mockLogger,
      mockPerformanceMonitor
    );

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('Theme Management', () => {
    it('should set current theme correctly', () => {
      const newTheme: ThemeDefinition = {
        ...testTheme,
        id: 'test-theme',
        name: 'Test Theme'
      };

      factory.setCurrentTheme(newTheme);

      // Theme should be updated (we can't directly access private property, but we can test behavior)
      const classes = factory.getThemeClasses(SemanticRole.BUTTON);
      expect(classes).toContain('theme-test-theme');
    });

    it('should handle null theme', () => {
      factory.setCurrentTheme(null);

      const classes = factory.getThemeClasses(SemanticRole.BUTTON);
      expect(classes).toEqual([]);
    });
  });

  describe('Element Creation', () => {
    it('should create elements with theme application', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: 'custom-class',
        attributes: { id: 'test-element' }
      };

      const element = await factory.createElement(options);

      expect(element).toBeInstanceOf(HTMLDivElement);
      expect(element.tagName).toBe('DIV');
      expect(element.getAttribute('id')).toBe('test-element');
      expect(element.getAttribute('data-theme-role')).toBe('surface');
      expect(element.getAttribute('data-theme-id')).toBe('light');
      expect(element.classList.contains('custom-class')).toBe(true);
      expect(element.classList.contains('theme-light')).toBe(true);
      expect(element.classList.contains('semantic-surface')).toBe(true);
    });

    it('should create elements with text content', async () => {
      const options: ElementOptions = {
        tagName: 'p',
        role: SemanticRole.TEXT,
        textContent: 'Hello, World!'
      };

      const element = await factory.createElement(options);

      expect(element.textContent).toBe('Hello, World!');
    });

    it('should create elements with HTML content', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        innerHTML: '<span>HTML Content</span>'
      };

      const element = await factory.createElement(options);

      expect(element.innerHTML).toBe('<span>HTML Content</span>');
      expect(element.querySelector('span')?.textContent).toBe('HTML Content');
    });

    it('should create elements with children', async () => {
      const child1 = document.createElement('span');
      child1.textContent = 'Child 1';

      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        children: [child1, 'Text child']
      };

      const element = await factory.createElement(options);

      expect(element.children).toHaveLength(1);
      expect(element.childNodes).toHaveLength(2); // span + text node
      expect(element.children[0]).toBe(child1);
      expect(element.childNodes[1].textContent).toBe('Text child');
    });

    it('should create elements with event listeners', async () => {
      const clickHandler = vi.fn();
      const options: ElementOptions = {
        tagName: 'button',
        role: SemanticRole.BUTTON,
        eventListeners: {
          click: clickHandler
        }
      };

      const element = await factory.createElement(options);

      // Simulate click
      element.click();
      expect(clickHandler).toHaveBeenCalledTimes(1);
    });

    it('should create elements with ARIA attributes', async () => {
      const options: ElementOptions = {
        tagName: 'button',
        role: SemanticRole.BUTTON,
        ariaAttributes: {
          'aria-label': 'Test button',
          'aria-pressed': 'false'
        }
      };

      const element = await factory.createElement(options);

      expect(element.getAttribute('aria-label')).toBe('Test button');
      expect(element.getAttribute('aria-pressed')).toBe('false');
    });

    it('should create multiple elements efficiently', async () => {
      const optionsArray: ElementOptions[] = [
        { tagName: 'div', role: SemanticRole.SURFACE },
        { tagName: 'button', role: SemanticRole.BUTTON },
        { tagName: 'input', role: SemanticRole.INPUT }
      ];

      const elements = await factory.createElements(optionsArray);

      expect(elements).toHaveLength(3);
      expect(elements[0].tagName).toBe('DIV');
      expect(elements[1].tagName).toBe('BUTTON');
      expect(elements[2].tagName).toBe('INPUT');

      // All should have theme applied
      elements.forEach(element => {
        expect(element.getAttribute('data-theme-id')).toBe('light');
        expect(element.classList.contains('theme-light')).toBe(true);
      });
    });
  });

  describe('Theme Application', () => {
    it('should apply theme to existing elements', async () => {
      const element = document.createElement('div');

      await factory.applyThemeToElement(element, SemanticRole.BUTTON);

      expect(element.getAttribute('data-theme-id')).toBe('light');
      expect(element.classList.contains('theme-light')).toBe(true);
      expect(element.classList.contains('semantic-button')).toBe(true);
      expect(element.style.getPropertyValue('--theme-primary')).toBe('#1565c0');
    });

    it('should skip theme application when no theme is available', async () => {
      factory.setCurrentTheme(null);
      const element = document.createElement('div');

      await factory.applyThemeToElement(element, SemanticRole.BUTTON);

      // Should log the skip action
      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_apply_skipped',
        undefined,
        expect.objectContaining({
          reason: 'No theme available'
        })
      );
    });

    it('should remove theme from elements', async () => {
      const element = document.createElement('div');
      element.classList.add('theme-light', 'semantic-surface');
      element.setAttribute('data-theme-id', 'light');
      element.style.setProperty('--theme-primary', '#1565c0');

      await factory.removeThemeFromElement(element);

      expect(element.getAttribute('data-theme-id')).toBeNull();
      expect(element.classList.contains('theme-light')).toBe(false);
      expect(element.style.getPropertyValue('--theme-primary')).toBe('');
    });

    it('should update element theme when theme changes', async () => {
      const element = document.createElement('div');
      element.setAttribute('data-theme-role', 'button');

      const newTheme: ThemeDefinition = {
        ...testTheme,
        id: 'dark',
        colors: { ...testTheme.colors, primary: '#bb86fc' }
      };

      await factory.updateElementTheme(element, newTheme);

      // The current implementation applies the current theme, not the new theme
      expect(element.getAttribute('data-theme-id')).toBe('light');
      expect(element.style.getPropertyValue('--theme-primary')).toBe('#1565c0');
    });

    it('should skip theme update when no role is found', async () => {
      const element = document.createElement('div');
      // No data-theme-role attribute

      const newTheme: ThemeDefinition = {
        ...testTheme,
        id: 'dark'
      };

      await factory.updateElementTheme(element, newTheme);

      expect(mockLogger.logUserAction).toHaveBeenCalledWith(
        'theme_update_skipped',
        'dark',
        expect.objectContaining({
          reason: 'No semantic role found'
        })
      );
    });
  });

  describe('Theme Classes and Variables', () => {
    it('should get theme classes for semantic roles', () => {
      const classes = factory.getThemeClasses(SemanticRole.BUTTON);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-button');
      expect(classes).toContain('theme-role-button');
      expect(classes).toContain('theme-interactive');
      expect(classes).toContain('theme-focusable');
    });

    it('should get CSS variables for semantic roles', () => {
      const variables = factory.getCSSVariables(SemanticRole.INPUT);

      expect(variables['--theme-primary']).toBe('#1565c0');
      expect(variables['--theme-input-bg']).toBe('#ffffff');
      expect(variables['--theme-input-border']).toBe('#e0e0e0');
      expect(variables['--theme-input-focus-border']).toBe('#1565c0');
    });

    it('should return empty arrays/objects when no theme is set', () => {
      factory.setCurrentTheme(null);

      const classes = factory.getThemeClasses(SemanticRole.BUTTON);
      const variables = factory.getCSSVariables(SemanticRole.INPUT);

      expect(classes).toEqual([]);
      expect(variables).toEqual({});
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid element creation gracefully', async () => {
      const invalidOptions = {
        tagName: '',
        role: SemanticRole.SURFACE
      } as any;

      await expect(factory.createElement(invalidOptions)).rejects.toThrow();
    });
  });

  describe('Performance and Logging', () => {
    it('should log element creation operations', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE
      };

      await factory.createElement(options);

      expect(mockPerformanceMonitor.measureOperation).toHaveBeenCalled();
      expect(mockLogger.logOperationStart).toHaveBeenCalled();
      expect(mockLogger.logOperationComplete).toHaveBeenCalled();
    });

    it('should log batch element creation', async () => {
      const optionsArray: ElementOptions[] = [
        { tagName: 'div', role: SemanticRole.SURFACE },
        { tagName: 'span', role: SemanticRole.TEXT }
      ];

      await factory.createElements(optionsArray);

      expect(mockLogger.logOperationStart).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          additionalData: expect.objectContaining({
            batchOperation: true
          })
        })
      );
    });
  });
});
