/**
 * Element Composition Tests
 * Tests element composition and hierarchy methods
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ElementComposer } from '../../element-factory/composition';
import { CoreElementFactory } from '../../element-factory/core-factory';
import { SemanticRole, ElementOptions } from '../../element-factory/interfaces';
import { IThemeLogger, IPerformanceMonitor } from '../../interfaces/core-interfaces';
import { LIGHT_THEME } from '../../theme-config';

// Mock implementations
const mockLogger: IThemeLogger = {
  logOperationStart: vi.fn(),
  logOperationComplete: vi.fn(),
  logError: vi.fn(),
  logUserAction: vi.fn(),
  createChild: vi.fn().mockReturnValue({
    logOperationStart: vi.fn(),
    logOperationComplete: vi.fn(),
    logError: vi.fn(),
    logUserAction: vi.fn(),
    createChild: vi.fn()
  })
};

const mockPerformanceMonitor: IPerformanceMonitor = {
  measureOperation: vi.fn().mockImplementation(async (_, fn) => await fn()),
  measureOperationSync: vi.fn().mockImplementation((_, fn) => fn()),
  setPerformanceThreshold: vi.fn(),
  getPerformanceStats: vi.fn().mockReturnValue({
    operationCount: 0,
    totalTime: 0,
    averageTime: 0,
    maxTime: 0,
    minTime: 0
  })
};

describe('ElementComposer', () => {
  let composer: ElementComposer;
  let coreFactory: CoreElementFactory;

  beforeEach(() => {
    coreFactory = new CoreElementFactory(
      LIGHT_THEME,
      mockLogger,
      mockPerformanceMonitor
    );
    composer = new ElementComposer(coreFactory);

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('Element Composition', () => {
    it('should compose elements with layout options', async () => {
      const container = document.createElement('div');
      const elements = [
        document.createElement('span'),
        document.createElement('div')
      ];

      await composer.composeElements(container, elements, 'grid');
      expect(container.classList.contains('layout-grid')).toBe(true);
      expect(container.style.display).toBe('grid');
      expect(container.children).toHaveLength(2);
    });

    it('should compose elements with flex layout', async () => {
      const container = document.createElement('div');
      const elements = [document.createElement('span')];

      await composer.composeElements(container, elements, 'flex');

      expect(container.classList.contains('layout-flex')).toBe(true);
      expect(container.style.display).toBe('flex');
      expect(container.style.flexWrap).toBe('wrap');
      expect(container.style.gap).toBe('var(--theme-spacing-medium, 1rem)');
    });

    it('should compose elements with stack layout', async () => {
      const container = document.createElement('div');
      const elements = [document.createElement('span')];

      await composer.composeElements(container, elements, 'stack');

      expect(container.classList.contains('layout-stack')).toBe(true);
      expect(container.style.display).toBe('flex');
      expect(container.style.flexDirection).toBe('column');
      expect(container.style.gap).toBe('var(--theme-spacing-small, 0.5rem)');
    });

    it('should use default flex layout when no layout specified', async () => {
      const container = document.createElement('div');
      const elements = [document.createElement('span')];

      await composer.composeElements(container, elements);

      expect(container.classList.contains('layout-flex')).toBe(true);
      expect(container.style.display).toBe('flex');
    });
  });

  describe('Element Hierarchy', () => {
    it('should create element hierarchies with theme inheritance', async () => {
      const rootOptions: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: 'root'
      };

      const childrenOptions: ElementOptions[] = [
        { tagName: 'span', role: SemanticRole.TEXT, className: 'child1' },
        { tagName: 'button', role: SemanticRole.BUTTON, className: 'child2' }
      ];

      const hierarchy = await composer.createHierarchy(rootOptions, childrenOptions);

      expect(hierarchy.tagName).toBe('DIV');
      expect(hierarchy.classList.contains('root')).toBe(true);
      expect(hierarchy.children).toHaveLength(2);
      expect(hierarchy.children[0].classList.contains('child1')).toBe(true);
      expect(hierarchy.children[1].classList.contains('child2')).toBe(true);
    });

    it('should create hierarchy with theme override inheritance', async () => {
      const themeOverride = {
        id: 'custom-theme',
        colors: {
          primary: '#custom-color',
          text: '#000000',
          background: '#ffffff',
          surface: '#f5f5f5',
          border: '#e0e0e0',
          secondary: '#424242',
          accent: '#1976d2',
          error: '#d32f2f',
          warning: '#f57c00',
          success: '#388e3c',
          info: '#1976d2'
        }
      };

      const rootOptions: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        themeOverride
      };

      const childrenOptions: ElementOptions[] = [
        { tagName: 'span', role: SemanticRole.TEXT }
      ];

      const hierarchy = await composer.createHierarchy(rootOptions, childrenOptions);

      // Both root and child should have theme applied with the custom theme ID
      expect(hierarchy.getAttribute('data-theme-id')).toBe('custom-theme');
      expect(hierarchy.children[0].getAttribute('data-theme-id')).toBe('custom-theme');
    });

    it('should create hierarchy with empty children array', async () => {
      const rootOptions: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE
      };

      const hierarchy = await composer.createHierarchy(rootOptions, []);

      expect(hierarchy.tagName).toBe('DIV');
      expect(hierarchy.children).toHaveLength(0);
    });
  });

  describe('Element Cloning', () => {
    it('should clone elements while preserving theme', async () => {
      const original = document.createElement('div');
      original.classList.add('theme-light', 'semantic-surface');
      original.setAttribute('data-theme-id', 'light');
      original.style.setProperty('--theme-primary', '#1565c0');

      const cloned = await composer.cloneElement(original, true);

      expect(cloned).not.toBe(original);
      expect(cloned.classList.contains('theme-light')).toBe(true);
      expect(cloned.getAttribute('data-theme-id')).toBe('light');
      expect(cloned.style.getPropertyValue('--theme-primary')).toBe('#1565c0');
    });

    it('should clone elements without preserving theme', async () => {
      const original = document.createElement('div');
      original.classList.add('theme-light', 'semantic-surface', 'custom-class');
      original.setAttribute('data-theme-id', 'light');

      const cloned = await composer.cloneElement(original, false);

      expect(cloned).not.toBe(original);
      expect(cloned.classList.contains('theme-light')).toBe(false);
      expect(cloned.classList.contains('custom-class')).toBe(true);
      expect(cloned.getAttribute('data-theme-id')).toBeNull();
    });

    it('should clone elements with default preserve theme behavior', async () => {
      const original = document.createElement('div');
      original.classList.add('theme-light');
      original.setAttribute('data-theme-id', 'light');

      const cloned = await composer.cloneElement(original);

      // Default should preserve theme
      expect(cloned.classList.contains('theme-light')).toBe(true);
      expect(cloned.getAttribute('data-theme-id')).toBe('light');
    });

    it('should clone complex elements with children', async () => {
      const original = document.createElement('div');
      const child = document.createElement('span');
      child.textContent = 'Child content';
      original.appendChild(child);
      original.classList.add('theme-light');

      const cloned = await composer.cloneElement(original, true);

      expect(cloned.children).toHaveLength(1);
      expect(cloned.children[0].textContent).toBe('Child content');
      expect(cloned.classList.contains('theme-light')).toBe(true);
    });

    it('should handle cloning elements with computed styles', async () => {
      const original = document.createElement('div');
      original.style.setProperty('--theme-primary', '#1565c0');
      original.style.setProperty('--custom-var', '#custom');

      // Mock getComputedStyle
      const originalGetComputedStyle = window.getComputedStyle;
      window.getComputedStyle = vi.fn().mockReturnValue({
        length: 2,
        item: vi.fn()
          .mockReturnValueOnce('--theme-primary')
          .mockReturnValueOnce('--custom-var'),
        getPropertyValue: vi.fn()
          .mockReturnValueOnce('#1565c0')
          .mockReturnValueOnce('#custom')
      });

      const cloned = await composer.cloneElement(original, true);

      expect(cloned.style.getPropertyValue('--theme-primary')).toBe('#1565c0');

      // Restore original function
      window.getComputedStyle = originalGetComputedStyle;
    });
  });

  describe('Advanced Layout Methods', () => {
    it('should create responsive layout containers', async () => {
      const container = await composer.createResponsiveLayout({
        columns: { mobile: 1, tablet: 2, desktop: 3 },
        gap: '2rem'
      });

      expect(container.classList.contains('responsive-layout')).toBe(true);
      expect(container.style.display).toBe('grid');
      expect(container.style.gap).toBe('2rem');
      expect(container.style.gridTemplateColumns).toBe('repeat(1, 1fr)');
    });

    it('should create flex layouts with custom options', async () => {
      const container = await composer.createFlexLayout({
        direction: 'column',
        justify: 'center',
        align: 'start',
        wrap: true,
        gap: '1.5rem'
      });

      expect(container.classList.contains('flex-layout')).toBe(true);
      expect(container.style.display).toBe('flex');
      expect(container.style.flexDirection).toBe('column');
      expect(container.style.justifyContent).toBe('center');
      expect(container.style.alignItems).toBe('flex-start');
      expect(container.style.flexWrap).toBe('wrap');
      expect(container.style.gap).toBe('1.5rem');
    });

    it('should create masonry layouts', async () => {
      const container = await composer.createMasonryLayout({
        columns: 4,
        gap: '1rem',
        minItemWidth: '250px'
      });

      expect(container.classList.contains('masonry-layout')).toBe(true);
      expect(container.style.display).toBe('grid');
      expect(container.style.gridTemplateColumns).toBe('repeat(auto-fit, minmax(250px, 1fr))');
      expect(container.style.gap).toBe('1rem');
    });

    it('should create card grid layouts', async () => {
      const container = await composer.createCardGrid({
        minCardWidth: '300px',
        maxCardWidth: '400px',
        gap: '2rem',
        aspectRatio: '1.5'
      });

      expect(container.classList.contains('card-grid')).toBe(true);
      expect(container.style.display).toBe('grid');
      expect(container.style.gridTemplateColumns).toBe('repeat(auto-fit, minmax(300px, 400px))');
      expect(container.style.gap).toBe('2rem');
      expect(container.style.justifyContent).toBe('center');
    });

    it('should use default options for layout methods', async () => {
      const responsiveContainer = await composer.createResponsiveLayout({});
      const flexContainer = await composer.createFlexLayout({});
      const masonryContainer = await composer.createMasonryLayout({});
      const cardContainer = await composer.createCardGrid({});

      // Check that defaults are applied
      expect(responsiveContainer.style.gridTemplateColumns).toBe('repeat(1, 1fr)');
      expect(flexContainer.style.flexDirection).toBe('row');
      expect(masonryContainer.style.gridTemplateColumns).toBe('repeat(auto-fit, minmax(200px, 1fr))');
      expect(cardContainer.style.gridTemplateColumns).toBe('repeat(auto-fit, minmax(250px, 350px))');
    });
  });
});
