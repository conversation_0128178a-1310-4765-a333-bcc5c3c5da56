/**
 * Element Options Validation Tests
 * Tests element options validation logic
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { ElementOptionsValidator } from '../../element-factory/validation';
import { SemanticRole, ElementOptions } from '../../element-factory/interfaces';

describe('ElementOptionsValidator', () => {
  let validator: ElementOptionsValidator;

  beforeEach(() => {
    validator = new ElementOptionsValidator();
  });

  describe('Valid Options', () => {
    it('should validate minimal valid options', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should validate comprehensive valid options', async () => {
      const options: ElementOptions = {
        tagName: 'button',
        role: SemanticRole.BUTTON,
        className: ['btn', 'btn-primary'],
        attributes: { type: 'button', disabled: false },
        ariaAttributes: { 'aria-label': 'Test button' },
        eventListeners: { click: () => {} },
        textContent: 'Click me',
        children: [document.createElement('span')]
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate all supported HTML tags', async () => {
      const supportedTags = [
        'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'button', 'input', 'textarea', 'select', 'option',
        'form', 'label', 'fieldset', 'legend',
        'ul', 'ol', 'li', 'dl', 'dt', 'dd',
        'table', 'thead', 'tbody', 'tfoot', 'tr', 'th', 'td',
        'header', 'footer', 'nav', 'main', 'section', 'article', 'aside',
        'dialog', 'details', 'summary',
        'a', 'img', 'video', 'audio', 'canvas', 'svg',
        'strong', 'em', 'code', 'pre', 'blockquote',
        'time', 'mark', 'small', 'sub', 'sup'
      ];

      for (const tagName of supportedTags) {
        const options: ElementOptions = {
          tagName: tagName as keyof HTMLElementTagNameMap,
          role: SemanticRole.SURFACE
        };

        const result = await validator.validateOptions(options);
        expect(result.isValid).toBe(true);
      }
    });

    it('should validate all semantic roles', async () => {
      const roles = Object.values(SemanticRole);

      for (const role of roles) {
        const options: ElementOptions = {
          tagName: 'div',
          role
        };

        const result = await validator.validateOptions(options);
        expect(result.isValid).toBe(true);
      }
    });
  });

  describe('Required Field Validation', () => {
    it('should require tagName', async () => {
      const options = {
        role: SemanticRole.SURFACE
      } as ElementOptions;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('tagName is required');
    });

    it('should require role', async () => {
      const options = {
        tagName: 'div'
      } as ElementOptions;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('role is required');
    });

    it('should require both tagName and role', async () => {
      const options = {} as ElementOptions;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('tagName is required');
      expect(result.errors).toContain('role is required');
    });
  });

  describe('Tag Name Validation', () => {
    it('should reject non-string tagName', async () => {
      const options = {
        tagName: 123,
        role: SemanticRole.SURFACE
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('tagName must be a string');
    });

    it('should reject unsupported tag names', async () => {
      const options = {
        tagName: 'unsupported-tag',
        role: SemanticRole.SURFACE
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Unsupported tag name: unsupported-tag');
    });
  });

  describe('Semantic Role Validation', () => {
    it('should reject invalid semantic roles', async () => {
      const options = {
        tagName: 'div',
        role: 'invalid-role'
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid semantic role: invalid-role');
    });
  });

  describe('ClassName Validation', () => {
    it('should accept string className', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: 'test-class'
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
    });

    it('should accept array of string classNames', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: ['class1', 'class2']
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
    });

    it('should reject non-string/non-array className', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: 123
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('className must be a string or array of strings');
    });

    it('should reject array with non-string elements', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: ['valid', 123, 'also-valid']
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('className[1] must be a string');
    });
  });

  describe('Attributes Validation', () => {
    it('should accept valid attributes object', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        attributes: {
          id: 'test-id',
          'data-value': 'test',
          tabindex: 0,
          disabled: true
        }
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
    });

    it('should reject non-object attributes', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        attributes: 'not-an-object'
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('attributes must be an object');
    });

    it('should reject array as attributes', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        attributes: ['not', 'an', 'object']
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('attributes must be an object');
    });

    it('should reject invalid attribute values', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        attributes: {
          id: 'valid',
          invalid: { nested: 'object' }
        }
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Attribute 'invalid' value must be string, number, or boolean");
    });

    it('should detect dangerous attributes', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        attributes: {
          onclick: 'alert("xss")'
        }
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Potentially dangerous attribute: onclick');
    });
  });

  describe('ARIA Attributes Validation', () => {
    it('should accept valid ARIA attributes', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        ariaAttributes: {
          'aria-label': 'Test label',
          'aria-hidden': 'true',
          'aria-expanded': 'false'
        }
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
    });

    it('should reject non-object ariaAttributes', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        ariaAttributes: 'not-an-object'
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('ariaAttributes must be an object');
    });

    it('should reject attributes not starting with aria-', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        ariaAttributes: {
          'aria-label': 'valid',
          'invalid-attr': 'invalid'
        }
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("ARIA attribute 'invalid-attr' must start with 'aria-'");
    });

    it('should reject non-string ARIA attribute values', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        ariaAttributes: {
          'aria-label': 123
        }
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("ARIA attribute 'aria-label' value must be a string");
    });
  });

  describe('Event Listeners Validation', () => {
    it('should accept valid event listeners', async () => {
      const options: ElementOptions = {
        tagName: 'button',
        role: SemanticRole.BUTTON,
        eventListeners: {
          click: () => {},
          mousedown: () => {},
          keydown: () => {}
        }
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
    });

    it('should reject non-object eventListeners', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        eventListeners: 'not-an-object'
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('eventListeners must be an object');
    });

    it('should reject non-function event listeners', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        eventListeners: {
          click: 'not-a-function'
        }
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Event listener for 'click' must be a function");
    });

    it('should reject unsupported event types', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        eventListeners: {
          'unsupported-event': () => {}
        }
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Unsupported event type: unsupported-event');
    });
  });

  describe('Children Validation', () => {
    it('should accept valid children array', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        children: [
          'Text content',
          document.createElement('span')
        ]
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
    });

    it('should reject non-array children', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        children: 'not-an-array'
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('children must be an array');
    });

    it('should reject invalid child types', async () => {
      const options = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        children: [
          'valid string',
          123,
          document.createElement('span')
        ]
      } as any;

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Child at index 1 must be a string or HTMLElement');
    });
  });

  describe('Performance Warnings', () => {
    it('should warn about large number of children', async () => {
      const children = Array(60).fill('child');
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        children
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Large number of children may impact performance');
    });

    it('should warn about innerHTML and children conflict', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        innerHTML: '<span>HTML</span>',
        children: ['text']
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Both innerHTML and children specified; children will be appended after innerHTML');
    });

    it('should warn about innerHTML and textContent conflict', async () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        innerHTML: '<span>HTML</span>',
        textContent: 'Text'
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Both innerHTML and textContent specified; innerHTML will take precedence');
    });

    it('should warn about large number of event listeners', async () => {
      const eventListeners: Record<string, () => void> = {};
      const events = ['click', 'mousedown', 'mouseup', 'mouseover', 'mouseout', 'mousemove', 'keydown', 'keyup', 'keypress', 'focus', 'blur'];
      events.forEach(event => {
        eventListeners[event] = () => {};
      });

      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        eventListeners
      };

      const result = await validator.validateOptions(options);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Large number of event listeners may impact performance');
    });
  });

  describe('Theme Override Validation', () => {
    it('should validate valid theme override', () => {
      const themeOverride = {
        colors: {
          primary: '#custom-color'
        }
      };

      const result = validator.validateThemeOverride(themeOverride);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject non-object theme override', () => {
      const result = validator.validateThemeOverride('not-an-object');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('themeOverride must be an object');
    });

    it('should reject invalid colors object', () => {
      const themeOverride = {
        colors: 'not-an-object'
      };

      const result = validator.validateThemeOverride(themeOverride);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('themeOverride.colors must be an object');
    });
  });
});
