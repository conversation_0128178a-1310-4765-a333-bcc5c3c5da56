/**
 * Semantic Roles Tests
 * Tests semantic role system and theme class/CSS variable generation
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { SemanticRoleHandler } from '../../element-factory/semantic-roles';
import { SemanticRole } from '../../element-factory/interfaces';
import { ThemeDefinition } from '../../theme-types';
import { LIGHT_THEME, DARK_THEME } from '../../theme-config';

describe('SemanticRoleHandler', () => {
  let handler: SemanticRoleHandler;
  let testTheme: ThemeDefinition;

  beforeEach(() => {
    handler = new SemanticRoleHandler();
    testTheme = LIGHT_THEME;
  });

  describe('Theme Class Generation', () => {
    it('should generate theme classes for surface role', () => {
      const classes = handler.getThemeClasses(SemanticRole.SURFACE, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-surface');
      expect(classes).toContain('theme-role-surface');
      expect(classes).toContain('theme-surface');
      expect(classes).toContain('theme-elevated');
    });

    it('should generate theme classes for button role', () => {
      const classes = handler.getThemeClasses(SemanticRole.BUTTON, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-button');
      expect(classes).toContain('theme-role-button');
      expect(classes).toContain('theme-interactive');
      expect(classes).toContain('theme-focusable');
    });

    it('should generate theme classes for input role', () => {
      const classes = handler.getThemeClasses(SemanticRole.INPUT, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-input');
      expect(classes).toContain('theme-role-input');
      expect(classes).toContain('theme-input');
      expect(classes).toContain('theme-focusable');
    });

    it('should generate theme classes for dialog role', () => {
      const classes = handler.getThemeClasses(SemanticRole.DIALOG, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-dialog');
      expect(classes).toContain('theme-role-dialog');
      expect(classes).toContain('theme-overlay');
      expect(classes).toContain('theme-modal');
    });

    it('should generate theme classes for overlay role', () => {
      const classes = handler.getThemeClasses(SemanticRole.OVERLAY, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-overlay');
      expect(classes).toContain('theme-role-overlay');
      expect(classes).toContain('theme-overlay');
      expect(classes).toContain('theme-floating');
    });

    it('should generate theme classes for card role', () => {
      const classes = handler.getThemeClasses(SemanticRole.CARD, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-card');
      expect(classes).toContain('theme-role-card');
      expect(classes).toContain('theme-surface');
      expect(classes).toContain('theme-card');
    });

    it('should generate theme classes for list role', () => {
      const classes = handler.getThemeClasses(SemanticRole.LIST, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-list');
      expect(classes).toContain('theme-role-list');
      expect(classes).toContain('theme-list');
      expect(classes).toContain('theme-structured');
    });

    it('should generate theme classes for menu role', () => {
      const classes = handler.getThemeClasses(SemanticRole.MENU, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-menu');
      expect(classes).toContain('theme-role-menu');
      expect(classes).toContain('theme-menu');
      expect(classes).toContain('theme-interactive');
    });

    it('should generate theme classes for navigation role', () => {
      const classes = handler.getThemeClasses(SemanticRole.NAVIGATION, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-navigation');
      expect(classes).toContain('theme-role-navigation');
      expect(classes).toContain('theme-navigation');
      expect(classes).toContain('theme-interactive');
    });

    it('should generate theme classes for header role', () => {
      const classes = handler.getThemeClasses(SemanticRole.HEADER, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-header');
      expect(classes).toContain('theme-role-header');
      expect(classes).toContain('theme-header');
      expect(classes).toContain('theme-prominent');
    });

    it('should generate default theme classes for text role', () => {
      const classes = handler.getThemeClasses(SemanticRole.TEXT, testTheme);

      expect(classes).toContain('theme-light');
      expect(classes).toContain('semantic-text');
      expect(classes).toContain('theme-role-text');
      expect(classes).toContain('theme-content');
    });
  });

  describe('CSS Variable Generation', () => {
    it('should generate base CSS variables for all roles', () => {
      const variables = handler.getCSSVariables(SemanticRole.SURFACE, testTheme);

      expect(variables['--theme-primary']).toBe('#1565c0');
      expect(variables['--theme-secondary']).toBe('#6c757d');
      expect(variables['--theme-background']).toBe('#ffffff');
      expect(variables['--theme-surface']).toBe('#f8f9fa');
      expect(variables['--theme-text']).toBe('#333333');
      expect(variables['--theme-border']).toBe('#e0e0e0');
    });

    it('should generate role-specific CSS variables for surface', () => {
      const variables = handler.getCSSVariables(SemanticRole.SURFACE, testTheme);

      expect(variables['--theme-surface-bg']).toBe('#f8f9fa');
      expect(variables['--theme-surface-border']).toBe('#e0e0e0');
    });

    it('should generate role-specific CSS variables for button', () => {
      const variables = handler.getCSSVariables(SemanticRole.BUTTON, testTheme);

      expect(variables['--theme-button-bg']).toBe('#1565c0');
      expect(variables['--theme-button-text']).toBe('#ffffff');
      expect(variables['--theme-button-hover-bg']).toBe('#f5f5f5');
    });

    it('should generate role-specific CSS variables for input', () => {
      const variables = handler.getCSSVariables(SemanticRole.INPUT, testTheme);

      expect(variables['--theme-input-bg']).toBe('#ffffff');
      expect(variables['--theme-input-border']).toBe('#e0e0e0');
      expect(variables['--theme-input-focus-border']).toBe('#1565c0');
    });

    it('should generate role-specific CSS variables for dialog', () => {
      const variables = handler.getCSSVariables(SemanticRole.DIALOG, testTheme);

      expect(variables['--theme-dialog-bg']).toBe('#f8f9fa');
      expect(variables['--theme-dialog-shadow']).toBe('rgba(0, 0, 0, 0.1)');
    });

    it('should generate role-specific CSS variables for overlay', () => {
      const variables = handler.getCSSVariables(SemanticRole.OVERLAY, testTheme);

      expect(variables['--theme-overlay-bg']).toBe('#f8f9fa');
      expect(variables['--theme-overlay-shadow']).toBe('rgba(0, 0, 0, 0.1)');
    });

    it('should generate role-specific CSS variables for card', () => {
      const variables = handler.getCSSVariables(SemanticRole.CARD, testTheme);

      expect(variables['--theme-card-bg']).toBe('#f8f9fa');
      expect(variables['--theme-card-border']).toBe('#e0e0e0');
    });

    it('should generate role-specific CSS variables for list', () => {
      const variables = handler.getCSSVariables(SemanticRole.LIST, testTheme);

      expect(variables['--theme-list-bg']).toBe('#ffffff');
      expect(variables['--theme-list-border']).toBe('#e0e0e0');
    });

    it('should generate role-specific CSS variables for menu', () => {
      const variables = handler.getCSSVariables(SemanticRole.MENU, testTheme);

      expect(variables['--theme-menu-bg']).toBe('#f8f9fa');
      expect(variables['--theme-menu-border']).toBe('#e0e0e0');
      expect(variables['--theme-menu-hover-bg']).toBe('#f5f5f5');
    });

    it('should generate role-specific CSS variables for navigation', () => {
      const variables = handler.getCSSVariables(SemanticRole.NAVIGATION, testTheme);

      expect(variables['--theme-nav-bg']).toBe('#f8f9fa');
      expect(variables['--theme-nav-border']).toBe('#e0e0e0');
      expect(variables['--theme-nav-active-bg']).toBe('#e9ecef');
    });

    it('should generate role-specific CSS variables for header', () => {
      const variables = handler.getCSSVariables(SemanticRole.HEADER, testTheme);

      expect(variables['--theme-header-bg']).toBe('#f8f9fa');
      expect(variables['--theme-header-text']).toBe('#333333');
      expect(variables['--theme-header-border']).toBe('#e0e0e0');
    });

    it('should use custom CSS variable prefix', () => {
      const customTheme: ThemeDefinition = {
        ...testTheme,
        cssVariablePrefix: 'custom'
      };

      const variables = handler.getCSSVariables(SemanticRole.BUTTON, customTheme);

      expect(variables['--custom-primary']).toBe('#1565c0');
      expect(variables['--custom-button-bg']).toBe('#1565c0');
    });
  });

  describe('Caching', () => {
    it('should cache theme classes for performance', () => {
      // First call
      const classes1 = handler.getThemeClasses(SemanticRole.BUTTON, testTheme);

      // Second call should return cached result
      const classes2 = handler.getThemeClasses(SemanticRole.BUTTON, testTheme);

      expect(classes1).toBe(classes2); // Same reference indicates caching
    });

    it('should cache CSS variables for performance', () => {
      // First call
      const variables1 = handler.getCSSVariables(SemanticRole.INPUT, testTheme);

      // Second call should return cached result
      const variables2 = handler.getCSSVariables(SemanticRole.INPUT, testTheme);

      expect(variables1).toBe(variables2); // Same reference indicates caching
    });

    it('should clear caches when requested', () => {
      // Populate cache
      handler.getThemeClasses(SemanticRole.BUTTON, testTheme);
      handler.getCSSVariables(SemanticRole.INPUT, testTheme);

      const statsBefore = handler.getCacheStats();
      expect(statsBefore.themeClassCacheSize).toBeGreaterThan(0);
      expect(statsBefore.cssVariableCacheSize).toBeGreaterThan(0);

      // Clear caches
      handler.clearCaches();

      const statsAfter = handler.getCacheStats();
      expect(statsAfter.themeClassCacheSize).toBe(0);
      expect(statsAfter.cssVariableCacheSize).toBe(0);
    });

    it('should provide cache statistics', () => {
      // Initially empty
      let stats = handler.getCacheStats();
      expect(stats.themeClassCacheSize).toBe(0);
      expect(stats.cssVariableCacheSize).toBe(0);

      // Add some cached items
      handler.getThemeClasses(SemanticRole.BUTTON, testTheme);
      handler.getThemeClasses(SemanticRole.INPUT, testTheme);
      handler.getCSSVariables(SemanticRole.SURFACE, testTheme);

      stats = handler.getCacheStats();
      expect(stats.themeClassCacheSize).toBe(2);
      expect(stats.cssVariableCacheSize).toBe(1);
    });
  });

  describe('Role Validation', () => {
    it('should validate valid semantic roles', () => {
      expect(handler.isValidRole('surface')).toBe(true);
      expect(handler.isValidRole('button')).toBe(true);
      expect(handler.isValidRole('input')).toBe(true);
      expect(handler.isValidRole('dialog')).toBe(true);
    });

    it('should reject invalid semantic roles', () => {
      expect(handler.isValidRole('invalid')).toBe(false);
      expect(handler.isValidRole('')).toBe(false);
      expect(handler.isValidRole('BUTTON')).toBe(false); // Case sensitive
    });

    it('should get all available semantic roles', () => {
      const roles = handler.getAllRoles();

      expect(roles).toContain(SemanticRole.SURFACE);
      expect(roles).toContain(SemanticRole.BUTTON);
      expect(roles).toContain(SemanticRole.INPUT);
      expect(roles).toContain(SemanticRole.DIALOG);
      expect(roles).toContain(SemanticRole.OVERLAY);
      expect(roles).toContain(SemanticRole.NAVIGATION);
      expect(roles).toContain(SemanticRole.HEADER);
      expect(roles).toContain(SemanticRole.TEXT);
      expect(roles).toContain(SemanticRole.CARD);
      expect(roles).toContain(SemanticRole.LIST);
      expect(roles).toContain(SemanticRole.MENU);
    });
  });

  describe('ARIA Attributes', () => {
    it('should get role-specific ARIA attributes for button', () => {
      const attributes = handler.getRoleAriaAttributes(SemanticRole.BUTTON);
      expect(attributes['role']).toBe('button');
    });

    it('should get role-specific ARIA attributes for dialog', () => {
      const attributes = handler.getRoleAriaAttributes(SemanticRole.DIALOG);
      expect(attributes['role']).toBe('dialog');
    });

    it('should get role-specific ARIA attributes for navigation', () => {
      const attributes = handler.getRoleAriaAttributes(SemanticRole.NAVIGATION);
      expect(attributes['role']).toBe('navigation');
    });

    it('should get role-specific ARIA attributes for list', () => {
      const attributes = handler.getRoleAriaAttributes(SemanticRole.LIST);
      expect(attributes['role']).toBe('list');
    });

    it('should get role-specific ARIA attributes for menu', () => {
      const attributes = handler.getRoleAriaAttributes(SemanticRole.MENU);
      expect(attributes['role']).toBe('menu');
    });

    it('should get role-specific ARIA attributes for header', () => {
      const attributes = handler.getRoleAriaAttributes(SemanticRole.HEADER);
      expect(attributes['role']).toBe('banner');
    });

    it('should return empty object for roles that use semantic HTML', () => {
      const surfaceAttributes = handler.getRoleAriaAttributes(SemanticRole.SURFACE);
      const textAttributes = handler.getRoleAriaAttributes(SemanticRole.TEXT);

      expect(Object.keys(surfaceAttributes)).toHaveLength(0);
      expect(Object.keys(textAttributes)).toHaveLength(0);
    });
  });

  describe('Theme Variations', () => {
    it('should work with different themes', () => {
      const lightClasses = handler.getThemeClasses(SemanticRole.BUTTON, LIGHT_THEME);
      const darkClasses = handler.getThemeClasses(SemanticRole.BUTTON, DARK_THEME);

      expect(lightClasses).toContain('theme-light');
      expect(darkClasses).toContain('theme-dark');
    });

    it('should generate different CSS variables for different themes', () => {
      const lightVariables = handler.getCSSVariables(SemanticRole.BUTTON, LIGHT_THEME);
      const darkVariables = handler.getCSSVariables(SemanticRole.BUTTON, DARK_THEME);

      expect(lightVariables['--theme-primary']).toBe('#1565c0');
      expect(darkVariables['--theme-primary']).toBe('#4fc3f7');
    });
  });
});
