/**
 * Interface Compliance Tests
 * Validates interface definitions and type safety
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  SemanticRole,
  ElementOptions
} from '../../element-factory/interfaces';
import { ThemeElementFactory } from '../../element-factory';
import { IThemeLogger, IPerformanceMonitor } from '../../interfaces/core-interfaces';
import { LIGHT_THEME } from '../../theme-config';

// Mock implementations
const mockLogger: IThemeLogger = {
  logOperationStart: vi.fn(),
  logOperationComplete: vi.fn(),
  logError: vi.fn(),
  logUserAction: vi.fn(),
  createChild: vi.fn().mockReturnValue({
    logOperationStart: vi.fn(),
    logOperationComplete: vi.fn(),
    logError: vi.fn(),
    logUserAction: vi.fn(),
    createChild: vi.fn()
  })
};

const mockPerformanceMonitor: IPerformanceMonitor = {
  measureOperation: vi.fn().mockImplementation(async (_, fn) => await fn()),
  measureOperationSync: vi.fn().mockImplementation((_, fn) => fn()),
  setPerformanceThreshold: vi.fn(),
  getPerformanceStats: vi.fn().mockReturnValue({
    operationCount: 0,
    totalTime: 0,
    averageTime: 0,
    maxTime: 0,
    minTime: 0
  })
};

describe('Interface Compliance', () => {
  let factory: ThemeElementFactory;

  beforeEach(() => {
    factory = new ThemeElementFactory(
      LIGHT_THEME,
      mockLogger,
      mockPerformanceMonitor
    );

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('SemanticRole Enum', () => {
    it('should define all required semantic roles', () => {
      expect(SemanticRole.SURFACE).toBe('surface');
      expect(SemanticRole.BUTTON).toBe('button');
      expect(SemanticRole.INPUT).toBe('input');
      expect(SemanticRole.DIALOG).toBe('dialog');
      expect(SemanticRole.OVERLAY).toBe('overlay');
      expect(SemanticRole.NAVIGATION).toBe('navigation');
      expect(SemanticRole.HEADER).toBe('header');
      expect(SemanticRole.TEXT).toBe('text');
      expect(SemanticRole.CARD).toBe('card');
      expect(SemanticRole.LIST).toBe('list');
      expect(SemanticRole.MENU).toBe('menu');
    });

    it('should have consistent string values', () => {
      const roles = Object.values(SemanticRole);
      const uniqueRoles = new Set(roles);

      // All roles should be unique
      expect(uniqueRoles.size).toBe(roles.length);

      // All roles should be lowercase strings
      roles.forEach(role => {
        expect(typeof role).toBe('string');
        expect(role).toBe(role.toLowerCase());
        expect(role).not.toContain(' ');
      });
    });
  });

  describe('ElementOptions Interface', () => {
    it('should accept valid element options', () => {
      const options: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE,
        className: 'test-class',
        attributes: { id: 'test-id' },
        textContent: 'Test content'
      };

      expect(options.tagName).toBe('div');
      expect(options.role).toBe(SemanticRole.SURFACE);
      expect(options.className).toBe('test-class');
      expect(options.attributes?.id).toBe('test-id');
      expect(options.textContent).toBe('Test content');
    });

    it('should support comprehensive configuration options', () => {
      const options: ElementOptions = {
        tagName: 'button',
        role: SemanticRole.BUTTON,
        className: ['btn', 'btn-primary'],
        attributes: { type: 'button', disabled: false },
        eventListeners: { click: () => {} },
        children: [document.createElement('span')],
        ariaAttributes: { 'aria-label': 'Test button' }
      };

      expect(options.className).toEqual(['btn', 'btn-primary']);
      expect(options.eventListeners?.click).toBeTypeOf('function');
      expect(options.children).toHaveLength(1);
      expect(options.ariaAttributes?.['aria-label']).toBe('Test button');
    });

    it('should support optional properties', () => {
      const minimalOptions: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE
      };

      expect(minimalOptions.tagName).toBe('div');
      expect(minimalOptions.role).toBe(SemanticRole.SURFACE);
      expect(minimalOptions.className).toBeUndefined();
      expect(minimalOptions.attributes).toBeUndefined();
    });
  });

  describe('IElementFactory Interface', () => {
    it('should implement IElementFactory interface', () => {
      expect(factory).toBeInstanceOf(ThemeElementFactory);

      // Check that all required methods exist
      expect(typeof factory.createElement).toBe('function');
      expect(typeof factory.createElements).toBe('function');
      expect(typeof factory.applyThemeToElement).toBe('function');
      expect(typeof factory.removeThemeFromElement).toBe('function');
      expect(typeof factory.updateElementTheme).toBe('function');
      expect(typeof factory.getThemeClasses).toBe('function');
      expect(typeof factory.getCSSVariables).toBe('function');
      expect(typeof factory.validateOptions).toBe('function');
    });

    it('should have correct method signatures', () => {
      // createElement should return Promise<HTMLElement>
      const createElementResult = factory.createElement({
        tagName: 'div',
        role: SemanticRole.SURFACE
      });
      expect(createElementResult).toBeInstanceOf(Promise);

      // getThemeClasses should return string array
      const classes = factory.getThemeClasses(SemanticRole.BUTTON);
      expect(Array.isArray(classes)).toBe(true);
      classes.forEach(cls => expect(typeof cls).toBe('string'));

      // getCSSVariables should return object
      const variables = factory.getCSSVariables(SemanticRole.INPUT);
      expect(typeof variables).toBe('object');
      expect(variables).not.toBeNull();
    });
  });

  describe('IUIPatternFactory Interface', () => {
    it('should implement IUIPatternFactory interface', () => {
      // Check that all UI pattern methods exist
      expect(typeof factory.createDialog).toBe('function');
      expect(typeof factory.createButton).toBe('function');
      expect(typeof factory.createInput).toBe('function');
      expect(typeof factory.createPanel).toBe('function');
    });

    it('should have correct UI pattern method signatures', () => {
      // All UI pattern methods should return Promises
      const dialogResult = factory.createDialog({});
      expect(dialogResult).toBeInstanceOf(Promise);

      const buttonResult = factory.createButton({ label: 'Test' });
      expect(buttonResult).toBeInstanceOf(Promise);

      const inputResult = factory.createInput({});
      expect(inputResult).toBeInstanceOf(Promise);

      const panelResult = factory.createPanel({});
      expect(panelResult).toBeInstanceOf(Promise);
    });
  });

  describe('IElementComposer Interface', () => {
    it('should implement IElementComposer interface', () => {
      // Check that all composition methods exist
      expect(typeof factory.composeElements).toBe('function');
      expect(typeof factory.createHierarchy).toBe('function');
      expect(typeof factory.cloneElement).toBe('function');
    });

    it('should have correct composition method signatures', () => {
      const container = document.createElement('div');
      const elements = [document.createElement('span')];

      // composeElements should return Promise<HTMLElement>
      const composeResult = factory.composeElements(container, elements);
      expect(composeResult).toBeInstanceOf(Promise);

      // createHierarchy should return Promise<HTMLElement>
      const hierarchyResult = factory.createHierarchy(
        { tagName: 'div', role: SemanticRole.SURFACE },
        []
      );
      expect(hierarchyResult).toBeInstanceOf(Promise);

      // cloneElement should return Promise<HTMLElement>
      const cloneResult = factory.cloneElement(document.createElement('div'));
      expect(cloneResult).toBeInstanceOf(Promise);
    });
  });

  describe('Type Safety', () => {
    it('should enforce correct tag name types', () => {
      // This test ensures TypeScript compilation catches invalid tag names
      const validOptions: ElementOptions = {
        tagName: 'div', // Valid HTML tag
        role: SemanticRole.SURFACE
      };

      expect(validOptions.tagName).toBe('div');
    });

    it('should enforce semantic role types', () => {
      // This test ensures TypeScript compilation catches invalid roles
      const validOptions: ElementOptions = {
        tagName: 'div',
        role: SemanticRole.SURFACE // Must be from SemanticRole enum
      };

      expect(Object.values(SemanticRole)).toContain(validOptions.role);
    });

    it('should support generic type parameters', async () => {
      // Test generic type support for createElement
      const divElement = await factory.createElement<HTMLDivElement>({
        tagName: 'div',
        role: SemanticRole.SURFACE
      });

      expect(divElement).toBeInstanceOf(HTMLDivElement);

      const buttonElement = await factory.createElement<HTMLButtonElement>({
        tagName: 'button',
        role: SemanticRole.BUTTON
      });

      expect(buttonElement).toBeInstanceOf(HTMLButtonElement);
    });
  });

  describe('SOLID Principles Compliance', () => {
    it('should follow Interface Segregation Principle', () => {
      // Interfaces should be focused and not force implementation of unneeded methods

      // IElementFactory focuses on core element operations
      const elementFactoryMethods = [
        'createElement', 'createElements', 'applyThemeToElement',
        'removeThemeFromElement', 'updateElementTheme', 'getThemeClasses',
        'getCSSVariables', 'validateOptions'
      ];

      // IUIPatternFactory focuses on UI patterns
      const uiPatternMethods = [
        'createDialog', 'createButton', 'createInput', 'createPanel'
      ];

      // IElementComposer focuses on composition
      const composerMethods = [
        'composeElements', 'createHierarchy', 'cloneElement'
      ];

      // Verify methods exist and are properly segregated
      elementFactoryMethods.forEach(method => {
        expect(typeof (factory as any)[method]).toBe('function');
      });

      uiPatternMethods.forEach(method => {
        expect(typeof (factory as any)[method]).toBe('function');
      });

      composerMethods.forEach(method => {
        expect(typeof (factory as any)[method]).toBe('function');
      });
    });

    it('should follow Dependency Inversion Principle', () => {
      // Factory should depend on abstractions (interfaces), not concrete implementations
      expect(factory).toHaveProperty('getCurrentTheme');
      expect(factory).toHaveProperty('setCurrentTheme');

      // Should accept interface-based dependencies
      expect(typeof factory.getCurrentTheme).toBe('function');
      expect(typeof factory.setCurrentTheme).toBe('function');
    });
  });
});
