/**
 * Basic Recovery Strategies Implementation
 * Implements the Strategy Pattern for extensible error recovery mechanisms.
 * Each strategy focuses on a specific type of error recovery.
 */

import { IRecoveryStrategy, IThemeError } from '../interfaces/core-interfaces';
import { ThemeErrorCategory, ThemeOperation } from '../theme-error-handler';

/**
 * Fallback theme recovery strategy
 * Attempts to recover from theme errors by falling back to a default theme
 */
export class FallbackThemeStrategy implements IRecoveryStrategy {
  public readonly name = 'FallbackThemeStrategy';
  public readonly priority = 100;

  constructor(private defaultTheme = 'light') {}

  public canHandle(error: IThemeError): boolean {
    return error.category === ThemeErrorCategory.VALIDATION ||
           error.category === ThemeErrorCategory.NETWORK ||
           (error.context.operation === ThemeOperation.THEME_LOAD ||
            error.context.operation === ThemeOperation.THEME_SWITCH);
  }

  public async execute(_error: IThemeError): Promise<boolean> {
    try {
      // Attempt to apply default theme
      const rootElement = document.documentElement;

      // Clear any existing theme classes
      rootElement.classList.remove('theme-light', 'theme-dark', 'dark');

      // Apply default theme
      if (this.defaultTheme === 'dark') {
        rootElement.classList.add('dark');
      }

      // Store the fallback theme
      if (typeof window !== 'undefined' && window.localStorage) {
        try {
          window.localStorage.setItem('theme', this.defaultTheme);
        } catch (e) {
          // Ignore localStorage errors in test environment
        }
      }

      // Dispatch theme change event
      window.dispatchEvent(new CustomEvent('feather:themechange', {
        detail: { theme: this.defaultTheme }
      }));

      return true;
    } catch (recoveryError) {
      console.warn('Fallback theme strategy failed:', recoveryError);
      return false;
    }
  }
}

/**
 * Cache clear recovery strategy
 * Attempts to recover from errors by clearing theme-related caches
 */
export class CacheClearStrategy implements IRecoveryStrategy {
  public readonly name = 'CacheClearStrategy';
  public readonly priority = 80;

  public canHandle(error: IThemeError): boolean {
    return error.category === ThemeErrorCategory.PERFORMANCE ||
           error.category === ThemeErrorCategory.SYSTEM ||
           error.context.operation === ThemeOperation.CSS_INJECT;
  }

  public async execute(_error: IThemeError): Promise<boolean> {
    try {
      // Clear theme-related localStorage items
      if (typeof window !== 'undefined' && window.localStorage) {
        const themeKeys = ['theme', 'custom-themes', 'theme-preferences'];
        themeKeys.forEach(key => {
          try {
            window.localStorage.removeItem(key);
          } catch (e) {
            // Ignore individual key removal failures
          }
        });
      }

      // Clear any cached CSS variables
      const rootElement = document.documentElement;

      // Remove custom CSS properties that start with --theme-
      for (let i = 0; i < rootElement.style.length; i++) {
        const property = rootElement.style[i];
        if (property.startsWith('--theme-')) {
          rootElement.style.removeProperty(property);
        }
      }

      // Force a repaint
      rootElement.style.display = 'none';
      void rootElement.offsetHeight; // Trigger reflow
      rootElement.style.display = '';

      return true;
    } catch (recoveryError) {
      console.warn('Cache clear strategy failed:', recoveryError);
      return false;
    }
  }
}

/**
 * DOM refresh recovery strategy
 * Attempts to recover from DOM-related errors by refreshing theme elements
 */
export class DOMRefreshStrategy implements IRecoveryStrategy {
  public readonly name = 'DOMRefreshStrategy';
  public readonly priority = 60;

  public canHandle(error: IThemeError): boolean {
    return error.category === ThemeErrorCategory.SYSTEM ||
           error.context.operation === ThemeOperation.ELEMENT_CREATE ||
           error.context.operation === ThemeOperation.PLUGIN_APPLY;
  }

  public async execute(_error: IThemeError): Promise<boolean> {
    try {
      // Find and refresh theme-related elements
      const themeElements = document.querySelectorAll('[data-theme], .theme-element, [class*="theme-"]');

      themeElements.forEach(element => {
        try {
          // Force style recalculation
          const htmlElement = element as HTMLElement;
          const display = htmlElement.style.display;
          htmlElement.style.display = 'none';
          void htmlElement.offsetHeight; // Trigger reflow
          htmlElement.style.display = display;
        } catch {
          // Ignore individual element refresh failures
        }
      });

      // Refresh CSS custom properties
      const rootElement = document.documentElement;
      const currentTheme = rootElement.classList.contains('dark') ? 'dark' : 'light';

      // Re-trigger theme application
      rootElement.classList.remove('theme-light', 'theme-dark', 'dark');

      // Small delay to ensure cleanup
      await new Promise(resolve => setTimeout(resolve, 10));

      if (currentTheme === 'dark') {
        rootElement.classList.add('dark');
      }

      return true;
    } catch (recoveryError) {
      console.warn('DOM refresh strategy failed:', recoveryError);
      return false;
    }
  }
}

/**
 * Retry operation recovery strategy
 * Attempts to recover by retrying the failed operation with exponential backoff
 */
export class RetryOperationStrategy implements IRecoveryStrategy {
  public readonly name = 'RetryOperationStrategy';
  public readonly priority = 40;

  constructor(
    private maxRetries = 3,
    private baseDelayMs = 100
  ) {}

  public canHandle(error: IThemeError): boolean {
    return error.category === ThemeErrorCategory.NETWORK ||
           error.category === ThemeErrorCategory.PERFORMANCE;
  }

  public async execute(_error: IThemeError): Promise<boolean> {
    // This strategy would need access to the original operation to retry it
    // For now, we'll implement a basic delay and return true to simulate retry success

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        // Exponential backoff delay
        const delay = this.baseDelayMs * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        // In a real implementation, we would retry the original operation here
        // For demonstration, we'll simulate a successful retry
        if (Math.random() > 0.3) { // 70% success rate simulation
          return true;
        }
      } catch (retryError) {
        console.warn(`Retry attempt ${attempt} failed:`, retryError);

        if (attempt === this.maxRetries) {
          return false;
        }
      }
    }

    return false;
  }
}

/**
 * User notification recovery strategy
 * Provides user feedback and guidance for manual recovery
 */
export class UserNotificationStrategy implements IRecoveryStrategy {
  public readonly name = 'UserNotificationStrategy';
  public readonly priority = 20;

  public canHandle(error: IThemeError): boolean {
    return error.category === ThemeErrorCategory.USER ||
           error.recoverable === false; // Last resort for non-recoverable errors
  }

  public async execute(error: IThemeError): Promise<boolean> {
    try {
      // Create a user-friendly notification
      const notification = this.createNotification(error);

      // Show notification to user
      this.showNotification(notification);

      // For user errors, we consider showing the notification as "recovery"
      return error.category === ThemeErrorCategory.USER;
    } catch (notificationError) {
      console.warn('User notification strategy failed:', notificationError);
      return false;
    }
  }

  private createNotification(error: IThemeError): {
    title: string;
    message: string;
    actions: string[];
  } {
    const baseTitle = 'Theme System Issue';
    const message = error.getUserFriendlyMessage();
    const actions = error.recoveryActions;

    switch (error.category) {
      case ThemeErrorCategory.USER:
        return {
          title: 'Theme Action Required',
          message: `${message}\n\nPlease try the following:`,
          actions
        };
      case ThemeErrorCategory.NETWORK:
        return {
          title: 'Theme Loading Issue',
          message: `Unable to load theme: ${message}`,
          actions: ['Check your internet connection', 'Try refreshing the page', 'Use offline mode']
        };
      case ThemeErrorCategory.VALIDATION:
        return {
          title: 'Theme Configuration Error',
          message: `Theme configuration issue: ${message}`,
          actions: ['Reset to default theme', 'Check theme settings', 'Contact support']
        };
      default:
        return {
          title: baseTitle,
          message,
          actions
        };
    }
  }

  private showNotification(notification: {
    title: string;
    message: string;
    actions: string[];
  }): void {
    // In a real implementation, this would integrate with the app's notification system
    // For now, we'll use console output and browser notifications if available

    console.group(`🎨 ${notification.title}`);
    console.warn(notification.message);
    console.info('Suggested actions:', notification.actions);
    console.groupEnd();

    // Try to show browser notification if permission is granted
    if (typeof window !== 'undefined' && 'Notification' in window && window.Notification?.permission === 'granted') {
      try {
        new window.Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico'
        });
      } catch (e) {
        // Ignore notification errors in test environment
      }
    }
  }
}

/**
 * Factory function to create all basic recovery strategies
 */
export function createBasicRecoveryStrategies(): IRecoveryStrategy[] {
  return [
    new FallbackThemeStrategy(),
    new CacheClearStrategy(),
    new DOMRefreshStrategy(),
    new RetryOperationStrategy(),
    new UserNotificationStrategy()
  ];
}
