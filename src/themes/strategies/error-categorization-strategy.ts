/**
 * Error Categorization Strategy Implementation
 * Implements the Strategy Pattern for extensible error categorization.
 * Follows Single Responsibility Principle by focusing solely on error categorization logic.
 */

import { ThemeErrorCategory, ThemeOperation } from '../theme-types';
import { FeatherError } from '../../utils/error';

/**
 * Interface for error categorization strategies
 * Follows Interface Segregation Principle (ISP)
 */
export interface IErrorCategorizationStrategy {
  /** Strategy name for identification */
  readonly name: string;
  /** Priority for strategy selection (higher = more preferred) */
  readonly priority: number;
  
  /**
   * Check if this strategy can categorize the given error
   */
  canCategorize(error: Error, operation: ThemeOperation): boolean;
  
  /**
   * Categorize the error
   */
  categorize(error: Error, operation: ThemeOperation): ThemeErrorCategory;
}

/**
 * Operation-based error categorization strategy
 * Categorizes errors based on the theme operation that failed
 */
export class OperationBasedCategorizationStrategy implements IErrorCategorizationStrategy {
  public readonly name = 'OperationBasedCategorizationStrategy';
  public readonly priority = 100;

  public canCategorize(_error: Error, operation: ThemeOperation): boolean {
    // This strategy can handle any operation
    return Object.values(ThemeOperation).includes(operation);
  }

  public categorize(_error: Error, operation: ThemeOperation): ThemeErrorCategory {
    switch (operation) {
      case ThemeOperation.THEME_VALIDATE:
        return ThemeErrorCategory.VALIDATION;
      
      case ThemeOperation.THEME_LOAD:
      case ThemeOperation.THEME_SAVE:
        return ThemeErrorCategory.NETWORK;
      
      case ThemeOperation.THEME_SWITCH:
      case ThemeOperation.CSS_INJECT:
        return ThemeErrorCategory.PERFORMANCE;
      
      case ThemeOperation.ELEMENT_CREATE:
      case ThemeOperation.PLUGIN_APPLY:
        return ThemeErrorCategory.SYSTEM;
      
      case ThemeOperation.CUSTOM_THEME_REGISTER:
        return ThemeErrorCategory.USER;
      
      default:
        return ThemeErrorCategory.SYSTEM;
    }
  }
}

/**
 * Error message pattern-based categorization strategy
 * Categorizes errors based on error message patterns
 */
export class MessagePatternCategorizationStrategy implements IErrorCategorizationStrategy {
  public readonly name = 'MessagePatternCategorizationStrategy';
  public readonly priority = 80;

  private readonly patterns = new Map<ThemeErrorCategory, RegExp[]>([
    [ThemeErrorCategory.VALIDATION, [
      /invalid|validation|format|schema|required|missing/i,
      /parse|syntax|malformed|corrupt/i
    ]],
    [ThemeErrorCategory.NETWORK, [
      /network|fetch|request|response|timeout|connection/i,
      /404|500|503|offline|unreachable/i
    ]],
    [ThemeErrorCategory.PERFORMANCE, [
      /performance|slow|timeout|memory|leak|throttle/i,
      /benchmark|threshold|limit|quota/i
    ]],
    [ThemeErrorCategory.USER, [
      /permission|access|denied|unauthorized|forbidden/i,
      /user|input|preference|setting/i
    ]],
    [ThemeErrorCategory.SYSTEM, [
      /system|internal|unexpected|unknown|fatal/i,
      /dom|element|node|document/i
    ]]
  ]);

  public canCategorize(error: Error, _operation: ThemeOperation): boolean {
    // Check if error message matches any known patterns
    const message = error.message.toLowerCase();
    for (const patterns of this.patterns.values()) {
      if (patterns.some(pattern => pattern.test(message))) {
        return true;
      }
    }
    return false;
  }

  public categorize(error: Error, _operation: ThemeOperation): ThemeErrorCategory {
    const message = error.message.toLowerCase();
    
    for (const [category, patterns] of this.patterns.entries()) {
      if (patterns.some(pattern => pattern.test(message))) {
        return category;
      }
    }
    
    // Fallback to system error if no pattern matches
    return ThemeErrorCategory.SYSTEM;
  }
}

/**
 * Error type-based categorization strategy
 * Categorizes errors based on JavaScript error types and custom error codes
 */
export class ErrorTypeCategorizationStrategy implements IErrorCategorizationStrategy {
  public readonly name = 'ErrorTypeCategorizationStrategy';
  public readonly priority = 90;

  public canCategorize(error: Error, _operation: ThemeOperation): boolean {
    // Check if it's a FeatherError with a code or a known JavaScript error type
    return error instanceof FeatherError || 
           error instanceof TypeError ||
           error instanceof ReferenceError ||
           error instanceof SyntaxError ||
           error instanceof RangeError;
  }

  public categorize(error: Error, _operation: ThemeOperation): ThemeErrorCategory {
    // Handle FeatherError with specific codes
    if (error instanceof FeatherError) {
      const code = error.code.toUpperCase();
      
      if (code.includes('VALIDATION') || code.includes('PARSE')) {
        return ThemeErrorCategory.VALIDATION;
      }
      if (code.includes('NETWORK') || code.includes('FETCH')) {
        return ThemeErrorCategory.NETWORK;
      }
      if (code.includes('PERFORMANCE') || code.includes('TIMEOUT')) {
        return ThemeErrorCategory.PERFORMANCE;
      }
      if (code.includes('USER') || code.includes('PERMISSION')) {
        return ThemeErrorCategory.USER;
      }
      
      return ThemeErrorCategory.SYSTEM;
    }

    // Handle standard JavaScript error types
    if (error instanceof TypeError || error instanceof ReferenceError) {
      return ThemeErrorCategory.SYSTEM;
    }
    
    if (error instanceof SyntaxError) {
      return ThemeErrorCategory.VALIDATION;
    }
    
    if (error instanceof RangeError) {
      return ThemeErrorCategory.PERFORMANCE;
    }

    return ThemeErrorCategory.SYSTEM;
  }
}

/**
 * Composite error categorization strategy manager
 * Manages multiple categorization strategies and selects the best match
 */
export class ErrorCategorizationManager {
  private strategies: IErrorCategorizationStrategy[] = [];

  constructor() {
    // Register default strategies
    this.registerStrategy(new OperationBasedCategorizationStrategy());
    this.registerStrategy(new MessagePatternCategorizationStrategy());
    this.registerStrategy(new ErrorTypeCategorizationStrategy());
  }

  /**
   * Register a categorization strategy
   */
  public registerStrategy(strategy: IErrorCategorizationStrategy): void {
    this.strategies.push(strategy);
    // Sort by priority (highest first)
    this.strategies.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Categorize an error using the best available strategy
   */
  public categorizeError(error: Error, operation: ThemeOperation): ThemeErrorCategory {
    // Find the first strategy that can handle this error
    for (const strategy of this.strategies) {
      if (strategy.canCategorize(error, operation)) {
        return strategy.categorize(error, operation);
      }
    }

    // Fallback to system error if no strategy can handle it
    return ThemeErrorCategory.SYSTEM;
  }

  /**
   * Get all registered strategies
   */
  public getStrategies(): ReadonlyArray<IErrorCategorizationStrategy> {
    return this.strategies;
  }

  /**
   * Clear all strategies
   */
  public clearStrategies(): void {
    this.strategies = [];
  }
}
