/**
 * Performance Threshold Strategy Implementation
 * Implements the Strategy Pattern for dynamic performance threshold management.
 * Follows Single Responsibility Principle by focusing solely on threshold logic.
 */

import { ThemeOperation } from '../theme-types';

/**
 * Interface for performance threshold strategies
 * Follows Interface Segregation Principle (ISP)
 */
export interface IPerformanceThresholdStrategy {
  /** Strategy name for identification */
  readonly name: string;
  /** Priority for strategy selection (higher = more preferred) */
  readonly priority: number;

  /**
   * Check if this strategy can determine threshold for the given operation
   */
  canDetermineThreshold(operation: ThemeOperation, context?: ThresholdContext): boolean;

  /**
   * Determine the performance threshold for an operation
   */
  determineThreshold(operation: ThemeOperation, context?: ThresholdContext): number;
}

/**
 * Context information for threshold determination
 */
export interface ThresholdContext {
  /** Number of elements that will be affected */
  elementCount?: number;
  /** Current system performance state */
  systemLoad?: 'low' | 'medium' | 'high';
  /** User device capabilities */
  deviceCapabilities?: {
    memory?: number; // in MB
    cores?: number;
    isMobile?: boolean;
  };
  /** Historical performance data */
  historicalData?: {
    averageDuration?: number;
    successRate?: number;
  };
  /** User preferences */
  userPreferences?: {
    performanceMode?: 'fast' | 'balanced' | 'quality';
    animationsEnabled?: boolean;
  };
}

/**
 * Static threshold strategy
 * Uses predefined thresholds for each operation type
 */
export class StaticThresholdStrategy implements IPerformanceThresholdStrategy {
  public readonly name = 'StaticThresholdStrategy';
  public readonly priority = 50;

  private readonly defaultThresholds = new Map<ThemeOperation, number>([
    [ThemeOperation.THEME_SWITCH, 100],
    [ThemeOperation.THEME_LOAD, 50],
    [ThemeOperation.THEME_SAVE, 200],
    [ThemeOperation.THEME_VALIDATE, 10],
    [ThemeOperation.ELEMENT_CREATE, 1],
    [ThemeOperation.CSS_INJECT, 10],
    [ThemeOperation.PLUGIN_APPLY, 50],
    [ThemeOperation.CUSTOM_THEME_REGISTER, 100]
  ]);

  public canDetermineThreshold(operation: ThemeOperation, _context?: ThresholdContext): boolean {
    return this.defaultThresholds.has(operation);
  }

  public determineThreshold(operation: ThemeOperation, _context?: ThresholdContext): number {
    return this.defaultThresholds.get(operation) || 100;
  }
}

/**
 * Adaptive threshold strategy
 * Adjusts thresholds based on system capabilities and context
 */
export class AdaptiveThresholdStrategy implements IPerformanceThresholdStrategy {
  public readonly name = 'AdaptiveThresholdStrategy';
  public readonly priority = 80;

  private baseThresholds = new Map<ThemeOperation, number>([
    [ThemeOperation.THEME_SWITCH, 100],
    [ThemeOperation.THEME_LOAD, 50],
    [ThemeOperation.THEME_SAVE, 200],
    [ThemeOperation.THEME_VALIDATE, 10],
    [ThemeOperation.ELEMENT_CREATE, 1],
    [ThemeOperation.CSS_INJECT, 10],
    [ThemeOperation.PLUGIN_APPLY, 50],
    [ThemeOperation.CUSTOM_THEME_REGISTER, 100]
  ]);

  public canDetermineThreshold(operation: ThemeOperation, context?: ThresholdContext): boolean {
    return this.baseThresholds.has(operation) && context !== undefined;
  }

  public determineThreshold(operation: ThemeOperation, context?: ThresholdContext): number {
    const baseThreshold = this.baseThresholds.get(operation) || 100;

    if (!context) {
      return baseThreshold;
    }

    let adjustedThreshold = baseThreshold;

    // Adjust based on element count
    if (context.elementCount) {
      const elementMultiplier = this.calculateElementMultiplier(context.elementCount);
      adjustedThreshold *= elementMultiplier;
    }

    // Adjust based on system load
    if (context.systemLoad) {
      const loadMultiplier = this.calculateLoadMultiplier(context.systemLoad);
      adjustedThreshold *= loadMultiplier;
    }

    // Adjust based on device capabilities
    if (context.deviceCapabilities) {
      const deviceMultiplier = this.calculateDeviceMultiplier(context.deviceCapabilities);
      adjustedThreshold *= deviceMultiplier;
    }

    // Adjust based on user preferences
    if (context.userPreferences) {
      const preferenceMultiplier = this.calculatePreferenceMultiplier(context.userPreferences);
      adjustedThreshold *= preferenceMultiplier;
    }

    return Math.round(adjustedThreshold);
  }

  private calculateElementMultiplier(elementCount: number): number {
    if (elementCount < 10) return 1.0;
    if (elementCount < 100) return 1.2;
    if (elementCount < 1000) return 1.5;
    return 2.0;
  }

  private calculateLoadMultiplier(systemLoad: 'low' | 'medium' | 'high'): number {
    switch (systemLoad) {
      case 'low': return 0.8;
      case 'medium': return 1.0;
      case 'high': return 1.5;
      default: return 1.0;
    }
  }

  private calculateDeviceMultiplier(capabilities: ThresholdContext['deviceCapabilities']): number {
    let multiplier = 1.0;

    if (capabilities?.isMobile) {
      multiplier *= 1.3; // Mobile devices get more lenient thresholds
    }

    if (capabilities?.memory) {
      if (capabilities.memory < 2048) { // Less than 2GB
        multiplier *= 1.4;
      } else if (capabilities.memory > 8192) { // More than 8GB
        multiplier *= 0.8;
      }
    }

    if (capabilities?.cores) {
      if (capabilities.cores <= 2) {
        multiplier *= 1.2;
      } else if (capabilities.cores >= 8) {
        multiplier *= 0.9;
      }
    }

    return multiplier;
  }

  private calculatePreferenceMultiplier(preferences: ThresholdContext['userPreferences']): number {
    let multiplier = 1.0;

    if (preferences?.performanceMode) {
      switch (preferences.performanceMode) {
        case 'fast':
          multiplier *= 0.7; // Stricter thresholds for fast mode
          break;
        case 'quality':
          multiplier *= 1.5; // More lenient for quality mode
          break;
        case 'balanced':
        default:
          multiplier *= 1.0;
          break;
      }
    }

    if (preferences?.animationsEnabled === false) {
      multiplier *= 0.8; // Stricter thresholds when animations are disabled
    }

    return multiplier;
  }
}

/**
 * Learning threshold strategy
 * Uses historical performance data to optimize thresholds over time
 */
export class LearningThresholdStrategy implements IPerformanceThresholdStrategy {
  public readonly name = 'LearningThresholdStrategy';
  public readonly priority = 90;

  private readonly learningRate = 0.1;
  // private readonly _minSamples = 5; // Reserved for future use

  public canDetermineThreshold(_operation: ThemeOperation, context?: ThresholdContext): boolean {
    return context?.historicalData !== undefined &&
           context.historicalData.averageDuration !== undefined &&
           context.historicalData.successRate !== undefined;
  }

  public determineThreshold(operation: ThemeOperation, context?: ThresholdContext): number {
    if (!context?.historicalData) {
      // Fallback to static threshold
      return new StaticThresholdStrategy().determineThreshold(operation, context);
    }

    const { averageDuration, successRate } = context.historicalData;

    if (averageDuration === undefined || successRate === undefined) {
      return new StaticThresholdStrategy().determineThreshold(operation, context);
    }

    // Calculate adaptive threshold based on historical performance
    let adaptiveThreshold = averageDuration;

    // Adjust based on success rate
    if (successRate < 0.8) {
      // Low success rate - increase threshold to be more lenient
      adaptiveThreshold *= 1.5;
    } else if (successRate > 0.95) {
      // High success rate - decrease threshold to be more strict
      adaptiveThreshold *= 0.9;
    }

    // Apply learning rate to smooth changes
    const staticThreshold = new StaticThresholdStrategy().determineThreshold(operation, context);
    const learnedThreshold = staticThreshold +
      (adaptiveThreshold - staticThreshold) * this.learningRate;

    return Math.round(Math.max(learnedThreshold, staticThreshold * 0.5));
  }
}

/**
 * Performance threshold manager
 * Manages multiple threshold strategies and selects the best one
 */
export class PerformanceThresholdManager {
  private strategies: IPerformanceThresholdStrategy[] = [];

  constructor() {
    // Register default strategies
    this.registerStrategy(new StaticThresholdStrategy());
    this.registerStrategy(new AdaptiveThresholdStrategy());
    this.registerStrategy(new LearningThresholdStrategy());
  }

  /**
   * Register a threshold strategy
   */
  public registerStrategy(strategy: IPerformanceThresholdStrategy): void {
    this.strategies.push(strategy);
    // Sort by priority (highest first)
    this.strategies.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Determine the best threshold for an operation
   */
  public determineThreshold(operation: ThemeOperation, context?: ThresholdContext): number {
    // Find the first strategy that can handle this operation with the given context
    for (const strategy of this.strategies) {
      if (strategy.canDetermineThreshold(operation, context)) {
        return strategy.determineThreshold(operation, context);
      }
    }

    // Fallback to static strategy if no other strategy can handle it
    return new StaticThresholdStrategy().determineThreshold(operation, context);
  }

  /**
   * Get all registered strategies
   */
  public getStrategies(): ReadonlyArray<IPerformanceThresholdStrategy> {
    return this.strategies;
  }

  /**
   * Clear all strategies
   */
  public clearStrategies(): void {
    this.strategies = [];
  }

  /**
   * Get system performance context
   */
  public getSystemContext(): ThresholdContext {
    const context: ThresholdContext = {};

    // Detect device capabilities
    if (typeof navigator !== 'undefined') {
      context.deviceCapabilities = {
        memory: (navigator as any).deviceMemory ? (navigator as any).deviceMemory * 1024 : undefined,
        cores: navigator.hardwareConcurrency,
        isMobile: /Mobi|Android/i.test(navigator.userAgent)
      };
    }

    // Estimate system load based on performance timing
    if (typeof performance !== 'undefined' && performance.now) {
      const start = performance.now();
      // Simple CPU-bound operation to estimate load
      for (let i = 0; i < 10000; i++) {
        Math.random();
      }
      const duration = performance.now() - start;

      if (duration < 1) {
        context.systemLoad = 'low';
      } else if (duration < 5) {
        context.systemLoad = 'medium';
      } else {
        context.systemLoad = 'high';
      }
    }

    return context;
  }
}

/**
 * Factory function to create threshold strategies
 */
export function createThresholdStrategies(): IPerformanceThresholdStrategy[] {
  return [
    new StaticThresholdStrategy(),
    new AdaptiveThresholdStrategy(),
    new LearningThresholdStrategy()
  ];
}
