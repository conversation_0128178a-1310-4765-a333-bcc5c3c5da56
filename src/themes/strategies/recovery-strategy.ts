/**
 * Enhanced Recovery Strategy Implementation
 * Implements the Strategy Pattern for advanced error recovery mechanisms.
 * Extends the basic recovery strategies with more sophisticated recovery logic.
 */

import { IRecoveryStrategy, IThemeError } from '../interfaces/core-interfaces';
import { ThemeErrorCategory } from '../theme-types';

/**
 * Context-aware recovery strategy
 * Adapts recovery behavior based on error context and system state
 */
export class ContextAwareRecoveryStrategy implements IRecoveryStrategy {
  public readonly name = 'ContextAwareRecoveryStrategy';
  public readonly priority = 120;

  public canHandle(error: IThemeError): boolean {
    // This strategy can handle any error but prioritizes complex scenarios
    return error.context.userState !== undefined ||
           error.context.performance !== undefined ||
           error.context.additionalData !== undefined;
  }

  public async execute(error: IThemeError): Promise<boolean> {
    try {
      const context = error.context;

      // Analyze user state for recovery decisions
      if (context.userState) {
        return await this.recoverBasedOnUserState(error);
      }

      // Analyze performance context for recovery decisions
      if (context.performance) {
        return await this.recoverBasedOnPerformance(error);
      }

      // Use additional data for custom recovery logic
      if (context.additionalData) {
        return await this.recoverBasedOnAdditionalData(error);
      }

      return false;
    } catch (recoveryError) {
      console.warn('Context-aware recovery strategy failed:', recoveryError);
      return false;
    }
  }

  private async recoverBasedOnUserState(error: IThemeError): Promise<boolean> {
    const userState = error.context.userState!;

    // If user has a custom theme, try to fall back to built-in theme
    if (userState.isCustomTheme && userState.currentTheme) {
      try {
        const fallbackTheme = userState.currentTheme.includes('dark') ? 'dark' : 'light';
        await this.applyFallbackTheme(fallbackTheme);
        return true;
      } catch {
        return false;
      }
    }

    // If there are many elements, try to reduce complexity
    if (userState.elementCount && userState.elementCount > 1000) {
      return await this.simplifyThemeApplication();
    }

    return false;
  }

  private async recoverBasedOnPerformance(error: IThemeError): Promise<boolean> {
    const performance = error.context.performance!;

    // If memory usage is high, try to clear caches
    if (performance.memoryUsage && performance.memoryUsage > 50 * 1024 * 1024) { // 50MB
      return await this.clearMemoryCaches();
    }

    // If execution time was long, try to optimize
    if (performance.executionTime && performance.executionTime > 1000) { // 1 second
      return await this.optimizeThemeApplication();
    }

    return false;
  }

  private async recoverBasedOnAdditionalData(error: IThemeError): Promise<boolean> {
    const additionalData = error.context.additionalData!;

    // Handle specific recovery scenarios based on additional data
    if (additionalData.retryCount && typeof additionalData.retryCount === 'number') {
      if (additionalData.retryCount < 3) {
        // Implement exponential backoff retry
        const delay = Math.pow(2, additionalData.retryCount) * 100;
        await new Promise(resolve => setTimeout(resolve, delay));
        return true;
      }
    }

    return false;
  }

  private async applyFallbackTheme(theme: string): Promise<void> {
    const rootElement = document.documentElement;
    rootElement.classList.remove('theme-light', 'theme-dark', 'dark');

    if (theme === 'dark') {
      rootElement.classList.add('dark');
    }

    // Store fallback theme
    localStorage.setItem('theme', theme);

    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('feather:themechange', {
      detail: { theme }
    }));
  }

  private async simplifyThemeApplication(): Promise<boolean> {
    try {
      // Disable animations temporarily
      const style = document.createElement('style');
      style.textContent = '* { transition: none !important; animation: none !important; }';
      document.head.appendChild(style);

      // Remove after a short delay
      setTimeout(() => {
        document.head.removeChild(style);
      }, 100);

      return true;
    } catch {
      return false;
    }
  }

  private async clearMemoryCaches(): Promise<boolean> {
    try {
      // Clear theme-related caches
      const themeKeys = ['theme', 'custom-themes', 'theme-preferences', 'theme-cache'];
      themeKeys.forEach(key => {
        try {
          localStorage.removeItem(key);
          sessionStorage.removeItem(key);
        } catch {
          // Ignore individual failures
        }
      });

      // Force garbage collection if available
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc();
      }

      return true;
    } catch {
      return false;
    }
  }

  private async optimizeThemeApplication(): Promise<boolean> {
    try {
      // Use requestAnimationFrame for smoother updates
      return new Promise<boolean>(resolve => {
        requestAnimationFrame(() => {
          try {
            // Batch DOM updates
            const rootElement = document.documentElement;
            const currentTheme = rootElement.classList.contains('dark') ? 'dark' : 'light';

            // Re-apply theme with optimization
            rootElement.style.willChange = 'auto';
            rootElement.classList.remove('theme-light', 'theme-dark', 'dark');

            requestAnimationFrame(() => {
              if (currentTheme === 'dark') {
                rootElement.classList.add('dark');
              }
              rootElement.style.willChange = '';
              resolve(true);
            });
          } catch {
            resolve(false);
          }
        });
      });
    } catch {
      return false;
    }
  }
}

/**
 * Progressive recovery strategy
 * Implements a multi-step recovery process with increasing intervention levels
 */
export class ProgressiveRecoveryStrategy implements IRecoveryStrategy {
  public readonly name = 'ProgressiveRecoveryStrategy';
  public readonly priority = 110;

  private recoverySteps = [
    'refresh_styles',
    'clear_cache',
    'reset_theme',
    'fallback_theme',
    'emergency_reset'
  ];

  public canHandle(error: IThemeError): boolean {
    // Handle errors that might benefit from progressive recovery
    return error.category === ThemeErrorCategory.SYSTEM ||
           error.category === ThemeErrorCategory.PERFORMANCE ||
           (error.context.additionalData?.progressiveRecovery === true);
  }

  public async execute(error: IThemeError): Promise<boolean> {
    const startStep = this.determineStartStep(error);

    for (let i = startStep; i < this.recoverySteps.length; i++) {
      const step = this.recoverySteps[i];

      try {
        const success = await this.executeRecoveryStep(step);
        if (success) {
          return true;
        }
      } catch (stepError) {
        console.warn(`Progressive recovery step '${step}' failed:`, stepError);
      }

      // Small delay between steps
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    return false;
  }

  private determineStartStep(error: IThemeError): number {
    // Start with more aggressive steps for severe errors
    if (error.category === ThemeErrorCategory.SYSTEM) {
      return 2; // Start with reset_theme
    }

    if (error.category === ThemeErrorCategory.PERFORMANCE) {
      return 1; // Start with clear_cache
    }

    return 0; // Start with refresh_styles
  }

  private async executeRecoveryStep(step: string): Promise<boolean> {
    switch (step) {
      case 'refresh_styles':
        return await this.refreshStyles();

      case 'clear_cache':
        return await this.clearCache();

      case 'reset_theme':
        return await this.resetTheme();

      case 'fallback_theme':
        return await this.applyFallbackTheme();

      case 'emergency_reset':
        return await this.emergencyReset();

      default:
        return false;
    }
  }

  private async refreshStyles(): Promise<boolean> {
    try {
      // Force style recalculation
      const rootElement = document.documentElement;
      const display = rootElement.style.display;
      rootElement.style.display = 'none';
      void rootElement.offsetHeight; // Trigger reflow
      rootElement.style.display = display;
      return true;
    } catch {
      return false;
    }
  }

  private async clearCache(): Promise<boolean> {
    try {
      // Clear theme-related storage
      const keys = ['theme', 'theme-cache', 'custom-themes'];
      keys.forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      });
      return true;
    } catch {
      return false;
    }
  }

  private async resetTheme(): Promise<boolean> {
    try {
      const rootElement = document.documentElement;
      rootElement.className = '';
      rootElement.removeAttribute('data-theme');

      // Remove all custom CSS properties
      for (let i = rootElement.style.length - 1; i >= 0; i--) {
        const property = rootElement.style[i];
        if (property.startsWith('--theme-')) {
          rootElement.style.removeProperty(property);
        }
      }

      return true;
    } catch {
      return false;
    }
  }

  private async applyFallbackTheme(): Promise<boolean> {
    try {
      const rootElement = document.documentElement;
      rootElement.classList.add('theme-light');
      localStorage.setItem('theme', 'light');

      window.dispatchEvent(new CustomEvent('feather:themechange', {
        detail: { theme: 'light' }
      }));

      return true;
    } catch {
      return false;
    }
  }

  private async emergencyReset(): Promise<boolean> {
    try {
      // Complete reset of all theme-related state
      const rootElement = document.documentElement;
      rootElement.className = '';
      rootElement.removeAttribute('style');
      rootElement.removeAttribute('data-theme');

      // Clear all storage
      localStorage.clear();
      sessionStorage.clear();

      // Reload the page as last resort
      if (typeof window !== 'undefined' && window.location) {
        window.location.reload();
        return true;
      }

      return false;
    } catch {
      return false;
    }
  }
}

/**
 * Factory function to create enhanced recovery strategies
 */
export function createEnhancedRecoveryStrategies(): IRecoveryStrategy[] {
  return [
    new ContextAwareRecoveryStrategy(),
    new ProgressiveRecoveryStrategy()
  ];
}
