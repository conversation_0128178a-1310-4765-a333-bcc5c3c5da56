/**
 * Theme Performance Monitor
 * Handles performance monitoring for theme operations following Single Responsibility Principle.
 * Separated from ThemeLogger to maintain focused responsibilities.
 */

import { IPerformanceMonitor, IThemeLogger } from './interfaces/core-interfaces';
import { ThemeOperation, ThemeLogContext, ThemePerformanceMetrics } from './theme-types';
import { PerformanceThresholdManager, ThresholdContext } from './strategies/threshold-strategy';

/**
 * Performance Monitor class for theme operations
 * Follows Single Responsibility Principle - only handles performance monitoring
 */
export class ThemePerformanceMonitor implements IPerformanceMonitor {
  private performanceThresholds: Map<ThemeOperation, number>;
  private operationCounters: Map<string, number>;
  private averageDurations: Map<string, number>;
  private benchmarkViolations: Map<string, number>;
  private logger: IThemeLogger;
  private thresholdManager: PerformanceThresholdManager;

  constructor(
    logger: IThemeLogger,
    thresholdManager?: PerformanceThresholdManager
  ) {
    this.logger = logger;
    this.thresholdManager = thresholdManager || new PerformanceThresholdManager();

    // Initialize performance thresholds (in milliseconds)
    this.performanceThresholds = new Map([
      [ThemeOperation.THEME_SWITCH, 100],
      [ThemeOperation.THEME_LOAD, 50],
      [ThemeOperation.THEME_SAVE, 200],
      [ThemeOperation.THEME_VALIDATE, 10],
      [ThemeOperation.ELEMENT_CREATE, 1],
      [ThemeOperation.CSS_INJECT, 10],
      [ThemeOperation.PLUGIN_APPLY, 50],
      [ThemeOperation.CUSTOM_THEME_REGISTER, 100]
    ]);

    this.operationCounters = new Map();
    this.averageDurations = new Map();
    this.benchmarkViolations = new Map();
  }

  /**
   * Measure and log async operation performance
   */
  public async measureOperation<T>(
    operation: ThemeOperation,
    fn: () => Promise<T>,
    context?: Partial<ThemeLogContext>
  ): Promise<T> {
    const startTime = performance.now();
    this.logger.logOperationStart(operation, context);

    try {
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const dynamicThreshold = this.getDynamicThreshold(operation, context);
      const metrics: ThemePerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: context?.userState?.elementCount,
        meetsBenchmark: duration <= dynamicThreshold,
        benchmark: dynamicThreshold
      };

      this.updateStatistics(operation, duration);
      this.logger.logOperationComplete(operation, true, metrics, context);
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      const dynamicThreshold = this.getDynamicThreshold(operation, context);
      const metrics: ThemePerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: context?.userState?.elementCount,
        meetsBenchmark: duration <= dynamicThreshold,
        benchmark: dynamicThreshold
      };

      this.updateStatistics(operation, duration);
      this.logger.logOperationComplete(operation, false, metrics, context);
      throw error;
    }
  }

  /**
   * Measure and log sync operation performance
   */
  public measureOperationSync<T>(
    operation: ThemeOperation,
    fn: () => T,
    context?: Partial<ThemeLogContext>
  ): T {
    const startTime = performance.now();
    this.logger.logOperationStart(operation, context);

    try {
      const result = fn();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const dynamicThreshold = this.getDynamicThreshold(operation, context);
      const metrics: ThemePerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: context?.userState?.elementCount,
        meetsBenchmark: duration <= dynamicThreshold,
        benchmark: dynamicThreshold
      };

      this.updateStatistics(operation, duration);
      this.logger.logOperationComplete(operation, true, metrics, context);
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      const dynamicThreshold = this.getDynamicThreshold(operation, context);
      const metrics: ThemePerformanceMetrics = {
        startTime,
        endTime,
        duration,
        memoryBefore: this.getMemoryUsage(),
        memoryAfter: this.getMemoryUsage(),
        elementCount: context?.userState?.elementCount,
        meetsBenchmark: duration <= dynamicThreshold,
        benchmark: dynamicThreshold
      };

      this.updateStatistics(operation, duration);
      this.logger.logOperationComplete(operation, false, metrics, context);
      throw error;
    }
  }

  /**
   * Set performance threshold for an operation
   */
  public setPerformanceThreshold(operation: ThemeOperation, thresholdMs: number): void {
    this.performanceThresholds.set(operation, thresholdMs);
  }

  /**
   * Get dynamic threshold for an operation based on context
   */
  private getDynamicThreshold(operation: ThemeOperation, context?: Partial<ThemeLogContext>): number {
    // Create threshold context from log context
    const thresholdContext: ThresholdContext = {
      elementCount: context?.userState?.elementCount,
      systemLoad: this.estimateSystemLoad(),
      deviceCapabilities: this.thresholdManager.getSystemContext().deviceCapabilities,
      historicalData: {
        averageDuration: this.averageDurations.get(operation.toString()),
        successRate: this.calculateSuccessRate(operation)
      }
    };

    // Use threshold manager to determine optimal threshold
    const dynamicThreshold = this.thresholdManager.determineThreshold(operation, thresholdContext);

    // Fall back to static threshold if dynamic calculation fails
    return dynamicThreshold || this.performanceThresholds.get(operation) || 100;
  }

  private estimateSystemLoad(): 'low' | 'medium' | 'high' {
    // Simple heuristic based on recent performance
    const recentOperations = Array.from(this.averageDurations.values());
    if (recentOperations.length === 0) return 'medium';

    const avgDuration = recentOperations.reduce((sum, duration) => sum + duration, 0) / recentOperations.length;

    if (avgDuration < 50) return 'low';
    if (avgDuration < 200) return 'medium';
    return 'high';
  }

  private calculateSuccessRate(operation: ThemeOperation): number {
    const key = operation.toString();
    const totalOperations = this.operationCounters.get(key) || 0;
    const violations = this.benchmarkViolations.get(key) || 0;

    if (totalOperations === 0) return 1.0;
    return Math.max(0, (totalOperations - violations) / totalOperations);
  }

  /**
   * Get performance statistics
   */
  public getPerformanceStats(): {
    operationCounts: Record<string, number>;
    averageDurations: Record<string, number>;
    benchmarkViolations: Record<string, number>;
    memoryTrend: 'increasing' | 'decreasing' | 'stable' | 'unknown';
  } {
    return {
      operationCounts: Object.fromEntries(this.operationCounters),
      averageDurations: Object.fromEntries(this.averageDurations),
      benchmarkViolations: Object.fromEntries(this.benchmarkViolations),
      memoryTrend: 'unknown' // Simplified for now
    };
  }

  private updateStatistics(operation: ThemeOperation, duration: number): void {
    const key = operation.toString();

    // Update operation count
    const currentCount = this.operationCounters.get(key) || 0;
    this.operationCounters.set(key, currentCount + 1);

    // Update average duration
    const currentAvg = this.averageDurations.get(key) || 0;
    const newCount = currentCount + 1;
    const newAvg = (currentAvg * currentCount + duration) / newCount;
    this.averageDurations.set(key, newAvg);

    // Check for benchmark violations
    const threshold = this.performanceThresholds.get(operation);
    if (threshold && duration > threshold) {
      const violations = this.benchmarkViolations.get(key) || 0;
      this.benchmarkViolations.set(key, violations + 1);
    }
  }

  private getMemoryUsage(): number | undefined {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      // Type-safe access to performance.memory
      const performanceWithMemory = performance as Performance & {
        memory?: {
          usedJSHeapSize?: number;
          totalJSHeapSize?: number;
          jsHeapSizeLimit?: number;
        };
      };
      return performanceWithMemory.memory?.usedJSHeapSize;
    }
    return undefined;
  }
}
