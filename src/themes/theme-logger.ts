/**
 * Theme System Logging Infrastructure
 * Extends the FeatherJS logging system with theme-specific logging context and formatting.
 * Integrates seamlessly with the existing logger from src/utils/logger.ts.
 *
 * SOLID Compliance:
 * - Single Responsibility: Focused on theme-specific logging operations
 * - Interface Segregation: Implements IThemeLogger interface
 * - Dependency Inversion: Depends on Logger abstraction, not concrete implementation
 */

import { Logger, LogLevel } from '../utils/logger';
import { ThemeOperation, ThemePerformanceMetrics, ThemeLogContext } from './theme-types';
import { ThemeError } from './error-handling/theme-error-factory';
import { IThemeLogger } from './interfaces/core-interfaces';

/**
 * Theme-specific log levels for granular control
 */
export enum ThemeLogLevel {
  /** Detailed theme operation debugging */
  THEME_DEBUG = 'theme_debug',
  /** Theme user actions and state changes */
  THEME_INFO = 'theme_info',
  /** Theme performance warnings */
  THEME_WARN = 'theme_warn',
  /** Theme errors and failures */
  THEME_ERROR = 'theme_error'
}



/**
 * Theme Logger class for theme-specific logging operations
 * Follows Single Responsibility Principle - only handles logging
 * Implements IThemeLogger interface for dependency injection
 */
export class ThemeLogger implements IThemeLogger {
  private logger: Logger;

  constructor(baseLogger?: Logger) {
    this.logger = baseLogger || new Logger({
      source: 'ThemeSystem',
      level: LogLevel.DEBUG
    });
  }

  /**
   * Log theme operation start
   */
  public logOperationStart(operation: ThemeOperation, context?: Partial<ThemeLogContext>): void {
    const fullContext: ThemeLogContext = {
      operation,
      ...context
    };

    this.logger.debug(`Theme operation started: ${operation}`, {
      themeOperation: operation,
      context: fullContext,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log theme operation completion with performance metrics
   */
  public logOperationComplete(
    operation: ThemeOperation,
    success: boolean,
    metrics: ThemePerformanceMetrics,
    context?: Partial<ThemeLogContext>
  ): void {
    const fullContext: ThemeLogContext = {
      operation,
      performance: metrics,
      ...context
    };

    const logLevel = success ? LogLevel.INFO : LogLevel.ERROR;
    const status = success ? 'completed' : 'failed';
    const performanceStatus = metrics.meetsBenchmark ? 'within benchmark' : 'exceeded benchmark';

    const message = `Theme operation ${status}: ${operation} (${metrics.duration.toFixed(2)}ms, ${performanceStatus})`;
    const metadata = {
      themeOperation: operation,
      success,
      performance: metrics,
      context: fullContext,
      timestamp: new Date().toISOString()
    };

    if (logLevel === LogLevel.INFO) {
      this.logger.info(message, metadata);
    } else {
      this.logger.error(message, undefined, metadata);
    }

    // Log performance warning if benchmark exceeded
    if (!metrics.meetsBenchmark) {
      this.logger.warn(
        `Theme performance warning: ${operation} took ${metrics.duration.toFixed(2)}ms (exceeded benchmark)`,
        {
          performanceWarning: true,
          operation,
          duration: metrics.duration,
          benchmark: metrics.benchmark,
          context,
          timestamp: new Date().toISOString()
        }
      );
    }
  }

  /**
   * Log theme error with full context
   */
  public logError(error: ThemeError, context?: Partial<ThemeLogContext>): void {
    const fullContext: ThemeLogContext = {
      operation: context?.operation || error.context.operation,
      themeId: context?.themeId || error.context.themeId,
      userState: context?.userState || error.context.userState,
      pluginContext: context?.pluginContext,
      additionalData: {
        ...error.context.additionalData,
        ...context?.additionalData
      }
    };

    this.logger.error(
      `Theme error: ${error.getUserFriendlyMessage()}`,
      error,
      {
        themeError: true,
        category: error.category,
        recoverable: error.recoverable,
        recoveryActions: error.recoveryActions,
        context: fullContext,
        timestamp: new Date().toISOString()
      }
    );
  }

  /**
   * Log theme validation results
   */
  public logValidation(
    themeId: string,
    isValid: boolean,
    errors: string[] = [],
    warnings: string[] = []
  ): void {
    const level = isValid ? LogLevel.INFO : LogLevel.ERROR;
    const message = `Theme validation ${isValid ? 'passed' : 'failed'}: ${themeId}`;

    const metadata = {
      themeValidation: true,
      themeId,
      isValid,
      errors,
      warnings,
      timestamp: new Date().toISOString()
    };

    if (level === LogLevel.INFO) {
      this.logger.info(message, metadata);
    } else {
      this.logger.error(message, undefined, metadata);
    }
  }

  /**
   * Log user action related to themes
   */
  public logUserAction(
    action: string,
    themeId?: string,
    additionalData?: Record<string, unknown>
  ): void {
    this.logger.info(`User action: ${action}`, {
      userAction: true,
      action,
      themeId,
      ...additionalData,
      timestamp: new Date().toISOString()
    });
  }





  /**
   * Create a child theme logger with additional context
   */
  public createChild(source: string, additionalMetadata?: Record<string, unknown>): IThemeLogger {
    const childLogger = this.logger.createChild(source, additionalMetadata);
    return new ThemeLogger(childLogger);
  }

  /**
   * Get the underlying logger instance
   */
  public getLogger(): Logger {
    return this.logger;
  }
}
