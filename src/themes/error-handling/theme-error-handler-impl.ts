/**
 * Theme Error Handler Implementation
 * Follows Single Responsibility Principle (SRP) by focusing solely on error handling orchestration.
 * Uses Dependency Injection (DIP) to depend on abstractions rather than concrete implementations.
 */

import {
  IThemeErrorHandler,
  IThemeErrorFactory,
  IRecoveryStrategyManager,
  IErrorHistoryManager,
  IThemeError
} from '../interfaces/core-interfaces';
import { ThemeOperation, ThemeErrorContext } from '../theme-types';

/**
 * Theme error handler implementation
 * Orchestrates error handling using injected dependencies
 */
export class ThemeErrorHandlerImpl implements IThemeErrorHandler {
  constructor(
    private errorFactory: IThemeErrorFactory,
    private recoveryManager: IRecoveryStrategyManager,
    private historyManager: IErrorHistoryManager
  ) {}

  /**
   * Handle a theme error with automatic recovery attempts
   */
  public async handleError(error: IThemeError): Promise<boolean> {
    // Add to error history
    this.historyManager.addError(error);

    // If error is not recoverable, return false immediately
    if (!error.recoverable) {
      return false;
    }

    // Try recovery strategies
    const recovered = await this.recoveryManager.executeRecovery(error);

    // Mark as resolved if recovery succeeded
    if (recovered && 'markErrorResolved' in this.historyManager) {
      (this.historyManager as any).markErrorResolved(error, 'automatic_recovery');
    }

    return recovered;
  }

  /**
   * Wrap an async function with error handling
   */
  public async wrapAsync<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): Promise<T | null> {
    try {
      return await fn();
    } catch (error) {
      const themeError = this.errorFactory.normalizeToThemeError(error, operation, context);
      const recovered = await this.handleError(themeError);

      if (!recovered) {
        throw themeError;
      }

      return null;
    }
  }

  /**
   * Wrap a synchronous function with error handling
   */
  public wrap<T>(
    fn: () => T,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): T | null {
    try {
      return fn();
    } catch (error) {
      const themeError = this.errorFactory.normalizeToThemeError(error, operation, context);

      // Add to history but can't do async recovery for sync operations
      this.historyManager.addError(themeError);

      // For sync operations, we can't do async recovery, so just throw
      throw themeError;
    }
  }

  /**
   * Create and handle an error in one operation
   */
  public async createAndHandleError(
    message: string,
    category: import('../theme-error-handler').ThemeErrorCategory,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): Promise<boolean> {
    const error = this.errorFactory.createError(message, category, operation, context);
    return this.handleError(error);
  }

  /**
   * Get error handling statistics
   */
  public getStatistics(): {
    totalErrors: number;
    recoveryRate: number;
    errorsByCategory: Record<string, number>;
    errorsByOperation: Record<string, number>;
  } {
    const historyStats = this.historyManager.getStatistics();
    return {
      totalErrors: historyStats.total,
      recoveryRate: historyStats.recoveryRate,
      errorsByCategory: historyStats.byCategory as Record<string, number>,
      errorsByOperation: historyStats.byOperation as Record<string, number>
    };
  }

  /**
   * Get recent errors for debugging
   */
  public getRecentErrors(timeWindowMs = 300000): ReadonlyArray<IThemeError> {
    if ('getRecentErrors' in this.historyManager) {
      return (this.historyManager as any).getRecentErrors(timeWindowMs);
    }
    return this.historyManager.getHistory().slice(-10); // Fallback to last 10 errors
  }

  /**
   * Clear error history
   */
  public clearHistory(): void {
    this.historyManager.clearHistory();
  }

  /**
   * Test error handling with a mock error
   */
  public async testErrorHandling(
    message: string,
    category: import('../theme-error-handler').ThemeErrorCategory,
    operation: ThemeOperation
  ): Promise<{
    errorCreated: boolean;
    errorHandled: boolean;
    recovered: boolean;
    error?: IThemeError;
  }> {
    try {
      const error = this.errorFactory.createError(message, category, operation);
      const recovered = await this.handleError(error);

      return {
        errorCreated: true,
        errorHandled: true,
        recovered,
        error
      };
    } catch (handlingError) {
      return {
        errorCreated: true,
        errorHandled: false,
        recovered: false
      };
    }
  }

  /**
   * Validate error handling configuration
   */
  public validateConfiguration(): {
    valid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check if all dependencies are properly injected
    if (!this.errorFactory) {
      issues.push('Error factory is not injected');
    }
    if (!this.recoveryManager) {
      issues.push('Recovery manager is not injected');
    }
    if (!this.historyManager) {
      issues.push('History manager is not injected');
    }

    // Check if recovery manager has strategies
    if (this.recoveryManager && 'getRegisteredStrategyNames' in this.recoveryManager) {
      const strategies = (this.recoveryManager as any).getRegisteredStrategyNames();
      if (strategies.length === 0) {
        issues.push('No recovery strategies are registered');
      }
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * Get dependency information for debugging
   */
  public getDependencyInfo(): {
    errorFactory: string;
    recoveryManager: string;
    historyManager: string;
  } {
    return {
      errorFactory: this.errorFactory.constructor.name,
      recoveryManager: this.recoveryManager.constructor.name,
      historyManager: this.historyManager.constructor.name
    };
  }

  /**
   * Execute a function with comprehensive error handling and reporting
   */
  public async executeWithErrorHandling<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): Promise<{
    success: boolean;
    result?: T;
    error?: IThemeError;
    recovered: boolean;
    recoveryAttempted: boolean;
  }> {
    try {
      const result = await fn();
      return {
        success: true,
        result,
        recovered: false,
        recoveryAttempted: false
      };
    } catch (error) {
      const themeError = this.errorFactory.normalizeToThemeError(error, operation, context);

      let recovered = false;
      let recoveryAttempted = false;

      if (themeError.recoverable) {
        recoveryAttempted = true;
        recovered = await this.handleError(themeError);
      } else {
        this.historyManager.addError(themeError);
      }

      return {
        success: false,
        error: themeError,
        recovered,
        recoveryAttempted
      };
    }
  }

  /**
   * Batch error handling for multiple operations
   */
  public async handleMultipleErrors(errors: IThemeError[]): Promise<{
    totalErrors: number;
    recoveredErrors: number;
    failedErrors: number;
    results: Array<{ error: IThemeError; recovered: boolean }>;
  }> {
    const results: Array<{ error: IThemeError; recovered: boolean }> = [];
    let recoveredCount = 0;
    let failedCount = 0;

    for (const error of errors) {
      const recovered = await this.handleError(error);
      results.push({ error, recovered });

      if (recovered) {
        recoveredCount++;
      } else {
        failedCount++;
      }
    }

    return {
      totalErrors: errors.length,
      recoveredErrors: recoveredCount,
      failedErrors: failedCount,
      results
    };
  }
}
