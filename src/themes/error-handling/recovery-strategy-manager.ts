/**
 * Recovery Strategy Manager Implementation
 * Follows Single Responsibility Principle (SRP) by focusing solely on managing recovery strategies.
 * Implements Strategy Pattern for extensible error recovery mechanisms.
 */

import {
  IRecoveryStrategyManager,
  IRecoveryStrategy,
  IThemeError
} from '../interfaces/core-interfaces';
import { createBasicRecoveryStrategies } from '../strategies/basic-recovery-strategies';
import { createEnhancedRecoveryStrategies } from '../strategies/recovery-strategy';

/**
 * Recovery strategy manager implementation
 * Manages registration, prioritization, and execution of recovery strategies
 */
export class RecoveryStrategyManager implements IRecoveryStrategyManager {
  private strategies = new Map<string, IRecoveryStrategy>();
  private sortedStrategies: IRecoveryStrategy[] = [];

  /**
   * Register a recovery strategy
   */
  public registerStrategy(strategy: IRecoveryStrategy): void {
    if (this.strategies.has(strategy.name)) {
      throw new Error(`Recovery strategy '${strategy.name}' is already registered`);
    }

    this.strategies.set(strategy.name, strategy);
    this.updateSortedStrategies();
  }

  /**
   * Unregister a recovery strategy
   */
  public unregisterStrategy(strategyName: string): void {
    if (!this.strategies.has(strategyName)) {
      throw new Error(`Recovery strategy '${strategyName}' is not registered`);
    }

    this.strategies.delete(strategyName);
    this.updateSortedStrategies();
  }

  /**
   * Get all strategies that can handle an error
   */
  public getStrategiesForError(error: IThemeError): IRecoveryStrategy[] {
    return this.sortedStrategies.filter(strategy => strategy.canHandle(error));
  }

  /**
   * Execute recovery strategies for an error
   * Tries strategies in priority order until one succeeds
   */
  public async executeRecovery(error: IThemeError): Promise<boolean> {
    const applicableStrategies = this.getStrategiesForError(error);

    if (applicableStrategies.length === 0) {
      return false;
    }

    for (const strategy of applicableStrategies) {
      try {
        const recovered = await strategy.execute(error);
        if (recovered) {
          return true;
        }
      } catch (recoveryError) {
        // Recovery strategy itself failed, continue to next strategy
        console.warn(`Recovery strategy '${strategy.name}' failed:`, recoveryError);
      }
    }

    return false;
  }

  /**
   * Update the sorted strategies list based on priority
   */
  private updateSortedStrategies(): void {
    this.sortedStrategies = Array.from(this.strategies.values())
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Get all registered strategy names
   */
  public getRegisteredStrategyNames(): string[] {
    return Array.from(this.strategies.keys());
  }

  /**
   * Get strategy by name
   */
  public getStrategy(name: string): IRecoveryStrategy | undefined {
    return this.strategies.get(name);
  }

  /**
   * Check if a strategy is registered
   */
  public hasStrategy(name: string): boolean {
    return this.strategies.has(name);
  }

  /**
   * Clear all registered strategies
   */
  public clear(): void {
    this.strategies.clear();
    this.sortedStrategies = [];
  }

  /**
   * Get strategy statistics
   */
  public getStrategyStats(): {
    totalStrategies: number;
    strategiesByPriority: Array<{ name: string; priority: number }>;
  } {
    return {
      totalStrategies: this.strategies.size,
      strategiesByPriority: this.sortedStrategies.map(strategy => ({
        name: strategy.name,
        priority: strategy.priority
      }))
    };
  }

  /**
   * Replace an existing strategy
   */
  public replaceStrategy(strategy: IRecoveryStrategy): void {
    if (!this.strategies.has(strategy.name)) {
      throw new Error(`Recovery strategy '${strategy.name}' is not registered and cannot be replaced`);
    }

    this.strategies.set(strategy.name, strategy);
    this.updateSortedStrategies();
  }

  /**
   * Test all strategies with a mock error to validate they work
   */
  public async validateStrategies(mockError: IThemeError): Promise<{
    valid: boolean;
    results: Array<{ name: string; canHandle: boolean; error?: string }>;
  }> {
    const results: Array<{ name: string; canHandle: boolean; error?: string }> = [];
    let allValid = true;

    for (const strategy of this.sortedStrategies) {
      try {
        const canHandle = strategy.canHandle(mockError);
        results.push({ name: strategy.name, canHandle });
      } catch (error) {
        allValid = false;
        results.push({
          name: strategy.name,
          canHandle: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return { valid: allValid, results };
  }

  /**
   * Get strategies grouped by priority level
   */
  public getStrategiesByPriorityLevel(): Map<number, IRecoveryStrategy[]> {
    const groupedStrategies = new Map<number, IRecoveryStrategy[]>();

    for (const strategy of this.sortedStrategies) {
      const priority = strategy.priority;
      if (!groupedStrategies.has(priority)) {
        groupedStrategies.set(priority, []);
      }
      groupedStrategies.get(priority)!.push(strategy);
    }

    return groupedStrategies;
  }

  /**
   * Execute recovery with detailed reporting
   */
  public async executeRecoveryWithReport(error: IThemeError): Promise<{
    recovered: boolean;
    attemptedStrategies: Array<{
      name: string;
      priority: number;
      attempted: boolean;
      succeeded: boolean;
      error?: string;
    }>;
  }> {
    const applicableStrategies = this.getStrategiesForError(error);
    const attemptedStrategies: Array<{
      name: string;
      priority: number;
      attempted: boolean;
      succeeded: boolean;
      error?: string;
    }> = [];

    if (applicableStrategies.length === 0) {
      return { recovered: false, attemptedStrategies };
    }

    for (const strategy of applicableStrategies) {
      const attempt = {
        name: strategy.name,
        priority: strategy.priority,
        attempted: true,
        succeeded: false,
        error: undefined as string | undefined
      };

      try {
        const recovered = await strategy.execute(error);
        attempt.succeeded = recovered;
        attemptedStrategies.push(attempt);

        if (recovered) {
          return { recovered: true, attemptedStrategies };
        }
      } catch (recoveryError) {
        attempt.error = recoveryError instanceof Error ? recoveryError.message : 'Unknown error';
        attemptedStrategies.push(attempt);
      }
    }

    return { recovered: false, attemptedStrategies };
  }
}

/**
 * Factory function to create a fully configured RecoveryStrategyManager
 * with all available recovery strategies registered
 */
export function createConfiguredRecoveryStrategyManager(): RecoveryStrategyManager {
  const manager = new RecoveryStrategyManager();

  // Register basic recovery strategies
  const basicStrategies = createBasicRecoveryStrategies();
  basicStrategies.forEach(strategy => manager.registerStrategy(strategy));

  // Register enhanced recovery strategies
  const enhancedStrategies = createEnhancedRecoveryStrategies();
  enhancedStrategies.forEach(strategy => manager.registerStrategy(strategy));

  return manager;
}
