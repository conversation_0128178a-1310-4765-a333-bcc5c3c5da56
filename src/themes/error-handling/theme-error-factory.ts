/**
 * Theme Error Factory Implementation
 * Follows Single Responsibility Principle (SRP) by focusing solely on error creation and categorization.
 * Separated from error handling and recovery logic for better maintainability.
 */

import { FeatherError, <PERSON>rror<PERSON>andler } from '../../utils/error';
import {
  IThemeErrorFactory,
  IThemeError
} from '../interfaces/core-interfaces';
import {
  ThemeErrorCategory,
  ThemeOperation,
  ThemeErrorContext
} from '../theme-types';
import { ErrorCategorizationManager } from '../strategies/error-categorization-strategy';

/**
 * Theme error implementation
 */
export class ThemeError extends FeatherError implements IThemeError {
  public readonly category: ThemeErrorCategory;
  public readonly context: ThemeErrorContext;
  public readonly recoverable: boolean;
  public readonly recoveryActions: string[];

  constructor(
    message: string,
    category: ThemeErrorCategory,
    context: ThemeErrorContext,
    recoverable = true,
    recoveryActions: string[] = []
  ) {
    super(message, `THEME_${category.toUpperCase()}_ERROR`, context);

    // Set the prototype explicitly
    Object.setPrototypeOf(this, ThemeError.prototype);

    this.category = category;
    this.context = context;
    this.recoverable = recoverable;
    this.recoveryActions = recoveryActions.length > 0 ? recoveryActions : this.getDefaultRecoveryActions();
  }

  /**
   * Get default recovery actions based on error category
   */
  private getDefaultRecoveryActions(): string[] {
    switch (this.category) {
      case ThemeErrorCategory.VALIDATION:
        return [
          'Check theme configuration format',
          'Verify all required properties are present',
          'Validate color values and CSS variables'
        ];
      case ThemeErrorCategory.PERFORMANCE:
        return [
          'Reduce number of theme elements',
          'Check for memory leaks',
          'Optimize theme switching frequency'
        ];
      case ThemeErrorCategory.NETWORK:
        return [
          'Check network connectivity',
          'Retry theme loading operation',
          'Use cached theme if available'
        ];
      case ThemeErrorCategory.USER:
        return [
          'Verify user permissions',
          'Check theme accessibility',
          'Provide user-friendly error message'
        ];
      case ThemeErrorCategory.SYSTEM:
        return [
          'Check system resources',
          'Verify browser compatibility',
          'Restart theme system if necessary'
        ];
      default:
        return ['Contact support with error details'];
    }
  }

  /**
   * Get a user-friendly error message
   */
  public getUserFriendlyMessage(): string {
    const baseMessage = this.message;
    const contextInfo = this.context.themeId ? ` (Theme: ${this.context.themeId})` : '';
    return `${baseMessage}${contextInfo}`;
  }

  /**
   * Enhanced JSON serialization including theme-specific data
   */
  public override toJSON(): Record<string, unknown> {
    return {
      ...super.toJSON(),
      category: this.category,
      context: this.context,
      recoverable: this.recoverable,
      recoveryActions: this.recoveryActions
    };
  }
}

/**
 * Theme error factory implementation
 * Focuses solely on creating and categorizing theme errors
 */
export class ThemeErrorFactory implements IThemeErrorFactory {
  private categorizationManager: ErrorCategorizationManager;

  constructor(categorizationManager?: ErrorCategorizationManager) {
    this.categorizationManager = categorizationManager || new ErrorCategorizationManager();
  }
  /**
   * Create a theme error with proper context
   */
  public createError(
    message: string,
    category: ThemeErrorCategory,
    operation: ThemeOperation,
    additionalContext?: Partial<ThemeErrorContext>
  ): IThemeError {
    const context: ThemeErrorContext = {
      operation,
      ...additionalContext
    };

    return new ThemeError(message, category, context);
  }

  /**
   * Convert any error to a theme error
   */
  public normalizeToThemeError(
    error: unknown,
    operation: ThemeOperation,
    context?: Partial<ThemeErrorContext>
  ): IThemeError {
    if (error instanceof ThemeError) {
      return error;
    }

    const normalizedError = ErrorHandler.normalizeError(error);
    const category = this.categorizeError(normalizedError, operation);

    return new ThemeError(
      normalizedError.message,
      category,
      { operation, ...context }
    );
  }

  /**
   * Categorize an error based on its characteristics and operation
   * Uses the Strategy Pattern for extensible error categorization
   */
  public categorizeError(error: Error, operation: ThemeOperation): ThemeErrorCategory {
    return this.categorizationManager.categorizeError(error, operation);
  }

  /**
   * Create a validation error with specific validation context
   */
  public createValidationError(
    message: string,
    operation: ThemeOperation,
    validationDetails: {
      field?: string;
      expectedValue?: unknown;
      actualValue?: unknown;
      constraints?: string[];
    }
  ): IThemeError {
    return this.createError(
      message,
      ThemeErrorCategory.VALIDATION,
      operation,
      {
        additionalData: {
          validationType: 'field_validation',
          ...validationDetails
        }
      }
    );
  }

  /**
   * Create a performance error with performance metrics
   */
  public createPerformanceError(
    message: string,
    operation: ThemeOperation,
    performanceData: {
      duration?: number;
      threshold?: number;
      memoryUsage?: number;
      elementCount?: number;
    }
  ): IThemeError {
    return this.createError(
      message,
      ThemeErrorCategory.PERFORMANCE,
      operation,
      {
        performance: {
          executionTime: performanceData.duration,
          memoryUsage: performanceData.memoryUsage,
          elementCount: performanceData.elementCount
        },
        additionalData: {
          threshold: performanceData.threshold,
          performanceType: 'benchmark_violation'
        }
      }
    );
  }

  /**
   * Create a network error with network-specific context
   */
  public createNetworkError(
    message: string,
    operation: ThemeOperation,
    networkDetails: {
      url?: string;
      statusCode?: number;
      timeout?: number;
    }
  ): IThemeError {
    return this.createError(
      message,
      ThemeErrorCategory.NETWORK,
      operation,
      {
        additionalData: {
          networkType: 'request_failure',
          ...networkDetails
        }
      }
    );
  }

  /**
   * Create a user error with user context
   */
  public createUserError(
    message: string,
    operation: ThemeOperation,
    userContext: {
      userId?: string;
      action?: string;
      permissions?: string[];
    }
  ): IThemeError {
    return this.createError(
      message,
      ThemeErrorCategory.USER,
      operation,
      {
        additionalData: {
          userType: 'user_action_failure',
          ...userContext
        }
      }
    );
  }

  /**
   * Create a system error with system context
   */
  public createSystemError(
    message: string,
    operation: ThemeOperation,
    systemContext: {
      component?: string;
      browserInfo?: string;
      osInfo?: string;
    }
  ): IThemeError {
    return this.createError(
      message,
      ThemeErrorCategory.SYSTEM,
      operation,
      {
        additionalData: {
          systemType: 'system_failure',
          ...systemContext
        }
      }
    );
  }
}
