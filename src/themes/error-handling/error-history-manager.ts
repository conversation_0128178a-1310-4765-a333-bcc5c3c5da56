/**
 * Error History Manager Implementation
 * Follows Single Responsibility Principle (SRP) by focusing solely on error history and statistics.
 * Provides comprehensive error tracking and analysis capabilities.
 */

import { 
  IErrorHistoryManager, 
  IThemeError 
} from '../interfaces/core-interfaces';
import { ThemeErrorCategory, ThemeOperation } from '../theme-error-handler';

/**
 * Error history entry with additional metadata
 */
interface ErrorHistoryEntry {
  error: IThemeError;
  timestamp: Date;
  resolved: boolean;
  resolutionTime?: Date;
  resolutionMethod?: string;
}

/**
 * Error history manager implementation
 * Manages error history, statistics, and analysis
 */
export class ErrorHistoryManager implements IErrorHistoryManager {
  private history: ErrorHistoryEntry[] = [];
  private maxHistorySize = 100;

  /**
   * Add error to history
   */
  public addError(error: IThemeError): void {
    const entry: ErrorHistoryEntry = {
      error,
      timestamp: new Date(),
      resolved: false
    };

    this.history.push(entry);
    this.trimHistory();
  }

  /**
   * Mark an error as resolved
   */
  public markErrorResolved(error: IThemeError, resolutionMethod?: string): void {
    const entry = this.history.find(h => h.error === error);
    if (entry) {
      entry.resolved = true;
      entry.resolutionTime = new Date();
      entry.resolutionMethod = resolutionMethod;
    }
  }

  /**
   * Get error history
   */
  public getHistory(): ReadonlyArray<IThemeError> {
    return this.history.map(entry => entry.error);
  }

  /**
   * Get detailed error history with metadata
   */
  public getDetailedHistory(): ReadonlyArray<ErrorHistoryEntry> {
    return [...this.history];
  }

  /**
   * Clear error history
   */
  public clearHistory(): void {
    this.history = [];
  }

  /**
   * Get error statistics
   */
  public getStatistics(): {
    total: number;
    byCategory: Record<ThemeErrorCategory, number>;
    byOperation: Record<ThemeOperation, number>;
    recoveryRate: number;
  } {
    const stats = {
      total: this.history.length,
      byCategory: {} as Record<ThemeErrorCategory, number>,
      byOperation: {} as Record<ThemeOperation, number>,
      recoveryRate: 0
    };

    // Initialize counters
    Object.values(ThemeErrorCategory).forEach(category => {
      stats.byCategory[category] = 0;
    });
    Object.values(ThemeOperation).forEach(operation => {
      stats.byOperation[operation] = 0;
    });

    // Count errors and resolutions
    let resolvedCount = 0;
    this.history.forEach(entry => {
      const error = entry.error;
      stats.byCategory[error.category]++;
      stats.byOperation[error.context.operation]++;
      
      if (entry.resolved) {
        resolvedCount++;
      }
    });

    // Calculate recovery rate
    stats.recoveryRate = stats.total > 0 ? (resolvedCount / stats.total) * 100 : 0;

    return stats;
  }

  /**
   * Set maximum history size
   */
  public setMaxHistorySize(size: number): void {
    if (size < 1) {
      throw new Error('Maximum history size must be at least 1');
    }
    
    this.maxHistorySize = size;
    this.trimHistory();
  }

  /**
   * Get maximum history size
   */
  public getMaxHistorySize(): number {
    return this.maxHistorySize;
  }

  /**
   * Trim history to maximum size
   */
  private trimHistory(): void {
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
    }
  }

  /**
   * Get errors by category
   */
  public getErrorsByCategory(category: ThemeErrorCategory): ReadonlyArray<IThemeError> {
    return this.history
      .filter(entry => entry.error.category === category)
      .map(entry => entry.error);
  }

  /**
   * Get errors by operation
   */
  public getErrorsByOperation(operation: ThemeOperation): ReadonlyArray<IThemeError> {
    return this.history
      .filter(entry => entry.error.context.operation === operation)
      .map(entry => entry.error);
  }

  /**
   * Get recent errors within a time window
   */
  public getRecentErrors(timeWindowMs: number): ReadonlyArray<IThemeError> {
    const cutoffTime = new Date(Date.now() - timeWindowMs);
    return this.history
      .filter(entry => entry.timestamp >= cutoffTime)
      .map(entry => entry.error);
  }

  /**
   * Get unresolved errors
   */
  public getUnresolvedErrors(): ReadonlyArray<IThemeError> {
    return this.history
      .filter(entry => !entry.resolved)
      .map(entry => entry.error);
  }

  /**
   * Get resolved errors
   */
  public getResolvedErrors(): ReadonlyArray<IThemeError> {
    return this.history
      .filter(entry => entry.resolved)
      .map(entry => entry.error);
  }

  /**
   * Get error frequency analysis
   */
  public getErrorFrequencyAnalysis(timeWindowMs: number): {
    totalErrors: number;
    errorsPerHour: number;
    mostCommonCategory: ThemeErrorCategory | null;
    mostCommonOperation: ThemeOperation | null;
    errorTrend: 'increasing' | 'decreasing' | 'stable';
  } {
    const recentErrors = this.getRecentErrors(timeWindowMs);
    const hoursInWindow = timeWindowMs / (1000 * 60 * 60);

    // Count by category and operation
    const categoryCount = new Map<ThemeErrorCategory, number>();
    const operationCount = new Map<ThemeOperation, number>();

    recentErrors.forEach(error => {
      categoryCount.set(error.category, (categoryCount.get(error.category) || 0) + 1);
      operationCount.set(error.context.operation, (operationCount.get(error.context.operation) || 0) + 1);
    });

    // Find most common
    let mostCommonCategory: ThemeErrorCategory | null = null;
    let maxCategoryCount = 0;
    categoryCount.forEach((count, category) => {
      if (count > maxCategoryCount) {
        maxCategoryCount = count;
        mostCommonCategory = category;
      }
    });

    let mostCommonOperation: ThemeOperation | null = null;
    let maxOperationCount = 0;
    operationCount.forEach((count, operation) => {
      if (count > maxOperationCount) {
        maxOperationCount = count;
        mostCommonOperation = operation;
      }
    });

    // Analyze trend
    const halfWindow = timeWindowMs / 2;
    const firstHalfErrors = this.getRecentErrors(halfWindow);
    const secondHalfErrors = recentErrors.filter(error => {
      const errorEntry = this.history.find(entry => entry.error === error);
      return errorEntry && errorEntry.timestamp >= new Date(Date.now() - halfWindow);
    });

    let errorTrend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    if (secondHalfErrors.length > firstHalfErrors.length * 1.2) {
      errorTrend = 'increasing';
    } else if (secondHalfErrors.length < firstHalfErrors.length * 0.8) {
      errorTrend = 'decreasing';
    }

    return {
      totalErrors: recentErrors.length,
      errorsPerHour: recentErrors.length / hoursInWindow,
      mostCommonCategory,
      mostCommonOperation,
      errorTrend
    };
  }

  /**
   * Get resolution time statistics
   */
  public getResolutionTimeStats(): {
    averageResolutionTimeMs: number;
    medianResolutionTimeMs: number;
    fastestResolutionMs: number;
    slowestResolutionMs: number;
    totalResolved: number;
  } {
    const resolvedEntries = this.history.filter(entry => entry.resolved && entry.resolutionTime);
    
    if (resolvedEntries.length === 0) {
      return {
        averageResolutionTimeMs: 0,
        medianResolutionTimeMs: 0,
        fastestResolutionMs: 0,
        slowestResolutionMs: 0,
        totalResolved: 0
      };
    }

    const resolutionTimes = resolvedEntries.map(entry => {
      const resolutionTime = entry.resolutionTime!.getTime() - entry.timestamp.getTime();
      return resolutionTime;
    });

    resolutionTimes.sort((a, b) => a - b);

    const average = resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length;
    const median = resolutionTimes[Math.floor(resolutionTimes.length / 2)];
    const fastest = resolutionTimes[0];
    const slowest = resolutionTimes[resolutionTimes.length - 1];

    return {
      averageResolutionTimeMs: average,
      medianResolutionTimeMs: median,
      fastestResolutionMs: fastest,
      slowestResolutionMs: slowest,
      totalResolved: resolvedEntries.length
    };
  }

  /**
   * Export history data for analysis
   */
  public exportHistory(): {
    exportDate: string;
    totalEntries: number;
    maxHistorySize: number;
    entries: Array<{
      timestamp: string;
      category: ThemeErrorCategory;
      operation: ThemeOperation;
      message: string;
      recoverable: boolean;
      resolved: boolean;
      resolutionTime?: string;
      resolutionMethod?: string;
    }>;
  } {
    return {
      exportDate: new Date().toISOString(),
      totalEntries: this.history.length,
      maxHistorySize: this.maxHistorySize,
      entries: this.history.map(entry => ({
        timestamp: entry.timestamp.toISOString(),
        category: entry.error.category,
        operation: entry.error.context.operation,
        message: entry.error.message,
        recoverable: entry.error.recoverable,
        resolved: entry.resolved,
        resolutionTime: entry.resolutionTime?.toISOString(),
        resolutionMethod: entry.resolutionMethod
      }))
    };
  }
}
