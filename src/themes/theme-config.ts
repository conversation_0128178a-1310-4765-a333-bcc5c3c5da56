/**
 * Theme Configuration System
 *
 * Provides comprehensive theme configuration management with validation and persistence.
 * Implements SOLID principles for maintainable and extensible theme system architecture.
 *
 * SOLID Compliance:
 * - Single Responsibility: Each class has a focused responsibility
 * - Open/Closed: Extensible through interfaces and composition
 * - Liskov Substitution: All implementations are substitutable
 * - Interface Segregation: Focused interfaces for specific needs
 * - Dependency Inversion: Depends on abstractions, not concretions
 */

import {
  ThemeDefinition,
  CSSVariableMapping,
  ThemeOperation
} from './theme-types';
import {
  IThemeConfigManager,
  ICSSVariableInjector,
  IThemeStorage,
  IThemeValidator,
  IThemeLogger,
  IThemeErrorHandler
} from './interfaces/core-interfaces';
/**
 * Built-in light theme definition with WCAG AA compliant colors
 */
export const LIGHT_THEME: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  description: 'Clean and bright theme optimized for daylight use',
  version: '1.0.0',
  author: 'FeatherJS Team',
  isBuiltIn: true,
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  },
  stateColors: {
    hover: {
      background: '#f5f5f5',
      border: '#d0d0d0',
      text: '#1a1a1a'
    },
    focus: {
      background: '#ffffff',
      border: '#1565c0',
      ring: 'rgba(21, 101, 192, 0.25)'
    },
    active: {
      background: '#e9ecef',
      border: '#c0c0c0',
      text: '#000000'
    },
    disabled: {
      background: '#f8f9fa',
      border: '#e9ecef',
      text: '#6c757d'
    }
  },
  pluginColors: {
    palette: {
      background: '#ffffff',
      border: '#e0e0e0',
      shadow: 'rgba(0, 0, 0, 0.1)',
      tabActiveBorder: '#1565c0',
      swatchBorder: 'rgba(0, 0, 0, 0.1)'
    },
    chart: {
      background: '#ffffff',
      gridLines: '#e0e0e0',
      dataColors: ['#1565c0', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1']
    },
    code: {
      background: '#f8f9fa',
      border: '#e0e0e0',
      text: '#333333',
      keyword: '#d73a49',
      string: '#032f62',
      comment: '#6a737d'
    },
    table: {
      background: '#ffffff',
      border: '#e0e0e0',
      headerBackground: '#f8f9fa',
      alternateRowBackground: '#f8f9fa'
    },
    comments: {
      background: '#ffffff',
      border: '#e0e0e0',
      highlightBackground: 'rgba(74, 134, 232, 0.1)',
      avatarBackground: '#f8f9fa'
    }
  },
  animation: {
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    respectReducedMotion: true
  },
  cssVariablePrefix: 'theme'
};

/**
 * Built-in dark theme definition with WCAG AA compliant colors
 */
export const DARK_THEME: ThemeDefinition = {
  id: 'dark',
  name: 'Dark Theme',
  description: 'Elegant dark theme optimized for low-light environments',
  version: '1.0.0',
  author: 'FeatherJS Team',
  isBuiltIn: true,
  colors: {
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d',
    border: '#444444',
    primary: '#4fc3f7',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  },
  stateColors: {
    hover: {
      background: '#3a3a3a',
      border: '#555555',
      text: '#ffffff'
    },
    focus: {
      background: '#2d2d2d',
      border: '#4fc3f7',
      ring: 'rgba(79, 195, 247, 0.25)'
    },
    active: {
      background: '#404040',
      border: '#666666',
      text: '#ffffff'
    },
    disabled: {
      background: '#2d2d2d',
      border: '#3a3a3a',
      text: '#6c757d'
    }
  },
  pluginColors: {
    palette: {
      background: '#333333',
      border: '#444444',
      shadow: 'rgba(0, 0, 0, 0.3)',
      tabActiveBorder: '#4fc3f7',
      swatchBorder: 'rgba(255, 255, 255, 0.1)'
    },
    chart: {
      background: '#2d2d2d',
      gridLines: '#444444',
      dataColors: ['#4fc3f7', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1']
    },
    code: {
      background: '#2d2d2d',
      border: '#444444',
      text: '#f0f0f0',
      keyword: '#ff7b72',
      string: '#a5d6ff',
      comment: '#8b949e'
    },
    table: {
      background: '#2d2d2d',
      border: '#444444',
      headerBackground: '#3a3a3a',
      alternateRowBackground: '#333333'
    },
    comments: {
      background: '#2d2d2d',
      border: '#444444',
      highlightBackground: 'rgba(74, 134, 232, 0.2)',
      avatarBackground: '#3a3a3a'
    }
  },
  animation: {
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    respectReducedMotion: true
  },
  cssVariablePrefix: 'theme'
};

/**
 * Theme Configuration Manager
 * Manages theme registration, validation, and storage with comprehensive error handling
 */
export class ThemeConfigManager implements IThemeConfigManager {
  private readonly themes = new Map<string, ThemeDefinition>();
  private readonly builtInThemes: ThemeDefinition[] = [LIGHT_THEME, DARK_THEME];

  constructor(
    private readonly storage: IThemeStorage,
    private readonly validator: IThemeValidator,
    private readonly logger?: IThemeLogger,
    private readonly errorHandler?: IThemeErrorHandler
  ) {
    // Register built-in themes
    this.builtInThemes.forEach(theme => {
      this.themes.set(theme.id, theme);
    });

    this.logger?.logOperationStart(ThemeOperation.THEME_CONFIG_LOAD, {
      operation: ThemeOperation.THEME_CONFIG_LOAD,
      additionalData: { builtInThemeCount: this.builtInThemes.length }
    });
  }

  /**
   * Wrap async operations with error handling
   */
  private async wrapAsync<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    context?: { themeId?: string; additionalData?: Record<string, unknown> }
  ): Promise<T | null> {
    try {
      return await fn();
    } catch (error) {
      this.logger?.logUserAction('operation_failed', context?.themeId, {
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context?.additionalData
      });

      if (this.errorHandler) {
        const handled = await this.errorHandler.wrapAsync(fn, operation, context);
        return handled;
      }

      throw error;
    }
  }

  /**
   * Wrap async operations that return void
   */
  private async wrapAsyncVoid(
    fn: () => Promise<void>,
    operation: ThemeOperation,
    context?: { themeId?: string; additionalData?: Record<string, unknown> }
  ): Promise<void> {
    try {
      await fn();
    } catch (error) {
      this.logger?.logUserAction('operation_failed', context?.themeId, {
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context?.additionalData
      });

      if (this.errorHandler) {
        try {
          await this.errorHandler.wrapAsync(fn, operation, context);
          return;
        } catch {
          // If error handler fails, continue to throw original error
        }
      }

      throw error;
    }
  }

  /**
   * Load a theme configuration by ID
   */
  public async loadTheme(themeId: string): Promise<ThemeDefinition | null> {
    return this.wrapAsync(async () => {
      // Check in-memory themes first
      const memoryTheme = this.themes.get(themeId);
      if (memoryTheme) {
        return memoryTheme;
      }

      // Try to load from storage
      const storedTheme = await this.storage.retrieve(themeId);
      if (storedTheme) {
        // Validate stored theme before returning
        const validation = await this.validator.validateTheme(storedTheme);
        if (validation.isValid) {
          this.themes.set(themeId, storedTheme);
          return storedTheme;
        } else {
          this.logger?.logUserAction('theme_load_validation_failed', themeId, {
            errors: validation.errors,
            warnings: validation.warnings
          });
        }
      }

      return null;
    }, ThemeOperation.THEME_LOAD, { themeId });
  }

  /**
   * Save a theme configuration
   */
  public async saveTheme(theme: ThemeDefinition): Promise<void> {
    return this.wrapAsyncVoid(async () => {
      // Validate theme before saving
      const validation = await this.validator.validateTheme(theme);
      if (!validation.isValid) {
        throw new Error(`Theme validation failed: ${validation.errors.join(', ')}`);
      }

      // Update timestamps
      const now = new Date();
      const themeToSave: ThemeDefinition = {
        ...theme,
        modifiedAt: now,
        createdAt: theme.createdAt || now
      };

      // Save to storage if not built-in
      if (!themeToSave.isBuiltIn) {
        await this.storage.store(theme.id, themeToSave);
      }

      // Update in-memory cache
      this.themes.set(theme.id, themeToSave);

      this.logger?.logUserAction('theme_saved', theme.id, {
        isBuiltIn: themeToSave.isBuiltIn,
        version: themeToSave.version
      });
    }, ThemeOperation.THEME_SAVE, { themeId: theme.id });
  }

  /**
   * Get all available themes
   */
  public async getAvailableThemes(): Promise<ThemeDefinition[]> {
    const result = await this.wrapAsync(async () => {
      // Load any stored themes that aren't in memory
      const storedKeys = await this.storage.listKeys();
      for (const key of storedKeys) {
        if (!this.themes.has(key)) {
          const theme = await this.loadTheme(key);
          if (theme) {
            this.themes.set(key, theme);
          }
        }
      }

      return Array.from(this.themes.values());
    }, ThemeOperation.THEME_LOAD, { additionalData: { operation: 'getAvailableThemes' } });

    return result || [];
  }

  /**
   * Register a new theme
   */
  public async registerTheme(theme: ThemeDefinition): Promise<void> {
    return this.saveTheme(theme);
  }

  /**
   * Unregister a theme
   */
  public async unregisterTheme(themeId: string): Promise<void> {
    return this.wrapAsyncVoid(async () => {
      const theme = this.themes.get(themeId);
      if (!theme) {
        throw new Error(`Theme '${themeId}' not found`);
      }

      if (theme.isBuiltIn) {
        throw new Error(`Cannot unregister built-in theme '${themeId}'`);
      }

      // Remove from storage
      await this.storage.remove(themeId);

      // Remove from memory
      this.themes.delete(themeId);

      this.logger?.logUserAction('theme_unregistered', themeId);
    }, ThemeOperation.CUSTOM_THEME_REGISTER, { themeId });
  }

  /**
   * Get built-in theme definitions
   */
  public getBuiltInThemes(): ThemeDefinition[] {
    return [...this.builtInThemes];
  }
}

/**
 * CSS Variable Injector
 * Handles CSS variable injection and management with performance optimization
 */
export class CSSVariableInjector implements ICSSVariableInjector {
  private readonly injectedVariables = new Map<string, Set<string>>();
  private readonly documentRef: Document;

  constructor(
    documentRef?: Document,
    private readonly logger?: IThemeLogger,
    private readonly errorHandler?: IThemeErrorHandler
  ) {
    // Use provided document or fallback to global document if available
    this.documentRef = documentRef || (typeof document !== 'undefined' ? document : {} as Document);
  }

  /**
   * Check if document is available for DOM operations
   */
  private isDocumentAvailable(): boolean {
    return this.documentRef && 'documentElement' in this.documentRef;
  }



  /**
   * Wrap async operations that return void
   */
  private async wrapAsyncVoid(
    fn: () => Promise<void>,
    operation: ThemeOperation,
    context?: { themeId?: string; additionalData?: Record<string, unknown> }
  ): Promise<void> {
    try {
      await fn();
    } catch (error) {
      this.logger?.logUserAction('operation_failed', context?.themeId, {
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context?.additionalData
      });

      if (this.errorHandler) {
        try {
          await this.errorHandler.wrapAsync(fn, operation, context);
          return;
        } catch {
          // If error handler fails, continue to throw original error
        }
      }

      throw error;
    }
  }

  /**
   * Inject CSS variables for a theme
   */
  public async injectVariables(theme: ThemeDefinition, target?: Element): Promise<void> {
    return this.wrapAsyncVoid(async () => {
      // Skip injection if document is not available (e.g., in test environment)
      if (!target && !this.isDocumentAvailable()) {
        this.logger?.logUserAction('css_variables_skipped', theme.id, {
          reason: 'Document not available'
        });
        return;
      }

      const variables = this.generateVariableMappings(theme);
      const targetElement = target || this.documentRef.documentElement;
      const prefix = theme.cssVariablePrefix || 'theme';

      // Track injected variables for cleanup
      const variableNames = new Set<string>();

      // Inject variables in batches to prevent layout thrashing
      const style = (targetElement as HTMLElement).style;

      for (const variable of variables) {
        const variableName = `--${prefix}-${variable.name}`;
        style.setProperty(variableName, variable.value);
        variableNames.add(variableName);

        // Add fallback if specified
        if (variable.fallback) {
          const fallbackName = `${variableName}-fallback`;
          style.setProperty(fallbackName, variable.fallback);
          variableNames.add(fallbackName);
        }
      }

      // Store injected variables for cleanup
      this.injectedVariables.set(theme.id, variableNames);

      this.logger?.logUserAction('css_variables_injected', theme.id, {
        variableCount: variables.length,
        target: target ? 'custom' : 'document',
        prefix
      });
    }, ThemeOperation.CSS_VARIABLE_INJECT, { themeId: theme.id });
  }

  /**
   * Update specific CSS variables
   */
  public async updateVariables(variables: CSSVariableMapping[], target?: Element): Promise<void> {
    return this.wrapAsyncVoid(async () => {
      // Skip update if document is not available (e.g., in test environment)
      if (!target && !this.isDocumentAvailable()) {
        this.logger?.logUserAction('css_variables_update_skipped', undefined, {
          reason: 'Document not available'
        });
        return;
      }

      const targetElement = target || this.documentRef.documentElement;
      const style = (targetElement as HTMLElement).style;

      for (const variable of variables) {
        const variableName = `--${variable.name}`;
        style.setProperty(variableName, variable.value);

        if (variable.fallback) {
          const fallbackName = `${variableName}-fallback`;
          style.setProperty(fallbackName, variable.fallback);
        }
      }

      this.logger?.logUserAction('css_variables_updated', undefined, {
        variableCount: variables.length,
        target: target ? 'custom' : 'document'
      });
    }, ThemeOperation.CSS_INJECT, { additionalData: { operation: 'updateVariables' } });
  }

  /**
   * Remove CSS variables for a theme
   */
  public async removeVariables(themeId: string, target?: Element): Promise<void> {
    return this.wrapAsyncVoid(async () => {
      const variableNames = this.injectedVariables.get(themeId);
      if (!variableNames) {
        return; // No variables to remove
      }

      const targetElement = target || this.documentRef.documentElement;
      const style = (targetElement as HTMLElement).style;

      for (const variableName of variableNames) {
        style.removeProperty(variableName);
      }

      this.injectedVariables.delete(themeId);

      this.logger?.logUserAction('css_variables_removed', themeId, {
        variableCount: variableNames.size,
        target: target ? 'custom' : 'document'
      });
    }, ThemeOperation.CSS_INJECT, { themeId, additionalData: { operation: 'removeVariables' } });
  }

  /**
   * Generate CSS variable mappings from theme definition
   */
  public generateVariableMappings(theme: ThemeDefinition): CSSVariableMapping[] {
    const mappings: CSSVariableMapping[] = [];

    // Core colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      mappings.push({
        name: `color-${key}`,
        value,
        category: 'core',
        description: `Core ${key} color`
      });
    });

    // State colors
    Object.entries(theme.stateColors).forEach(([state, colors]) => {
      Object.entries(colors).forEach(([property, value]) => {
        mappings.push({
          name: `${state}-${property}`,
          value: value as string,
          category: 'state',
          description: `${state} state ${property} color`
        });
      });
    });

    // Plugin colors
    Object.entries(theme.pluginColors).forEach(([plugin, colors]) => {
      Object.entries(colors).forEach(([property, value]) => {
        if (Array.isArray(value)) {
          // Handle array values (like chart data colors)
          value.forEach((color, index) => {
            mappings.push({
              name: `${plugin}-${property}-${index}`,
              value: color,
              category: 'plugin',
              description: `${plugin} plugin ${property} color ${index}`
            });
          });
        } else {
          mappings.push({
            name: `${plugin}-${property}`,
            value: value as string,
            category: 'plugin',
            description: `${plugin} plugin ${property} color`
          });
        }
      });
    });

    // Animation properties
    mappings.push(
      {
        name: 'animation-duration',
        value: `${theme.animation.duration}ms`,
        category: 'core',
        description: 'Default animation duration'
      },
      {
        name: 'animation-easing',
        value: theme.animation.easing,
        category: 'core',
        description: 'Default animation easing function'
      }
    );

    return mappings;
  }

  /**
   * Get current CSS variable values
   */
  public getCurrentVariables(target?: Element): Record<string, string> {
    // Return empty object if document is not available (e.g., in test environment)
    if (!target && !this.isDocumentAvailable()) {
      return {};
    }

    const targetElement = target || this.documentRef.documentElement;
    const computedStyle = getComputedStyle(targetElement);
    const variables: Record<string, string> = {};

    // Get all CSS custom properties
    for (let i = 0; i < computedStyle.length; i++) {
      const property = computedStyle[i];
      if (property.startsWith('--theme-')) {
        variables[property] = computedStyle.getPropertyValue(property).trim();
      }
    }

    return variables;
  }
}

/**
 * LocalStorage-based Theme Storage
 * Provides persistent storage for custom themes with error handling and quota management
 */
export class LocalStorageThemeStorage implements IThemeStorage {
  private readonly storagePrefix = 'featherjs-theme-';
  private readonly maxStorageSize = 5 * 1024 * 1024; // 5MB limit

  constructor(
    private readonly storage: Storage = localStorage,
    private readonly logger?: IThemeLogger,
    private readonly errorHandler?: IThemeErrorHandler
  ) {}

  /**
   * Wrap async operations with error handling
   */
  private async wrapAsync<T>(
    fn: () => Promise<T>,
    operation: ThemeOperation,
    context?: { themeId?: string; additionalData?: Record<string, unknown> }
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      this.logger?.logUserAction('operation_failed', context?.themeId, {
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        ...context?.additionalData
      });

      if (this.errorHandler) {
        try {
          const handled = await this.errorHandler.wrapAsync(fn, operation, context);
          if (handled !== null) {
            return handled;
          }
        } catch {
          // If error handler fails, continue to throw original error
        }
      }

      throw error;
    }
  }



  /**
   * Store theme data
   */
  public async store(key: string, theme: ThemeDefinition): Promise<void> {
    return this.wrapAsync(async () => {
      if (!this.isAvailable()) {
        throw new Error('Storage is not available');
      }

      const storageKey = this.getStorageKey(key);
      const themeData = JSON.stringify(theme);

      // Check storage quota
      const storageInfo = await this.getStorageInfo();
      const dataSize = new Blob([themeData]).size;

      if (storageInfo.used + dataSize > this.maxStorageSize) {
        throw new Error('Storage quota exceeded');
      }

      this.storage.setItem(storageKey, themeData);

      this.logger?.logUserAction('theme_stored', key, {
        size: dataSize,
        storageUsed: storageInfo.used + dataSize
      });
    }, ThemeOperation.THEME_SAVE, { themeId: key });
  }

  /**
   * Retrieve theme data
   */
  public async retrieve(key: string): Promise<ThemeDefinition | null> {
    return this.wrapAsync(async () => {
      if (!this.isAvailable()) {
        return null;
      }

      const storageKey = this.getStorageKey(key);
      const themeData = this.storage.getItem(storageKey);

      if (!themeData) {
        return null;
      }

      try {
        const theme = JSON.parse(themeData) as ThemeDefinition;

        // Convert date strings back to Date objects
        if (theme.createdAt && typeof theme.createdAt === 'string') {
          theme.createdAt = new Date(theme.createdAt);
        }
        if (theme.modifiedAt && typeof theme.modifiedAt === 'string') {
          theme.modifiedAt = new Date(theme.modifiedAt);
        }

        this.logger?.logUserAction('theme_retrieved', key);
        return theme;
      } catch (error) {
        this.logger?.logUserAction('theme_retrieve_parse_error', key, {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        return null;
      }
    }, ThemeOperation.THEME_LOAD, { themeId: key });
  }

  /**
   * Remove theme data
   */
  public async remove(key: string): Promise<void> {
    return this.wrapAsync(async () => {
      if (!this.isAvailable()) {
        return;
      }

      const storageKey = this.getStorageKey(key);
      this.storage.removeItem(storageKey);

      this.logger?.logUserAction('theme_removed', key);
    }, ThemeOperation.THEME_SAVE, { themeId: key, additionalData: { operation: 'remove' } });
  }

  /**
   * List all stored theme keys
   */
  public async listKeys(): Promise<string[]> {
    return this.wrapAsync(async () => {
      if (!this.isAvailable()) {
        return [];
      }

      const keys: string[] = [];
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key && key.startsWith(this.storagePrefix)) {
          keys.push(key.substring(this.storagePrefix.length));
        }
      }

      return keys;
    }, ThemeOperation.THEME_LOAD, { additionalData: { operation: 'listKeys' } }) || [];
  }

  /**
   * Check if storage is available
   */
  public isAvailable(): boolean {
    try {
      const testKey = '__featherjs_storage_test__';
      this.storage.setItem(testKey, 'test');
      this.storage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get storage usage information
   */
  public async getStorageInfo(): Promise<{ used: number; available: number; total: number }> {
    return this.wrapAsync(async () => {
      if (!this.isAvailable()) {
        return { used: 0, available: 0, total: 0 };
      }

      let used = 0;
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key) {
          const value = this.storage.getItem(key);
          if (value) {
            used += new Blob([key + value]).size;
          }
        }
      }

      const total = this.maxStorageSize;
      const available = total - used;

      return { used, available, total };
    }, ThemeOperation.THEME_LOAD, { additionalData: { operation: 'getStorageInfo' } }) ||
    { used: 0, available: 0, total: 0 };
  }

  /**
   * Get the full storage key for a theme
   */
  private getStorageKey(key: string): string {
    return `${this.storagePrefix}${key}`;
  }
}
