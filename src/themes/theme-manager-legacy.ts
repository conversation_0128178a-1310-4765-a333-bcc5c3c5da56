/**
 * Legacy Theme Manager
 * 
 * Provides backward compatibility for the old ThemeManager API
 * while the new enhanced theme system is being implemented.
 * 
 * This is a temporary bridge that will be removed in Phase 1.4
 * when proper dependency injection is implemented.
 */

export type Theme = 'light' | 'dark';

export class ThemeManager {
  private readonly themeKey = 'editor-theme';
  private readonly availableThemes: Theme[] = ['light', 'dark'];
  private static instance: ThemeManager;

  constructor(
    private readonly doc: Document = document,
    private readonly storage: Storage = localStorage,
    private readonly win: Window = window
  ) {
    // Store instance for static access (for backward compatibility)
    ThemeManager.instance = this;
  }

  /**
   * Get the current theme
   */
  public static getCurrentTheme(): Theme {
    if (ThemeManager.instance) {
      const storedTheme = ThemeManager.instance.storage.getItem(ThemeManager.instance.themeKey) as Theme | null;
      return storedTheme || 'light';
    }
    return 'light';
  }

  /**
   * Apply theme classes to a dynamically created element
   * This is useful for plugins that create UI elements dynamically
   */
  public static applyThemeToElement(element: HTMLElement): void {
    const currentTheme = ThemeManager.getCurrentTheme();

    // Remove any existing theme classes
    element.classList.remove('theme-light', 'theme-dark');

    // Add the current theme class
    element.classList.add(`theme-${currentTheme}`);

    // Add dark class for Tailwind if the theme is dark
    if (currentTheme === 'dark') {
      element.classList.add('dark');
    } else {
      element.classList.remove('dark');
    }
  }

  /**
   * Initialize theme handling.
   */
  public async init(defaultTheme?: Theme): Promise<void> {
    // Check if user has a stored preference
    const hasStoredPreference = this.storage.getItem(this.themeKey) !== null;

    // Determine the theme to use
    let themeToApply: Theme;

    if (hasStoredPreference) {
      // Use stored preference if available
      themeToApply = this.storage.getItem(this.themeKey) as Theme;
    } else if (defaultTheme) {
      // Use provided default if no stored preference
      themeToApply = defaultTheme;
    } else {
      // Use system preference if no stored preference or default
      const prefersDark = this.win.matchMedia('(prefers-color-scheme: dark)');
      themeToApply = prefersDark.matches ? 'dark' : 'light';
    }

    // Apply the determined theme (don't persist if using system preference without explicit user choice)
    await this.applyTheme(themeToApply, hasStoredPreference);

    // Set up theme switcher
    const switcher = this.doc.getElementById('theme-switcher') as HTMLSelectElement | null;
    if (switcher) {
      switcher.value = themeToApply;
      switcher.addEventListener('change', async (e) => {
        const value = (e.target as HTMLSelectElement).value as Theme;
        // When user explicitly changes theme, always persist
        await this.applyTheme(value, true);
      });
    }

    // Only set up system preference listener if no user preference is stored
    this.setupSystemPreferenceListener();
  }

  /**
   * Programmatically set the current theme.
   */
  public async setTheme(theme: Theme): Promise<void> {
    await this.applyTheme(theme, true);
  }

  /**
   * Listen for theme changes. Returns an unsubscribe function.
   */
  public watch(callback: (theme: Theme) => void): () => void {
    const handler = (e: Event): void => {
      callback((e as CustomEvent<Theme>).detail);
    };
    this.doc.addEventListener('themechange', handler);
    return () => this.doc.removeEventListener('themechange', handler);
  }

  /* ------------------------------------------------------------------ */

  private async applyTheme(theme: Theme, persist = true): Promise<void> {
    if (!this.availableThemes.includes(theme)) {
      const error = new Error(`Theme "${theme}" not recognized`);
      throw error;
    }

    const rootEl = this.doc.documentElement;

    // Clear previous theme classes from body and root, and data-theme attribute
    this.doc.body.classList.remove(...this.availableThemes.map((t) => `theme-${t}`));
    rootEl.classList.remove('dark', ...this.availableThemes.map(t => `theme-${t}`));
    rootEl.removeAttribute('data-theme');

    // Apply theme-specific class to both body and root element for consistent CSS variable application
    this.doc.body.classList.add(`theme-${theme}`);
    rootEl.classList.add(`theme-${theme}`);

    // Apply Tailwind dark class to root element for dark mode
    if (theme === 'dark') {
      rootEl.classList.add('dark');
    }

    // Set data-theme attribute for CSS variable application
    rootEl.setAttribute('data-theme', theme);

    // Persist selection
    if (persist) {
      try {
        this.storage.setItem(this.themeKey, theme);
      } catch (error) {
        // Don't throw here - persistence failure shouldn't break theme application
      }
    }

    // Mirror value in select if present
    const switcher = this.doc.getElementById('theme-switcher') as HTMLSelectElement | null;
    if (switcher && switcher.value !== theme) {
      switcher.value = theme;
    }

    // Notify listeners
    this.doc.dispatchEvent(new CustomEvent<Theme>('themechange', {
      detail: theme,
      bubbles: true,
      composed: true
    }));

    // Dispatch a global theme change event for plugins to listen for
    window.dispatchEvent(new CustomEvent('feather:themechange', {
      detail: { theme }
    }));
  }

  private setupSystemPreferenceListener(): void {
    // If user already has a stored preference, don't set up the system preference listener
    if (this.storage.getItem(this.themeKey)) {
      return;
    }

    const prefersDark = this.win.matchMedia('(prefers-color-scheme: dark)');

    // Initial application of system preference (only if no user preference exists)
    if (!this.storage.getItem(this.themeKey)) {
      const systemTheme = prefersDark.matches ? 'dark' : 'light';

      // Apply system preference asynchronously without blocking
      this.applyTheme(systemTheme, false).catch(error => {
        console.warn('Failed to apply system theme preference:', error);
      });
    }

    // Add event listener for system preference changes
    const handleColorSchemeChange = (e: MediaQueryListEvent) => {
      // Double-check that user hasn't set a preference since we set up the listener
      if (!this.storage.getItem(this.themeKey)) {
        const newTheme = e.matches ? 'dark' : 'light';

        // Apply new system preference asynchronously without blocking
        this.applyTheme(newTheme, false).catch(error => {
          console.warn('Failed to apply system theme change:', error);
        });
      }
    };

    // Use the standard event listener API
    prefersDark.addEventListener('change', handleColorSchemeChange);
  }
}
