/**
 * Theme Helper
 *
 * This file contains helper functions for applying themes to dynamically created elements
 * and ensuring that all UI elements respect the user's theme preference.
 */

import { ThemeManager } from './theme-manager-legacy';

/**
 * Apply the current theme to a dynamically created element
 * @param element The element to apply the theme to
 */
export function applyThemeToElement(element: HTMLElement): void {
  ThemeManager.applyThemeToElement(element);
}

/**
 * Create a MutationObserver to watch for dynamically added elements and apply the theme to them
 */
export function setupThemeObserver(): void {
  // Create a MutationObserver to watch for dynamically added elements
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node instanceof HTMLElement) {
            // Check if this is a plugin UI element
            if (
              node.hasAttribute('role') &&
              (node.getAttribute('role') === 'dialog' || node.getAttribute('role') === 'menu')
            ) {
              applyThemeToElement(node);
            }

            // Check for absolute positioned elements that might be plugin UI
            if (
              node.classList.contains('absolute') &&
              (node.classList.contains('bg-white') ||
               node.classList.contains('dark:bg-slate-700') ||
               node.classList.contains('dark:bg-slate-800'))
            ) {
              applyThemeToElement(node);
            }

            // Check for elements with specific plugin-related classes
            const pluginClasses = [
              'feather-special-char-dialog',
              'feather-fullscreen-container',
              'feather-chart-wrapper',
              'feather-collapsible',
              'feather-table',
              'katex-container',
              'math-inline-wrapper',
              'math-display-wrapper',
              'feather-comment-panel',
              'feather-presence-bar',
              'feather-code-block'
            ];

            for (const className of pluginClasses) {
              if (node.classList.contains(className)) {
                applyThemeToElement(node);
                break;
              }
            }

            // Check for specific elements
            if (
              node.tagName === 'CANVAS' &&
              node.classList.contains('feather-chart')
            ) {
              applyThemeToElement(node);
            }
          }
        });
      }
    });
  });

  // Start observing the document body for added nodes
  observer.observe(document.body, { childList: true, subtree: true });

  // Listen for theme changes and update all plugin UI elements
  window.addEventListener('feather:themechange', () => {
    // Find all plugin UI elements and apply the theme
    document.querySelectorAll(`
      [role="dialog"],
      [role="menu"],
      .absolute.bg-white,
      .absolute.dark\\:bg-slate-700,
      .absolute.dark\\:bg-slate-800,
      .feather-special-char-dialog,
      .feather-fullscreen-container,
      .feather-chart-wrapper,
      .feather-collapsible,
      .feather-table,
      .katex-container,
      .math-inline-wrapper,
      .math-display-wrapper,
      .feather-comment-panel,
      .feather-presence-bar,
      .feather-code-block,
      canvas.feather-chart
    `).forEach((element) => {
      if (element instanceof HTMLElement) {
        applyThemeToElement(element);
      }
    });
  });
}
