import { describe, it, expect, beforeEach, vi } from 'vitest';
import { FormattingManager } from '../formatting-manager';
import type { IRenderer, ISelectionManager } from '../../types';

// Mock dependencies
const mockRenderer: IRenderer = {
  getElement: vi.fn(() => ({
    focus: vi.fn(),
    dispatchEvent: vi.fn(),
  }) as unknown as HTMLElement),
  setContent: vi.fn(),
  getContent: vi.fn(),
  focus: vi.fn(),
  initialize: vi.fn(),
  applyState: vi.fn(),
  insertNode: vi.fn(),
  deleteContents: vi.fn(),
  scrollIntoView: vi.fn(),
};

const mockSelectionManager: ISelectionManager = {
  initialize: vi.fn(),
  destroy: vi.fn(),
  getSelection: vi.fn(),
  getRange: vi.fn(),
  setRange: vi.fn(),
  isCollapsed: vi.fn(),
  getSelectedHtml: vi.fn(),
};

describe('FormattingManager', () => {
  let formattingManager: FormattingManager;

  // Define mockElement properties here
  const mockElementFocus = vi.fn();
  const mockElementDispatchEvent = vi.fn();
  const mockElement = {
      focus: mockElementFocus,
      dispatchEvent: mockElementDispatchEvent,
    } as unknown as HTMLElement; // Use the cast

  beforeEach(() => {
    formattingManager = new FormattingManager();
    vi.clearAllMocks(); // Clear global mocks

    // Reset specific mocks for the element
    mockElementFocus.mockClear();
    mockElementDispatchEvent.mockClear();

    // Set getElement to return the persistent mockElement
    mockRenderer.getElement = vi.fn(() => mockElement);

    // Reset mocks
  });

  describe('Initialization', () => {
    it('should store renderer and selectionManager references on initialization', () => {
      formattingManager.initialize(mockSelectionManager, mockRenderer);
      expect(() => formattingManager.initialize(mockSelectionManager, mockRenderer)).not.toThrow();
    });

    it('should throw error if initialize is called without renderer', () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect(() => formattingManager.initialize(mockSelectionManager, null as any)).toThrow('FormattingManager requires Renderer and SelectionManager.');
    });

    it('should throw error if initialize is called without selectionManager', () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      expect(() => formattingManager.initialize(null as any, mockRenderer)).toThrow('FormattingManager requires Renderer and SelectionManager.');
    });
  });

  describe('applyFormat', () => {
    // Mock range and selection for testing
    let mockRange: Range;
    let mockSelection: Selection;

    beforeEach(() => {
      // Initialize manager
      formattingManager.initialize(mockSelectionManager, mockRenderer);

      // Create mock range and selection with all required methods
      mockRange = {
        commonAncestorContainer: mockElement,
        startContainer: mockElement,
        endContainer: mockElement,
        startOffset: 0,
        endOffset: 0,
        collapsed: false,
        insertNode: vi.fn(),
        selectNodeContents: vi.fn(),
        cloneContents: vi.fn().mockReturnValue(document.createDocumentFragment()),
        deleteContents: vi.fn(),
        extractContents: vi.fn().mockReturnValue(document.createDocumentFragment()),
        surroundContents: vi.fn(),
        setStart: vi.fn(),
        setEnd: vi.fn(),
        cloneRange: vi.fn(),
      } as unknown as Range;

      mockSelection = {
        rangeCount: 1,
        getRangeAt: vi.fn().mockReturnValue(mockRange),
        removeAllRanges: vi.fn(),
        addRange: vi.fn(),
      } as unknown as Selection;

      // Mock selection manager methods
      mockSelectionManager.getSelection = vi.fn().mockReturnValue(mockSelection);
      mockSelectionManager.getRange = vi.fn().mockReturnValue(mockRange);
      mockSelectionManager.isCollapsed = vi.fn().mockReturnValue(false); // Non-collapsed selection
    });

    it('should focus the editor element when applying format', () => {
      const command = 'bold';
      formattingManager.applyFormat(command);

      // Check focus FIRST (as it happens first in the code)
      expect(mockRenderer.getElement).toHaveBeenCalled();
      expect(mockElement.focus).toHaveBeenCalled();
    });

    it('should handle collapsed selection (cursor only)', () => {
      const command = 'bold';
      mockSelectionManager.isCollapsed = vi.fn().mockReturnValue(true); // Collapsed selection

      formattingManager.applyFormat(command);

      expect(mockRenderer.getElement).toHaveBeenCalled();
      expect(mockElement.focus).toHaveBeenCalled();
      expect(mockSelectionManager.getSelection).toHaveBeenCalled();
      expect(mockSelectionManager.getRange).toHaveBeenCalled();
      expect(mockSelectionManager.isCollapsed).toHaveBeenCalled();
    });

    it('should handle non-collapsed selection (text selected)', () => {
      const command = 'italic';
      mockSelectionManager.isCollapsed = vi.fn().mockReturnValue(false); // Non-collapsed selection

      formattingManager.applyFormat(command);

      expect(mockRenderer.getElement).toHaveBeenCalled();
      expect(mockElement.focus).toHaveBeenCalled();
      expect(mockSelectionManager.getSelection).toHaveBeenCalled();
      expect(mockSelectionManager.getRange).toHaveBeenCalled();
      expect(mockSelectionManager.isCollapsed).toHaveBeenCalled();
    });

    it('should handle missing selection gracefully', () => {
      const command = 'bold';
      mockSelectionManager.getSelection = vi.fn().mockReturnValue(null);
      mockSelectionManager.getRange = vi.fn().mockReturnValue(null);

      // Should not throw error
      expect(() => formattingManager.applyFormat(command)).not.toThrow();

      expect(mockRenderer.getElement).toHaveBeenCalled();
      expect(mockElement.focus).toHaveBeenCalled();
    });

    it('should dispatch input event after applying format', () => {
      const command = 'bold';
      formattingManager.applyFormat(command);

      // Check that the element's dispatchEvent was called
      expect(mockElement.dispatchEvent).toHaveBeenCalled();
    });
  });
});
