import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SelectionManager } from '../selection-manager';

describe('SelectionManager', () => {
  let selectionManager: SelectionManager;
  let editorElement: HTMLElement;

  beforeEach(() => {
    // Create a mock editor element
    editorElement = document.createElement('div');
    editorElement.id = 'editor-test';
    document.body.appendChild(editorElement);

    // Instantiate SelectionManager
    selectionManager = new SelectionManager();
  });

  afterEach(() => {
    // Clean up the mock element
    document.body.removeChild(editorElement);
    // Reset any global mocks if necessary
    vi.resetAllMocks();
  });

  describe('Initialization', () => {
    it('should throw error if initialize is called without an element', () => {
      // @ts-expect-error Testing invalid input
      expect(() => selectionManager.initialize(null)).toThrow('SelectionManager requires an element for initialization.');
    });

    it('should add event listeners on initialization', () => {
      const addEventListenerSpy = vi.spyOn(document, 'addEventListener');
      selectionManager.initialize(editorElement);
      // Check if listeners for 'selectionchange' and potentially others are added
      // Note: This depends on the exact implementation details of _setupListeners
      expect(addEventListenerSpy).toHaveBeenCalledWith('selectionchange', expect.any(Function));
      // Add more checks if other listeners are expected
      addEventListenerSpy.mockRestore();
    });
  });

  describe('getSelection / getRange', () => {
    let mockSelection: Selection | null;
    let getSelectionSpy: ReturnType<typeof vi.spyOn>;

    const createMockRange = (): Range => ({
      // Basic mock properties - expand as needed for tests
      startContainer: editorElement,
      startOffset: 0,
      endContainer: editorElement,
      endOffset: 0,
      collapsed: true,
      commonAncestorContainer: editorElement,
      // Mock methods - implement basic functionality or use vi.fn()
      setStart: vi.fn(),
      setEnd: vi.fn(),
      selectNodeContents: vi.fn(),
      cloneRange: vi.fn(() => createMockRange()), // Return a new mock range
      // Add other Range methods/properties as needed by SelectionManager
    }) as unknown as Range; // Type assertion needed for partial mock

    beforeEach(() => {
      // Mock window.getSelection before each test in this suite
      getSelectionSpy = vi.spyOn(window, 'getSelection');
      selectionManager.initialize(editorElement); // Initialize AFTER spy setup
    });

    afterEach(() => {
      // Restore the original window.getSelection after each test
      getSelectionSpy.mockRestore();
    });

    it('getSelection should return the result of window.getSelection', () => {
      mockSelection = { rangeCount: 0 } as Selection; // Simple mock for this test
      getSelectionSpy.mockReturnValue(mockSelection);

      // Trigger the handler to update internal state AFTER mock is configured
      document.dispatchEvent(new Event('selectionchange'));

      // When rangeCount is 0, the internal _updateSelection sets currentSelection to null
      expect(selectionManager.getSelection()).toBeNull();
    });

    it('getRange should return the first range if selection exists', () => {
      const mockRange = createMockRange();
      mockSelection = {
        rangeCount: 1,
        getRangeAt: vi.fn().mockReturnValue(mockRange),
      } as unknown as Selection;
      getSelectionSpy.mockReturnValue(mockSelection);

      // Trigger the handler to update internal state AFTER mock is configured
      document.dispatchEvent(new Event('selectionchange'));

      expect(selectionManager.getRange()).toBe(mockRange);
      expect(mockSelection.getRangeAt).toHaveBeenCalledWith(0);
    });

    it('getRange should return null if no selection exists (rangeCount is 0)', () => {
      mockSelection = { rangeCount: 0 } as Selection;
      getSelectionSpy.mockReturnValue(mockSelection);

      expect(selectionManager.getRange()).toBeNull();
    });

    it('getRange should return null if window.getSelection returns null', () => {
      getSelectionSpy.mockReturnValue(null);
      expect(selectionManager.getRange()).toBeNull();
    });
  });

  describe('setRange', () => {
    let mockSelection: Selection | null;
    let getSelectionSpy: ReturnType<typeof vi.spyOn>;

    beforeEach(() => {
      // Mock window.getSelection and the returned Selection object
      mockSelection = {
        removeAllRanges: vi.fn(),
        addRange: vi.fn(),
      } as unknown as Selection;
      getSelectionSpy = vi.spyOn(window, 'getSelection').mockReturnValue(mockSelection);
      selectionManager.initialize(editorElement); // Initialize AFTER spy setup
    });

    afterEach(() => {
      getSelectionSpy.mockRestore();
    });

    it('should call removeAllRanges and addRange on the selection object for valid range', () => {
      // Create a range that is within the editor element
      const mockRange = {
        commonAncestorContainer: editorElement, // Make sure it's within the editor
        startContainer: editorElement,
        endContainer: editorElement,
        startOffset: 0,
        endOffset: 0,
      } as unknown as Range;

      selectionManager.setRange(mockRange);

      expect(mockSelection?.removeAllRanges).toHaveBeenCalled();
      expect(mockSelection?.addRange).toHaveBeenCalledWith(mockRange);
    });

    it('should not set range if it is outside the editor element', () => {
      // Create a range that is outside the editor element
      const outsideElement = document.createElement('div');
      const mockRange = {
        commonAncestorContainer: outsideElement, // Outside the editor
        startContainer: outsideElement,
        endContainer: outsideElement,
      } as unknown as Range;

      selectionManager.setRange(mockRange);

      // Should not call removeAllRanges or addRange for invalid range
      expect(mockSelection?.removeAllRanges).not.toHaveBeenCalled();
      expect(mockSelection?.addRange).not.toHaveBeenCalled();
    });

    it('should not throw if window.getSelection returns null', () => {
      const mockRange = {
        commonAncestorContainer: editorElement,
      } as unknown as Range;

      getSelectionSpy.mockReturnValue(null);
      expect(() => selectionManager.setRange(mockRange)).not.toThrow();
    });

    it('should throw error for null range', () => {
      expect(() => selectionManager.setRange(null as any)).toThrow('[SelectionManager] Cannot set null range');
    });
  });

  describe('destroy', () => {
    it('should remove event listeners added during initialization', () => {
      // Spy on addEventListener to capture the listener function reference
      const addSpy = vi.spyOn(document, 'addEventListener');
      selectionManager.initialize(editorElement);
      const selectionChangeListener = addSpy.mock.calls.find(call => call[0] === 'selectionchange')?.[1];
      addSpy.mockRestore(); // Restore original addEventListener

      const removeSpy = vi.spyOn(document, 'removeEventListener');
      selectionManager.destroy();

      // Verify removeEventListener was called with the same function reference
      expect(removeSpy).toHaveBeenCalledWith('selectionchange', selectionChangeListener);
      // Add checks for other listeners if necessary
      removeSpy.mockRestore();
    });
  });
});
