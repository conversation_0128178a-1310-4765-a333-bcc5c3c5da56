import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Renderer } from '../renderer';

// Mock placeCursorAtEnd at the top level
vi.mock('../../utils/cursor', () => ({
  placeCursorAtEnd: vi.fn()
}));

describe('Renderer', () => {
  let renderer: Renderer;
  let editorElement: HTMLElement;

  beforeEach(() => {
    // Create a mock editor element for each test
    editorElement = document.createElement('div');
    editorElement.id = 'editor-test';
    document.body.appendChild(editorElement);

    // Create a new Renderer instance
    renderer = new Renderer();
    // Note: Initialization happens within tests that need it
  });

  afterEach(() => {
    // Clean up the mock element
    document.body.removeChild(editorElement);
  });

  describe('Initialization', () => {
    it('should throw error if initialize is called without an element', () => {
      // @ts-expect-error Testing invalid input
      expect(() => renderer.initialize(null)).toThrow('Renderer requires an HTML element.');
    });

    it('should set contenteditable attribute on the element during initialization', () => {
      renderer.initialize(editorElement);
      expect(editorElement.getAttribute('contenteditable')).toBe('true');
    });

    it('should not overwrite existing contenteditable attribute if already true', () => {
      editorElement.setAttribute('contenteditable', 'true');
      renderer.initialize(editorElement);
      expect(editorElement.getAttribute('contenteditable')).toBe('true');
    });
  });

  describe('getElement', () => {
    it('should return the element it was initialized with', () => {
      renderer.initialize(editorElement);
      expect(renderer.getElement()).toBe(editorElement);
    });
  });

  describe('setContent / getContent', () => {
    beforeEach(() => {
      // Ensure renderer is initialized for these tests
      renderer.initialize(editorElement);
    });

    it('should set the innerHTML of the element', () => {
      const html = '<p>Hello World</p>';
      renderer.setContent(html);
      expect(editorElement.innerHTML).toBe(html);
    });

    it('should get the current innerHTML of the element', () => {
      const html = '<span>Test Content</span>';
      editorElement.innerHTML = html; // Set directly for testing getContent
      expect(renderer.getContent()).toBe(html);
    });

    it('should handle setting empty content', () => {
      renderer.setContent('');
      expect(editorElement.innerHTML).toBe('');
      expect(renderer.getContent()).toBe('');
    });
  });

  describe('focus', () => {
    it('should call focus on the element', () => {
      renderer.initialize(editorElement);
      const focusSpy = vi.spyOn(editorElement, 'focus');
      renderer.focus();
      expect(focusSpy).toHaveBeenCalled();
      focusSpy.mockRestore(); // Clean up spy
    });
  });

  describe('applyState', () => {

    beforeEach(() => {
      renderer.initialize(editorElement);
      // Reset mocks before each test if needed
      vi.clearAllMocks();
    });

    it('should set the content and attempt to place cursor at end', async () => {
      const { placeCursorAtEnd } = await import('../../utils/cursor');
      const htmlState = '<p>State applied</p>';
      renderer.applyState(htmlState);

      expect(renderer.getContent()).toBe(htmlState);
      expect(placeCursorAtEnd).toHaveBeenCalledWith(editorElement);
    });
  });

  // TODO: Add tests for insertNode and deleteContents (require Range mocks)
});
