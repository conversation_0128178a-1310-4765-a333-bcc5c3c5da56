import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
} from 'vitest';
import { EditorCore } from '../core';
import type {
  IHistoryManager,
  IClipboardManager,
  IFormattingManager,
  IRenderer,
  ISelectionManager,
} from '../../types';
import type { ThemeDefinition } from '../../themes/theme-types';

// Mock theme manager interface
interface MockThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

// Mock theme definitions
const mockLightTheme: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  description: 'Light theme for testing',
  version: '1.0.0',
  author: 'Test',
  isBuiltIn: true,
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  },
  stateColors: {
    hover: { background: '#f5f5f5', border: '#e0e0e0', text: '#333333' },
    active: { background: '#e0e0e0', border: '#d0d0d0', text: '#333333' },
    focus: { background: '#ffffff', border: '#3b82f6', ring: '#3b82f6' },
    disabled: { background: '#f5f5f5', border: '#e0e0e0', text: '#999999' }
  },
  pluginColors: {
    palette: {
      background: '#ffffff',
      border: '#e0e0e0',
      shadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      tabActiveBorder: '#4a86e8',
      swatchBorder: 'rgba(0, 0, 0, 0.1)'
    },
    chart: {
      background: '#ffffff',
      gridLines: '#e0e0e0',
      dataColors: ['#1565c0', '#28a745', '#ffc107', '#dc3545', '#17a2b8']
    },
    code: {
      background: '#f8f9fa',
      border: '#e0e0e0',
      text: '#333333',
      keyword: '#1565c0',
      string: '#28a745',
      comment: '#6c757d'
    },
    table: {
      background: '#ffffff',
      border: '#e0e0e0',
      headerBackground: '#f8f9fa',
      alternateRowBackground: '#f8f9fa'
    },
    comments: {
      background: '#ffffff',
      border: '#e0e0e0',
      highlightBackground: '#fff3cd',
      avatarBackground: '#f8f9fa'
    }
  },
  animation: {
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    respectReducedMotion: true
  },
  cssVariablePrefix: 'theme'
};

const mockDarkTheme: ThemeDefinition = {
  ...mockLightTheme,
  id: 'dark',
  name: 'Dark Theme',
  colors: {
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d',
    border: '#444444',
    primary: '#4fc3f7',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  },
  stateColors: {
    hover: { background: '#333333', border: '#555555', text: '#f0f0f0' },
    active: { background: '#444444', border: '#666666', text: '#f0f0f0' },
    focus: { background: '#2d2d2d', border: '#4fc3f7', ring: '#4fc3f7' },
    disabled: { background: '#333333', border: '#555555', text: '#888888' }
  },
  pluginColors: {
    palette: {
      background: '#2d2d2d',
      border: '#444444',
      shadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
      tabActiveBorder: '#4fc3f7',
      swatchBorder: 'rgba(255, 255, 255, 0.1)'
    },
    chart: {
      background: '#2d2d2d',
      gridLines: '#444444',
      dataColors: ['#4fc3f7', '#28a745', '#ffc107', '#dc3545', '#17a2b8']
    },
    code: {
      background: '#1a1a1a',
      border: '#444444',
      text: '#f0f0f0',
      keyword: '#4fc3f7',
      string: '#28a745',
      comment: '#888888'
    },
    table: {
      background: '#2d2d2d',
      border: '#444444',
      headerBackground: '#1a1a1a',
      alternateRowBackground: '#333333'
    },
    comments: {
      background: '#2d2d2d',
      border: '#444444',
      highlightBackground: '#3d3d00',
      avatarBackground: '#1a1a1a'
    }
  }
};

describe('EditorCore Theme Integration', () => {
  let editorCore: EditorCore;
  let mockElement: HTMLElement;
  let mockHistoryManager: IHistoryManager;
  let mockClipboardManager: IClipboardManager;
  let mockFormattingManager: IFormattingManager;
  let mockRenderer: IRenderer;
  let mockSelectionManager: ISelectionManager;
  let mockThemeManager: MockThemeManager;
  let themeChangeCallback: (theme: ThemeDefinition) => void;

  beforeEach(() => {
    // Create mock element
    mockElement = document.createElement('div');
    mockElement.id = 'test-editor';
    mockElement.classList.add('editor');
    document.body.appendChild(mockElement);

    // Create mock managers
    mockHistoryManager = {
      saveState: vi.fn(),
      undo: vi.fn().mockReturnValue(null),
      redo: vi.fn().mockReturnValue(null),
    } as any;

    mockClipboardManager = {
      initialize: vi.fn(),
      destroy: vi.fn(),
    } as any;

    mockFormattingManager = {
      initialize: vi.fn(),
      applyFormat: vi.fn(),
    } as any;

    mockRenderer = {
      initialize: vi.fn(),
      setContent: vi.fn(),
      getContent: vi.fn().mockReturnValue('<p>Test content</p>'),
      getElement: vi.fn().mockReturnValue(mockElement),
      applyState: vi.fn(),
      focus: vi.fn(),
    } as any;

    mockSelectionManager = {
      initialize: vi.fn(),
      destroy: vi.fn(),
    } as any;

    // Create mock theme manager
    mockThemeManager = {
      getCurrentTheme: vi.fn().mockReturnValue(mockLightTheme),
      watch: vi.fn().mockImplementation((callback) => {
        themeChangeCallback = callback;
        return vi.fn(); // Return unsubscribe function
      }),
    };
  });

  afterEach(() => {
    if (editorCore) {
      editorCore.destroy();
    }
    document.body.removeChild(mockElement);
    vi.restoreAllMocks();
  });

  describe('Theme Manager Integration', () => {
    it('should create editor core without theme manager', () => {
      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager
      );

      expect(editorCore).toBeDefined();
      expect(editorCore.getCurrentTheme()).toBeNull();
    });

    it('should initialize with theme manager and current theme', () => {
      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager,
        mockThemeManager
      );

      expect(mockThemeManager.getCurrentTheme).toHaveBeenCalled();
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
      expect(editorCore.getCurrentTheme()).toEqual(mockLightTheme);
    });

    it('should handle theme changes', () => {
      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager,
        mockThemeManager
      );

      // Simulate theme change
      themeChangeCallback(mockDarkTheme);

      expect(editorCore.getCurrentTheme()).toEqual(mockDarkTheme);
      expect(mockElement.classList.contains('theme-dark')).toBe(true);
      expect(mockElement.classList.contains('dark')).toBe(true);
      expect(mockElement.classList.contains('theme-light')).toBe(false);
    });

    it('should apply light theme classes correctly', () => {
      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager,
        mockThemeManager
      );

      // Simulate theme change to light
      themeChangeCallback(mockLightTheme);

      expect(mockElement.classList.contains('theme-light')).toBe(true);
      expect(mockElement.classList.contains('dark')).toBe(false);
      expect(mockElement.classList.contains('theme-dark')).toBe(false);
    });

    it('should clean up theme integration on destroy', () => {
      const unsubscribeMock = vi.fn();
      mockThemeManager.watch = vi.fn().mockReturnValue(unsubscribeMock);

      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager,
        mockThemeManager
      );

      editorCore.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
      expect(editorCore.getCurrentTheme()).toBeNull();
    });

    it('should handle theme change errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager,
        mockThemeManager
      );

      // Mock classList.add to throw an error to trigger the error handling
      const originalAdd = mockElement.classList.add;
      mockElement.classList.add = vi.fn().mockImplementation(() => {
        throw new Error('Mock classList error');
      });

      // Simulate theme change that will trigger an error
      themeChangeCallback(mockDarkTheme);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[EditorCore] Error applying theme to editor:'),
        expect.any(Error)
      );

      // Restore original method
      mockElement.classList.add = originalAdd;
      consoleSpy.mockRestore();
    });
  });

  describe('Core Functionality with Theme Integration', () => {
    beforeEach(() => {
      editorCore = new EditorCore(
        mockElement,
        mockHistoryManager,
        mockClipboardManager,
        mockFormattingManager,
        mockRenderer,
        mockSelectionManager,
        mockThemeManager
      );
    });

    it('should maintain core functionality with theme manager', () => {
      expect(editorCore.getElement()).toBe(mockElement);
      expect(editorCore.getContent()).toBe('<p>Test content</p>');

      editorCore.setContent('<p>New content</p>');
      expect(mockRenderer.setContent).toHaveBeenCalledWith('<p>New content</p>');

      editorCore.format('bold');
      expect(mockFormattingManager.applyFormat).toHaveBeenCalledWith('bold', undefined);

      editorCore.focus();
      expect(mockRenderer.focus).toHaveBeenCalled();
    });

    it('should handle undo/redo with theme integration', () => {
      mockHistoryManager.undo = vi.fn().mockReturnValue('<p>Previous content</p>');

      editorCore.undo();

      expect(mockHistoryManager.undo).toHaveBeenCalled();
      expect(mockRenderer.applyState).toHaveBeenCalledWith('<p>Previous content</p>');
    });
  });
});
