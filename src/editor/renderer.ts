import { <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { placeCursorAtEnd } from '../utils/cursor';
import type { ThemeDefinition } from '../themes/theme-types';

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Handles direct DOM manipulation and rendering for the editor content.
 */
export class Renderer implements IRenderer {
  private element!: HTMLElement; // Initialized via initialize
  private isApplyingState: boolean = false; // Prevent feedback loops when applying history

  // Theme integration
  private themeManager?: IThemeManager;
  private themeUnsubscribe?: () => void;
  private currentTheme: ThemeDefinition | null = null;

  constructor(themeManager?: IThemeManager) {
    // Set up theme integration if theme manager is provided
    this.themeManager = themeManager;
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }
  }

  /**
   * Initializes the renderer with the editor element.
   * @param element The editor's root HTML element.
   */
  initialize(element: HTMLElement): void {
    if (!element) {
      throw new Error('Renderer requires an HTML element.');
    }
    this.element = element;
    if (!this.element.getAttribute('contenteditable')) {
      this.element.setAttribute('contenteditable', 'true');
    }
    // Setup can now happen here
    this._handlePlaceholder(); // Initial check
    this.ensureScrollable(); // Configure scrolling behavior
    this._setupKeyboardNavigation(); // Handle keyboard navigation
  }

  /**
   * Sets up event listeners for keyboard navigation to ensure
   * proper scrolling and focus management during arrow key navigation
   * @private
   */
  private _setupKeyboardNavigation(): void {
    const element = this.getElement();

    // Handle arrow key navigation to ensure proper scrolling
    element.addEventListener('keydown', (event: KeyboardEvent) => {
      // Only handle arrow keys and maintain keyboard accessibility
      if (!['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
        return;
      }

      // Let default behavior happen but ensure scrolling works correctly
      // Use a small timeout to let the browser position the caret first
      setTimeout(() => {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          this.scrollIntoView(range);

          // Prevent the event from affecting anything outside the editor
          element.focus();
        }
      }, 0);
    });

    // Ensure editor maintains focus when clicking inside it
    element.addEventListener('mousedown', (event) => {
      // Only for clicks inside the editor
      if (event.target && element.contains(event.target as Node)) {
        // Prevent focus from moving to toolbar or other elements
        event.stopPropagation();
      }
    });
  }

  getElement(): HTMLElement {
    if (!this.element) {
      throw new Error('Renderer not initialized.');
    }
    return this.element;
  }

  getContent(): string {
    return this.getElement().innerHTML;
  }

  setContent(html: string): void {
    // Potentially sanitize HTML here if not done elsewhere
    this.getElement().innerHTML = html;
    // Consider if placeholder logic belongs here
    this._handlePlaceholder();
  }

  focus(): void {
    this.getElement().focus();
  }

  insertNode(node: Node): void {
    const selection = window.getSelection();
    if (selection && selection.rangeCount) {
      const range = selection.getRangeAt(0);
      range.deleteContents();
      range.insertNode(node);

      // Move cursor after the inserted node
      // Only try to set cursor position if the node was successfully inserted and has a parent
      if (node.parentNode) {
        range.setStartAfter(node);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);

        // Scroll to the inserted content
        this.scrollIntoView(node);
      } else {
        // Node wasn't properly inserted, use a safer fallback approach
        // Just collapse the range to the end of the current position
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }

  deleteContents(range: Range): void {
    range.deleteContents();
  }

  /**
   * Applies a state (HTML) to the editor, typically from history,
   * without triggering recursive history saves.
   * Also handles placing the cursor correctly after applying state.
   */
  applyState(html: string): void {
    if (this.isApplyingState) return;
    this.isApplyingState = true;
    try {
      this.element.innerHTML = html;
      // Placeholder check after applying state
      this._handlePlaceholder();
      // Place cursor at the end after state is applied
      placeCursorAtEnd(this.element);
    } finally {
      this.isApplyingState = false;
    }
  }

  /**
   * Manages the display of a placeholder when the editor is empty.
   * TODO: Make placeholder text configurable.
   * @private
   */
  private _handlePlaceholder(): void {
    // Check if editor is effectively empty (ignore placeholder itself or single <br>)
    const isEmpty = !this.element.textContent?.trim() || this.element.innerHTML === '<p><br></p>';
    if (isEmpty && !this.element.classList.contains('placeholder')) {
      // Apply placeholder (e.g., using a data attribute or class)
      // This is a simple example; could be more sophisticated
      // Ensure we don't wipe content if it's just a focus event on empty editor
      if (!this.element.firstChild || this.element.firstChild.nodeName === '#text') {
        this.element.innerHTML = '<p><br></p>'; // Ensure a valid paragraph structure
      }
      this.element.classList.add('placeholder');
      // this.element.setAttribute('data-placeholder', 'Start typing here...');
    } else if (!isEmpty && this.element.classList.contains('placeholder')) {
       this.element.classList.remove('placeholder');
      // this.element.removeAttribute('data-placeholder');
    }
  }

   // Potentially add methods for getting/setting attributes, classes, etc.

  /**
   * Ensures the editor element is properly configured for scrolling content
   * that exceeds the editor's height.
   */
  ensureScrollable(): void {
    // const element = this.getElement(); // Element is available via this.element

    // The following style manipulations are now handled by Tailwind classes
    // applied to #editor and .editor-container via src/styles/base.css.
    // This method can be kept for any future non-styling scroll-related logic,
    // or removed if it becomes entirely empty.

    // Example of what was removed:
    // const computedStyle = window.getComputedStyle(this.element);
    // if (computedStyle.overflowY !== 'auto' && computedStyle.overflowY !== 'scroll') {
    //   this.element.style.overflowY = 'auto';
    // }
    // if (!this.element.style.minHeight && !computedStyle.minHeight) {
    //  this.element.style.minHeight = '300px';
    // }
    // if (!this.element.style.padding && computedStyle.padding === '0px') {
    //   this.element.style.padding = '1rem';
    // }
    // const parentElement = this.element.parentElement;
    // if (parentElement && parentElement.classList.contains('editor-container')) {
    //   const containerStyle = window.getComputedStyle(parentElement);
    //   if (containerStyle.display !== 'flex') {
    //     parentElement.style.display = 'flex';
    //     parentElement.style.flexDirection = 'column';
    //   }
    //   if (!parentElement.style.maxHeight) {
    //     parentElement.style.maxHeight = '80vh';
    //   }
    // }
  }

  /**
   * Scrolls the editor to make the given node or range visible
   * @param nodeOrRange Node or Range to scroll into view
   * @param options ScrollIntoViewOptions for controlling the scroll behavior
   */
  scrollIntoView(nodeOrRange?: Node | Range | null, options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'nearest' }): void {
    // Ensure editor is scrollable
    this.ensureScrollable();

    if (!nodeOrRange) {
      // If no node/range provided, use current selection
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        nodeOrRange = selection.getRangeAt(0);
      } else {
        return; // No selection to scroll to
      }
    }

    // Handle different input types
    if (nodeOrRange instanceof Range) {
      // For Range objects, get the end container and scroll to it
      const rangeEnd = document.createRange();
      rangeEnd.selectNode(nodeOrRange.endContainer);

      // Create a temporary span to mark the position
      const tempMarker = document.createElement('span');
      tempMarker.style.display = 'inline';
      tempMarker.innerHTML = '\u200B'; // Zero-width space

      try {
        // Clone the range to avoid modifying the original
        const tempRange = rangeEnd.cloneRange();
        tempRange.collapse(false);
        tempRange.insertNode(tempMarker);

        // Scroll to the marker
        tempMarker.scrollIntoView(options);

        // Remove the marker
        if (tempMarker.parentNode) {
          tempMarker.parentNode.removeChild(tempMarker);
        }
      } catch (error) {
        console.warn('Failed to scroll to range:', error);
      }
    } else if (nodeOrRange instanceof Node) {
      // For Node objects, scroll directly to the node if it's an HTMLElement
      try {
        if (nodeOrRange instanceof HTMLElement) {
          nodeOrRange.scrollIntoView(options);
        } else if (nodeOrRange.parentElement) {
          // If not an HTMLElement but has a parent element, scroll to the parent
          nodeOrRange.parentElement.scrollIntoView(options);
        } else {
          // No direct way to scroll to this node, try to find its range
          const tempRange = document.createRange();
          try {
            tempRange.selectNode(nodeOrRange);
            this.scrollIntoView(tempRange, options);
          } catch (rangeError) {
            console.warn('Could not create range for node:', rangeError);
          }
        }
      } catch (error) {
        console.warn('Failed to scroll to node:', error);
      }
    }
  }

  /**
   * Get the current theme (for theme integration)
   */
  getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Handle theme changes and update content rendering
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;

      // Apply theme to the editor element if initialized
      if (this.element) {
        this._applyThemeToElement(theme);
      }
    } catch (error) {
      console.error('[Renderer] Error handling theme change:', error);
    }
  }

  /**
   * Apply theme styling to the editor element
   * @param theme - Theme to apply
   * @private
   */
  private _applyThemeToElement(theme: ThemeDefinition): void {
    try {
      // Apply theme class to the editor element
      this.element.classList.remove('theme-light', 'theme-dark');
      this.element.classList.add(`theme-${theme.id}`);

      // Apply dark class for Tailwind if the theme is dark
      if (theme.id === 'dark') {
        this.element.classList.add('dark');
      } else {
        this.element.classList.remove('dark');
      }

      // Update placeholder styling if element has placeholder
      if (this.element.classList.contains('placeholder')) {
        this._updatePlaceholderTheme(theme);
      }
    } catch (error) {
      console.error('[Renderer] Error applying theme to element:', error);
    }
  }

  /**
   * Update placeholder styling for the current theme
   * @param _theme - Theme to apply (reserved for future use)
   * @private
   */
  private _updatePlaceholderTheme(_theme: ThemeDefinition): void {
    try {
      // The placeholder styling is handled by CSS variables
      // This method is reserved for any future placeholder-specific theme logic

      // CSS variables like --editor-placeholder-text are automatically applied
      // through the theme class on the element
    } catch (error) {
      console.error('[Renderer] Error updating placeholder theme:', error);
    }
  }

  /**
   * Clean up theme integration resources
   */
  destroy(): void {
    if (this.themeUnsubscribe) {
      this.themeUnsubscribe();
      this.themeUnsubscribe = undefined;
    }
    this.themeManager = undefined;
    this.currentTheme = null;
  }
}
