import {
  IHistoryManager,
  IClipboardManager,
  IFormattingManager,
  IRenderer,
  ISelectionManager,
  IEditorCore
} from '../types';
import type { ThemeDefinition } from '../themes/theme-types';

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Central orchestrator for the Feather editor.
 */
export class EditorCore implements IEditorCore {
  private element: HTMLElement;
  private historyManager: IHistoryManager;
  private clipboardManager: IClipboardManager;
  private formattingManager: IFormattingManager;
  private renderer: IRenderer;
  private selectionManager: ISelectionManager;

  // Theme integration
  private themeManager?: IThemeManager;
  private themeUnsubscribe?: () => void;
  private currentTheme: ThemeDefinition | null = null;

  // Store bound listeners for easy removal
  private _boundHandleBeforeInput: (event: Event) => void;
  private _boundHandleInput: (event: Event) => void;
  private _boundHandleClick: (event: MouseEvent) => void;
  private _boundHandleDragOver: (event: DragEvent) => void;
  private _boundHandleDrop: (event: DragEvent) => void;

  constructor(
    element: HTMLElement,
    historyManager: IHistoryManager,
    clipboardManager: IClipboardManager,
    formattingManager: IFormattingManager,
    renderer: IRenderer,
    selectionManager: ISelectionManager,
    themeManager?: IThemeManager
  ) {
    if (!element) {
      throw new Error('Editor element is required');
    }

    this.element = element;
    this.historyManager = historyManager;
    this.clipboardManager = clipboardManager;
    this.formattingManager = formattingManager;
    this.renderer = renderer;
    this.selectionManager = selectionManager;

    // Set up theme integration if theme manager is provided
    this.themeManager = themeManager;
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }

    // Bind listener methods
    this._boundHandleBeforeInput = this._handleBeforeInput.bind(this);
    this._boundHandleInput = this._handleInput.bind(this);
    this._boundHandleClick = this._handleClick.bind(this);
    this._boundHandleDragOver = this._handleDragOver.bind(this);
    this._boundHandleDrop = this._handleDrop.bind(this);

    // Initialize dependencies
    this.renderer.initialize(this.element);
    // Set initial content *after* renderer is initialized
    this.renderer.setContent('<p>Start typing here...</p>');
    this.historyManager.saveState(this.renderer.getContent()); // Save initial state

    // Initialize managers that need element/other managers
    this.selectionManager.initialize(this.element);
    this.clipboardManager.initialize(this.element, this.selectionManager, this.renderer);
    this.formattingManager.initialize(this.selectionManager, this.renderer);

    // Setup core event listeners
    this._setupListeners();
  }

  private _setupListeners(): void {
    this.element.addEventListener('beforeinput', this._boundHandleBeforeInput);
    this.element.addEventListener('input', this._boundHandleInput);
    this.element.addEventListener('dragover', this._boundHandleDragOver);
    this.element.addEventListener('drop', this._boundHandleDrop);
    // Use document click for focus logic if needed, or element click
    this.element.addEventListener('click', this._boundHandleClick);
    // Selection change is likely handled within SelectionManager
  }

  private _removeListeners(): void {
    this.element.removeEventListener('beforeinput', this._boundHandleBeforeInput);
    this.element.removeEventListener('input', this._boundHandleInput);
    this.element.removeEventListener('dragover', this._boundHandleDragOver);
    this.element.removeEventListener('drop', this._boundHandleDrop);
    this.element.removeEventListener('click', this._boundHandleClick);
  }

  // --- Event Handlers --- //

  private _handleBeforeInput(_event: Event): void {
    // Save state before potential modification
    this.historyManager.saveState(this.renderer.getContent());
  }

  private _handleInput(_event: Event): void {
    // Placeholder logic - move to Renderer?
    if (this.element.textContent?.trim() === '') {
      // Use renderer to set content, ensuring it's saved to history potentially
      this.renderer.setContent('<p><br></p>');
      // TODO: Decide if input should trigger history save, or rely on beforeinput
      // this.historyManager.saveState(this.renderer.getContent());
    }
    // Input event might trigger other actions or checks
  }

  private _handleClick(event: MouseEvent): void {
    // Focus the editor if the click is directly on it (and not selecting text?)
    if (event.target === this.element) {
        // Check if selection is collapsed before focusing? TBD
        this.renderer.focus();
    }
  }

  private _handleDragOver(e: DragEvent): void {
    // Prevent default to indicate this is a valid drop target
    e.preventDefault();

    // Set the drop effect based on data types (copy for images, text)
    if (e.dataTransfer) {
      try {
        const types = Array.from(e.dataTransfer.items || [])
          .map(item => item.type);

        const hasImage = types.some(type => type.startsWith('image/'));
        const hasText = types.some(type => type === 'text/plain' || type === 'text/html');

        // Only accept drops we can handle
        if (hasImage || hasText) {
          e.dataTransfer.dropEffect = 'copy';
        } else {
          e.dataTransfer.dropEffect = 'none';
        }
      } catch (error) {
        console.error('[EditorCore] Error handling drag over:', error);
        // Fallback to allowing the drop
        if (e.dataTransfer) {
          e.dataTransfer.dropEffect = 'copy';
        }
      }
    }
  }

  private _handleDrop(e: DragEvent): void {
    // Always prevent default browser drop behavior
    e.preventDefault();

    if (!e.dataTransfer || !e.dataTransfer.items || !e.dataTransfer.items.length) {
      console.warn('[EditorCore] Drop event without data transfer items');
      return;
    }

    try {
      // Focus the editor when something is dropped
      this.element.focus();

      // Save state before modification (will be a no-op if no actual change)
      this.historyManager.saveState(this.renderer.getContent());

      // Handle files (like images) first
      const files = Array.from(e.dataTransfer.files || []);
      if (files.length > 0) {
        for (const file of files) {
          if (file.type.startsWith('image/')) {
            this._handleImageDrop(file);
            return; // Only handle one image at a time
          }
        }
      }

      // Handle text content
      for (const item of Array.from(e.dataTransfer.items)) {
        if (item.kind === 'string') {
          if (item.type === 'text/html') {
            item.getAsString((html) => {
              // Insert sanitized HTML at cursor position
              const sanitizedHtml = this._sanitizeHtml(html);
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = sanitizedHtml;

              // Insert the parsed nodes at cursor position
              const selection = window.getSelection();
              if (selection && selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);

                // Clear selection contents
                range.deleteContents();

                // Insert each child of the temporary div
                while (tempDiv.firstChild) {
                  range.insertNode(tempDiv.firstChild);
                }

                // Move cursor to end of inserted content
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);

                // Save state after modification
                this.historyManager.saveState(this.renderer.getContent());
              }
            });
            return;
          } else if (item.type === 'text/plain') {
            item.getAsString((text) => {
              // Insert text at cursor position using renderer methods
              const selection = window.getSelection();
              if (selection && selection.rangeCount > 0) {
                try {
                  const range = selection.getRangeAt(0);

                  // Create a text node with the plain text content
                  const textNode = document.createTextNode(text);

                  // Delete any existing selection content first
                  range.deleteContents();

                  // Insert the text node
                  range.insertNode(textNode);

                  // Move cursor to end of inserted text
                  range.setStartAfter(textNode);
                  range.setEndAfter(textNode);
                  range.collapse(false);
                  selection.removeAllRanges();
                  selection.addRange(range);

                  // Save state after modification
                  this.historyManager.saveState(this.renderer.getContent());
                } catch (err) {
                  console.error('[EditorCore] Error inserting plain text:', err);
                }
              }
            });
            return;
          }
        }
      }
    } catch (error) {
      console.error('[EditorCore] Error handling drop:', error);
    }
  }

  /**
   * Handle dropped image files by converting to data URLs
   * @private
   */
  private _handleImageDrop(file: File): void {
    if (!file.type.startsWith('image/')) {
      console.warn('[EditorCore] File is not an image:', file.type);
      return;
    }

    try {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (!event.target || typeof event.target.result !== 'string') {
          console.error('[EditorCore] Failed to read image file');
          return;
        }

        // Create image element with the data URL
        const img = document.createElement('img');
        img.src = event.target.result;
        img.alt = file.name;
        img.className = 'max-w-full'; // Replaced inline style with Tailwind class

        // Insert the image at cursor position
        this.renderer.insertNode(img);

        // Save state after modification
        this.historyManager.saveState(this.renderer.getContent());
      };

      reader.onerror = () => {
        console.error('[EditorCore] Error reading file:', reader.error);
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('[EditorCore] Error processing image drop:', error);
    }
  }

  /**
   * Simple HTML sanitizer to prevent XSS in pasted/dropped content
   * @private
   */
  private _sanitizeHtml(html: string): string {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Remove potentially harmful elements and attributes
    const sanitize = (node: Node): void => {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const el = node as HTMLElement;

        // Remove script tags and on* attributes completely
        if (el.tagName.toLowerCase() === 'script') {
          el.parentNode?.removeChild(el);
          return;
        }

        // Remove potentially dangerous attributes
        const dangerousAttrs = Array.from(el.attributes)
          .filter(attr => attr.name.startsWith('on') || attr.name === 'href' && attr.value.startsWith('javascript:'));

        dangerousAttrs.forEach(attr => {
          el.removeAttribute(attr.name);
        });

        // Process children recursively
        Array.from(el.childNodes).forEach(sanitize);
      }
    };

    sanitize(tempDiv);
    return tempDiv.innerHTML;
  }

  // --- IEditorCore Implementation --- //

  getElement(): HTMLElement {
    return this.renderer.getElement();
  }

  getContent(): string {
    return this.renderer.getContent();
  }

  setContent(html: string): void {
    this.renderer.setContent(html);
    this.historyManager.saveState(this.renderer.getContent());
  }

  format(command: string, value?: string | null): void {
    this.formattingManager.applyFormat(command, value);
    this.historyManager.saveState(this.renderer.getContent());
  }

  undo(): void {
    const restoredState = this.historyManager.undo();
    if (restoredState !== null) {
      // Use the renderer's dedicated method to apply state without triggering history saves
      this.renderer.applyState(restoredState);
    }
  }

  redo(): void {
    const restoredState = this.historyManager.redo();
    if (restoredState !== null) {
      // Use the renderer's dedicated method to apply state
      this.renderer.applyState(restoredState);
    }
  }

  focus(): void {
    this.renderer.focus();
  }

  /**
   * Get the current theme (for theme integration)
   */
  getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Handle theme changes and propagate to managed components
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;

      // Apply theme to the editor element
      this._applyThemeToEditor(theme);

      // Propagate theme changes to components that support theme integration
      this._propagateThemeToComponents(theme);
    } catch (error) {
      console.error('[EditorCore] Error handling theme change:', error);
    }
  }

  /**
   * Apply theme styling to the editor element
   * @param theme - Theme to apply
   * @private
   */
  private _applyThemeToEditor(theme: ThemeDefinition): void {
    try {
      // Apply theme class to the editor element
      this.element.classList.remove('theme-light', 'theme-dark');
      this.element.classList.add(`theme-${theme.id}`);

      // Apply dark class for Tailwind if the theme is dark
      if (theme.id === 'dark') {
        this.element.classList.add('dark');
      } else {
        this.element.classList.remove('dark');
      }
    } catch (error) {
      console.error('[EditorCore] Error applying theme to editor:', error);
    }
  }

  /**
   * Propagate theme changes to managed components
   * @param _theme - Theme to propagate (reserved for future use)
   * @private
   */
  private _propagateThemeToComponents(_theme: ThemeDefinition): void {
    try {
      // Note: Components like ClipboardManager, FormattingManager, etc.
      // will handle their own theme integration through their own ThemeManager instances
      // This method is reserved for future component-specific theme propagation if needed

      // For now, we rely on the global theme change events that components listen to
      // This ensures loose coupling and follows the established pattern
    } catch (error) {
      console.error('[EditorCore] Error propagating theme to components:', error);
    }
  }

  destroy(): void {
    this._removeListeners();
    this.clipboardManager.destroy();
    this.selectionManager.destroy();

    // Clean up theme integration
    if (this.themeUnsubscribe) {
      this.themeUnsubscribe();
      this.themeUnsubscribe = undefined;
    }
    this.themeManager = undefined;
    this.currentTheme = null;

    // Destroy other managers if they implement destroy()
    console.log('Editor destroyed');
  }
}
