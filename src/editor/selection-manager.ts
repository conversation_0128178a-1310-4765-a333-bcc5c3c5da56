import { ISelectionManager } from '../types';
import type { ThemeDefinition } from '../themes/theme-types';

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Manages user selection within the editor.
 */
export class SelectionManager implements ISelectionManager {
  private element!: HTMLElement; // Initialized via initialize
  private currentSelection: Selection | null = null;
  private currentRange: Range | null = null;
  private _boundHandleSelectionChange: () => void;

  // Theme integration
  private themeManager?: IThemeManager;
  private themeUnsubscribe?: () => void;
  private currentTheme: ThemeDefinition | null = null;

  constructor(themeManager?: IThemeManager) {
    this._boundHandleSelectionChange = this._handleSelectionChange.bind(this);

    // Set up theme integration if theme manager is provided
    this.themeManager = themeManager;
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }
  }

  /**
   * Initializes the selection manager with the editor element.
   * @param element The editor's root HTML element.
   */
  initialize(element: HTMLElement): void {
    if (!element) {
      throw new Error('SelectionManager requires an element for initialization.');
    }
    this.element = element;
    this._setupListeners();
    this._updateSelection(); // Initial check
  }

  destroy(): void {
    if (!this.element) return;
    document.removeEventListener('selectionchange', this._boundHandleSelectionChange);
    this.currentSelection = null;
    this.currentRange = null;

    // Clean up theme integration
    if (this.themeUnsubscribe) {
      this.themeUnsubscribe();
      this.themeUnsubscribe = undefined;
    }
    this.themeManager = undefined;
    this.currentTheme = null;
  }

  private _setupListeners(): void { // Keep private if only called by initialize
    if (!this.element) return;
    document.addEventListener('selectionchange', this._boundHandleSelectionChange);
  }

  private _handleSelectionChange(): void {
    this._updateSelection();
    // Dispatch a custom event for other components to react to selection changes
    document.dispatchEvent(new CustomEvent('editor-selection-change', {
      detail: { selection: this.currentSelection, range: this.currentRange }
    }));
  }

  private _updateSelection(): void {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      // Check if the selection is within our editor element
      if (this.element.contains(range.commonAncestorContainer)) {
        this.currentSelection = selection;
        this.currentRange = range;
        return; // Valid selection found
      }
    }
    // If no valid selection within the editor, reset
    this.currentSelection = null;
    this.currentRange = null;
  }

  getSelection(): Selection | null {
    return this.currentSelection;
  }

  getRange(): Range | null {
    // Return a clone to prevent external modification?
    // For now, return the live range.
    return this.currentRange;
  }

  /**
   * Sets the current selection range
   * @param range Range to set as the current selection
   * @throws Error if range is null or not valid
   */
  setRange(range: Range): void {
    if (!range) {
      throw new Error('[SelectionManager] Cannot set null range');
    }

    try {
      const selection = window.getSelection();
      if (!selection) {
        console.error('[SelectionManager] Failed to get window selection');
        return;
      }

      // Ensure the range is within our editor element
      if (!this.element.contains(range.commonAncestorContainer)) {
        console.warn('[SelectionManager] Attempted to set range outside editor');
        return;
      }

      selection.removeAllRanges();
      selection.addRange(range);

      // Update internal state after manually setting
      this._updateSelection();
    } catch (error) {
      console.error('[SelectionManager] Error setting range:', error);
      throw new Error(`[SelectionManager] Failed to set range: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Checks if the current selection is collapsed (i.e., just a cursor)
   * @returns true if the selection is collapsed or if no selection exists
   */
  isCollapsed(): boolean {
    return !this.currentRange || this.currentRange.collapsed;
  }

  /**
   * Gets the HTML content of the current selection
   * @returns HTML string of the selected content or empty string if no selection
   */
  getSelectedHtml(): string {
    if (!this.currentRange || this.currentRange.collapsed) {
      return '';
    }

    try {
      const div = document.createElement('div');
      div.appendChild(this.currentRange.cloneContents());
      return div.innerHTML;
    } catch (error) {
      console.error('[SelectionManager] Error getting selected HTML:', error);
      return '';
    }
  }

  /**
   * Gets the plain text content of the current selection
   * @returns Text string of the selected content or empty string if no selection
   */
  getSelectedText(): string {
    if (!this.currentRange || this.currentRange.collapsed) {
      return '';
    }

    try {
      const div = document.createElement('div');
      div.appendChild(this.currentRange.cloneContents());
      return div.textContent || '';
    } catch (error) {
      console.error('[SelectionManager] Error getting selected text:', error);
      return '';
    }
  }

  /**
   * Select entire contents of a DOM node
   * @param node The node whose contents should be selected
   * @returns true if selection was successful
   */
  selectNodeContents(node: Node): boolean {
    if (!node || !this.element.contains(node)) {
      console.warn('[SelectionManager] Cannot select invalid or external node');
      return false;
    }

    try {
      const selection = window.getSelection();
      if (!selection) return false;

      const range = document.createRange();
      range.selectNodeContents(node);

      selection.removeAllRanges();
      selection.addRange(range);

      this._updateSelection();
      return true;
    } catch (error) {
      console.error('[SelectionManager] Error selecting node contents:', error);
      return false;
    }
  }

  /**
   * Get the current theme (for theme integration)
   */
  getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Handle theme changes and update selection styling
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;

      // Apply theme to selection indicators if element is initialized
      if (this.element) {
        this._applyThemeToSelection(theme);
      }

      // Dispatch selection theme change event for external components
      this._dispatchSelectionThemeChange(theme);
    } catch (error) {
      console.error('[SelectionManager] Error handling theme change:', error);
    }
  }

  /**
   * Apply theme styling to selection indicators
   * @param theme - Theme to apply
   * @private
   */
  private _applyThemeToSelection(theme: ThemeDefinition): void {
    try {
      // Apply theme class to the editor element for selection styling
      this.element.classList.remove('theme-light', 'theme-dark');
      this.element.classList.add(`theme-${theme.id}`);

      // Apply dark class for Tailwind if the theme is dark
      if (theme.id === 'dark') {
        this.element.classList.add('dark');
      } else {
        this.element.classList.remove('dark');
      }
    } catch (error) {
      console.error('[SelectionManager] Error applying theme to selection:', error);
    }
  }

  /**
   * Dispatch selection theme change event
   * @param theme - Theme that changed
   * @private
   */
  private _dispatchSelectionThemeChange(theme: ThemeDefinition): void {
    try {
      document.dispatchEvent(new CustomEvent('selection-theme-change', {
        detail: {
          theme,
          selection: this.currentSelection,
          range: this.currentRange
        }
      }));
    } catch (error) {
      console.error('[SelectionManager] Error dispatching selection theme change:', error);
    }
  }
}
