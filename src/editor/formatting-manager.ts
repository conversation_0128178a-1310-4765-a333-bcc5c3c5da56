import { IFormattingManager, I<PERSON><PERSON><PERSON>, ISelectionManager } from '../types';
import type { ThemeDefinition } from '../themes/theme-types';

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Manages applying and querying formatting commands within the editor.
 */
export class FormattingManager implements IFormattingManager {
  private renderer!: IRenderer;
  private selectionManager!: ISelectionManager;
  private activeFormats: Record<string, boolean | string> = {}; // Track active formatting at cursor position

  // Theme integration
  private themeManager?: IThemeManager;
  private themeUnsubscribe?: () => void;
  private currentTheme: ThemeDefinition | null = null;

  constructor(themeManager?: IThemeManager) {
    // Set up theme integration if theme manager is provided
    this.themeManager = themeManager;
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }
  }

  /**
   * Initializes the FormattingManager with necessary dependencies.
   * @param selectionManager - The selection manager instance.
   * @param renderer - The renderer instance.
   */
  initialize(selectionManager: ISelectionManager, renderer: IRenderer): void {
    if (!renderer || !selectionManager) {
      throw new Error('FormattingManager requires Renderer and SelectionManager.');
    }
    this.renderer = renderer;
    this.selectionManager = selectionManager;

    // Setup listeners for selection changes to update UI
    document.addEventListener('editor-selection-change', this._handleSelectionChange.bind(this));

    // Initialize active formats
    this.activeFormats = {
      'bold': false,
      'italic': false,
      'underline': false,
      'strikeThrough': false
    };
  }

  /**
   * Handle selection changes and update formatting state accordingly
   * @private
   */
  private _handleSelectionChange(): void {
    const isCollapsed = this.selectionManager.isCollapsed();

    if (isCollapsed) {
      // When cursor only (no selection), use our tracked active formats
      // This ensures toolbar buttons stay active when formatting is enabled for cursor
      this._dispatchFormatStateChange();
    } else {
      // For actual selections, query the browser for formatting
      // This resets our active formats tracking to match current selection
      this.activeFormats = {
        'bold': document.queryCommandState('bold'),
        'italic': document.queryCommandState('italic'),
        'underline': document.queryCommandState('underline'),
        'strikeThrough': document.queryCommandState('strikeThrough')
      };
      this._dispatchFormatStateChange();
    }
  }

  /**
   * Dispatch an event with the current formatting state
   * @private
   */
  private _dispatchFormatStateChange(): void {
    document.dispatchEvent(new CustomEvent('editor-format-state-change', {
      detail: { formats: this.activeFormats }
    }));
  }

  /**
   * Applies a formatting command.
   * @param command - The command identifier (e.g., 'bold', 'italic').
   * @param value - Optional value for the command (e.g., font name for 'fontName').
   */
  applyFormat(command: string, value?: string): void {
    // Ensure editor has focus
    this.renderer.getElement().focus();

    const selection = this.selectionManager.getSelection();
    const range = this.selectionManager.getRange();
    const isCollapsed = this.selectionManager.isCollapsed();

    try {
      if (!selection || !range) {
        console.warn('[FormattingManager] No valid selection found while applying format:', command);
        return;
      }

      if (isCollapsed) {
        // Cursor only – delegate to collapsed handler
        this._applyFormatToCollapsedSelection(command, value, selection, range);
      } else {
        // Non-collapsed – wrap selected contents
        this._applyFormatToRange(command, value, range);
      }

      // Track active formats for UI updates (only basic toggles for now)
      if (typeof this.activeFormats[command] !== 'undefined') {
        this.activeFormats[command] = !this.activeFormats[command];
      }
      this._dispatchFormatStateChange();
    } catch (error) {
      console.error('[FormattingManager] Failed to apply format', command, error);
    } finally {
      // Trigger synthetic input so that HistoryManager can capture change
      this.renderer.getElement().dispatchEvent(
        new InputEvent('input', { bubbles: true, cancelable: true })
      );
    }
  }

  /**
   * Applies formatting when no text is selected (cursor position only).
   * This creates a better UX by allowing users to set formatting for upcoming text.
   * @private
   */
  private _applyFormatToCollapsedSelection(
    command: string,
    value: string | undefined,
    selection: Selection,
    range: Range
  ): void {
    try {
      // Inline formatting commands supported
      const inlineCommands: Record<string, string> = {
        bold: 'strong',
        italic: 'em',
        underline: 'u',
        strikeThrough: 's',
        'inline-code': 'code'
      };

      if (inlineCommands[command]) {
        // Insert a placeholder element carrying the desired tag / style
        this._insertPlaceholderWithFormat(inlineCommands[command], command, value, range, selection);
        return;
      }

      // Color commands – store as active format so future input inherits CSS
      if (command === 'foreColor' || command === 'backColor') {
        this.activeFormats[command] = value ?? '';
        return;
      }

      // Unsupported on collapsed selection – no-op but log
      console.info('[FormattingManager] Command not applicable to collapsed selection:', command);
    } catch (err) {
      console.error('[FormattingManager] Error applying format to collapsed selection', err);
    }
  }

  /**
   * Wraps the contents of a range in the appropriate element / style.
   */
  private _applyFormatToRange(command: string, value: string | undefined, range: Range): void {
    const inlineCommands: Record<string, string> = {
      bold: 'strong',
      italic: 'em',
      underline: 'u',
      strikeThrough: 's',
      'inline-code': 'code'
    };

    try {
      if (inlineCommands[command]) {
        this._wrapRangeWithElement(inlineCommands[command], range);
        return;
      }

      if (command === 'foreColor' || command === 'backColor') {
        const span = document.createElement('span');
        if (command === 'foreColor') span.style.color = value ?? '';
        else span.style.backgroundColor = value ?? '';
        this._wrapRangeWithElement(span, range);
        return;
      }

      // TODO: Implement block-level commands (headings, lists, etc.) in Renderer helpers.
      console.warn('[FormattingManager] Block-level command not yet implemented:', command);
    } catch (error) {
      console.error('[FormattingManager] Failed to apply format to range', error);
    }
  }

  /**
   * Utility to wrap a range with the provided element/tag.
   */
  private _wrapRangeWithElement(tagOrElement: string | HTMLElement, range: Range): void {
    const element: HTMLElement =
      typeof tagOrElement === 'string' ? document.createElement(tagOrElement) : tagOrElement;

    const contents = range.extractContents();
    element.appendChild(contents);
    range.insertNode(element);

    // Reset selection to the wrapped node
    this.selectionManager.setRange(range);
    this.renderer.scrollIntoView(element);
  }

  /**
   * Insert a zero-width placeholder wrapped in a formatting element so that
   * upcoming user input inherits the format. The placeholder is immediately
   * selected so any subsequent keystroke replaces it.
   */
  private _insertPlaceholderWithFormat(
    tagName: string,
    command: string,
    value: string | undefined,
    range: Range,
    selection: Selection
  ): void {
    const placeholder = document.createElement(tagName);
    placeholder.innerHTML = '\u200B'; // ZWSP

    // Handle color commands on inline placeholder
    if (command === 'foreColor') placeholder.style.color = value ?? '';
    if (command === 'backColor') placeholder.style.backgroundColor = value ?? '';

    range.insertNode(placeholder);

    // Move caret inside placeholder so next input overwrites ZWSP
    const newRange = document.createRange();
    newRange.selectNodeContents(placeholder);
    newRange.collapse();
    selection.removeAllRanges();
    selection.addRange(newRange);

    this.renderer.scrollIntoView(placeholder);
  }

  /**
   * Checks if a command is currently active (e.g., is the selection bold?).
   * @param command - The command identifier.
   * @returns True if the command state is active, false otherwise.
   */
  isFormatActive(command: string): boolean {
    const isCollapsed = this.selectionManager.isCollapsed();

    if (isCollapsed) {
      // When no text is selected (cursor only), use our tracked state
      return Boolean(this.activeFormats[command]);
    } else {
      // When text is selected, use browser's formatting detection
      return document.queryCommandState(command);
    }
  }

  /**
   * Gets the current value associated with a command (e.g., the font name of the selection).
   * @param command - The command identifier.
   * @returns The current value of the command.
   */
  getFormatValue(command: string): string {
    // this.renderer.getElement().focus(); // Might not be needed, test carefully
    return document.queryCommandValue(command);
  }

  /**
   * Gets the formatting state of the current selection (bold, italic, etc.).
   * This might be more detailed than queryCommandState.
   * @returns An object representing the current formatting state.
   */
  getFormatState(): Record<string, boolean | string> {
    // Example implementation using queryCommandState/Value
    // A more robust version might inspect the DOM directly via SelectionManager/Renderer
    const states: Record<string, boolean | string> = {};
    const commands = ['bold', 'italic', 'underline', 'strikeThrough', 'foreColor', 'backColor', 'fontName', 'fontSize'];

    commands.forEach(cmd => {
      if (['foreColor', 'backColor', 'fontName', 'fontSize'].includes(cmd)) {
        states[cmd] = this.getFormatValue(cmd);
      } else {
        states[cmd] = this.isFormatActive(cmd);
      }
    });

    // Example: Check for lists (might require DOM inspection)
    // states['orderedList'] = this._isNodeInList(this.selectionManager.getRange()?.commonAncestorContainer, 'OL');
    // states['unorderedList'] = this._isNodeInList(this.selectionManager.getRange()?.commonAncestorContainer, 'UL');

    return states;
  }

  /**
   * Get the current theme (for theme integration)
   */
  getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Handle theme changes and update formatting indicators
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;

      // Update formatting state indicators with theme-aware styling
      this._updateFormattingIndicators(theme);
    } catch (error) {
      console.error('[FormattingManager] Error handling theme change:', error);
    }
  }

  /**
   * Update formatting indicators with theme-aware styling
   * @param theme - Theme to apply
   * @private
   */
  private _updateFormattingIndicators(theme: ThemeDefinition): void {
    try {
      // Dispatch a custom event for toolbar buttons to update their styling
      document.dispatchEvent(new CustomEvent('formatting-theme-change', {
        detail: {
          theme,
          activeFormats: this.activeFormats
        }
      }));
    } catch (error) {
      console.error('[FormattingManager] Error updating formatting indicators:', error);
    }
  }

  /**
   * Clean up theme integration resources
   */
  destroy(): void {
    if (this.themeUnsubscribe) {
      this.themeUnsubscribe();
      this.themeUnsubscribe = undefined;
    }
    this.themeManager = undefined;
    this.currentTheme = null;
  }

  /*
  private _isNodeInList(node: Node | undefined | null, listTag: 'OL' | 'UL'): boolean {
      if (!node) return false;
      let currentNode: Node | null = node;
      const editorElement = this.renderer.getElement();
      while (currentNode && currentNode !== editorElement) {
          if (currentNode.nodeName === listTag) {
              return true;
          }
          currentNode = currentNode.parentNode;
      }
      return false;
  }
  */
}
