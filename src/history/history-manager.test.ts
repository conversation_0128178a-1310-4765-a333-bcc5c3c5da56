import {
  describe,
  test,
  it,
  expect,
  beforeEach,
  afterEach,
  vi
} from 'vitest';
import { HistoryManager } from './history-manager';
import type { ThemeDefinition } from '../themes/theme-types';

// Mock theme manager interface
interface MockThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

// Mock theme definitions
const mockLightTheme: ThemeDefinition = {
  id: 'light',
  name: 'Light Theme',
  description: 'Light theme for testing',
  version: '1.0.0',
  author: 'Test',
  isBuiltIn: true,
  colors: {
    text: '#333333',
    background: '#ffffff',
    surface: '#f8f9fa',
    border: '#e0e0e0',
    primary: '#1565c0',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  },
  stateColors: {
    hover: { background: '#f5f5f5', border: '#e0e0e0', text: '#333333' },
    active: { background: '#e0e0e0', border: '#d0d0d0', text: '#333333' },
    focus: { background: '#ffffff', border: '#3b82f6', ring: '#3b82f6' },
    disabled: { background: '#f5f5f5', border: '#e0e0e0', text: '#999999' }
  },
  pluginColors: {
    palette: {
      background: '#ffffff',
      border: '#e0e0e0',
      shadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      tabActiveBorder: '#4a86e8',
      swatchBorder: 'rgba(0, 0, 0, 0.1)'
    },
    chart: {
      background: '#ffffff',
      gridLines: '#e0e0e0',
      dataColors: ['#1565c0', '#28a745', '#ffc107', '#dc3545', '#17a2b8']
    },
    code: {
      background: '#f8f9fa',
      border: '#e0e0e0',
      text: '#333333',
      keyword: '#1565c0',
      string: '#28a745',
      comment: '#6c757d'
    },
    table: {
      background: '#ffffff',
      border: '#e0e0e0',
      headerBackground: '#f8f9fa',
      alternateRowBackground: '#f8f9fa'
    },
    comments: {
      background: '#ffffff',
      border: '#e0e0e0',
      highlightBackground: '#fff3cd',
      avatarBackground: '#f8f9fa'
    }
  },
  animation: {
    duration: 300,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    respectReducedMotion: true
  },
  cssVariablePrefix: 'theme'
};

const mockDarkTheme: ThemeDefinition = {
  ...mockLightTheme,
  id: 'dark',
  name: 'Dark Theme',
  colors: {
    text: '#f0f0f0',
    background: '#1a1a1a',
    surface: '#2d2d2d',
    border: '#444444',
    primary: '#4fc3f7',
    secondary: '#6c757d',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545',
    info: '#17a2b8'
  }
};

describe('History Manager', () => {
  let historyManager: HistoryManager;

  beforeEach(() => {
    historyManager = new HistoryManager();
  });

  test('initializes with empty state', () => {
    expect(historyManager.canUndo()).toBe(false);
    expect(historyManager.canRedo()).toBe(false);
  });

  test('saves unique states', () => {
    historyManager.saveState('state 1');
    historyManager.saveState('state 2');

    expect(historyManager.canUndo()).toBe(true);
  });

  test('ignores duplicate states', () => {
    historyManager.saveState('same state');
    historyManager.saveState('same state'); // This should be ignored

    // Expect 1 state since duplicates are ignored
    expect(historyManager.undoCount()).toBe(0);
  });

  test('clears redo stack on new state', () => {
    historyManager.saveState('state 1');
    historyManager.saveState('state 2');

    historyManager.undo();

    historyManager.saveState('state 3');

    expect(historyManager.canRedo()).toBe(false);
  });

  test('maintains maximum states limit', () => {
    for (let i = 0; i < 55; i++) {
      historyManager.saveState(`state ${i}`);
    }

    expect(historyManager.undoCount()).toBeLessThanOrEqual(50);
  });

  test('emits history change event', () => {
    const eventSpy = vi.fn();
    // Use window instead of document to match the dispatch target
    window.addEventListener('history-change', eventSpy);

    historyManager.saveState('new state');

    expect(eventSpy).toHaveBeenCalled();

    // Clean up the listener
    window.removeEventListener('history-change', eventSpy);
  });
});

describe('HistoryManager Theme Integration', () => {
  let historyManager: HistoryManager;
  let mockThemeManager: MockThemeManager;
  let themeChangeCallback: (theme: ThemeDefinition) => void;
  let eventSpy: any;

  beforeEach(() => {
    // Mock window.dispatchEvent
    eventSpy = vi.spyOn(window, 'dispatchEvent').mockImplementation(() => true);

    // Create mock theme manager
    mockThemeManager = {
      getCurrentTheme: vi.fn().mockReturnValue(mockLightTheme),
      watch: vi.fn().mockImplementation((callback) => {
        themeChangeCallback = callback;
        return vi.fn(); // Return unsubscribe function
      }),
    };
  });

  afterEach(() => {
    if (historyManager) {
      historyManager.destroy();
    }
    eventSpy.mockRestore();
    vi.restoreAllMocks();
  });

  describe('Theme Manager Integration', () => {
    it('should create history manager without theme manager', () => {
      historyManager = new HistoryManager();

      expect(historyManager).toBeDefined();
      expect(historyManager.getCurrentTheme()).toBeNull();
    });

    it('should initialize with theme manager and current theme', () => {
      historyManager = new HistoryManager({}, mockThemeManager);

      expect(mockThemeManager.getCurrentTheme).toHaveBeenCalled();
      expect(mockThemeManager.watch).toHaveBeenCalledWith(expect.any(Function));
      expect(historyManager.getCurrentTheme()).toEqual(mockLightTheme);
    });

    it('should handle theme changes', () => {
      historyManager = new HistoryManager({}, mockThemeManager);

      // Simulate theme change
      themeChangeCallback(mockDarkTheme);

      expect(historyManager.getCurrentTheme()).toEqual(mockDarkTheme);

      // Should dispatch theme-aware history change event
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'history-theme-change',
          detail: expect.objectContaining({
            theme: mockDarkTheme,
            canUndo: false,
            canRedo: false,
            undoCount: 0,
            redoCount: 0
          })
        })
      );
    });

    it('should include theme in regular history change events', () => {
      historyManager = new HistoryManager({}, mockThemeManager);
      eventSpy.mockClear(); // Clear initialization events

      // Save a state to trigger history change event
      historyManager.saveState('<p>Test content</p>');

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'history-change',
          detail: expect.objectContaining({
            theme: mockLightTheme,
            canUndo: false,
            canRedo: false
          })
        })
      );
    });

    it('should clean up theme integration on destroy', () => {
      const unsubscribeMock = vi.fn();
      mockThemeManager.watch = vi.fn().mockReturnValue(unsubscribeMock);

      historyManager = new HistoryManager({}, mockThemeManager);
      historyManager.destroy();

      expect(unsubscribeMock).toHaveBeenCalled();
      expect(historyManager.getCurrentTheme()).toBeNull();
    });

    it('should handle theme change errors gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      historyManager = new HistoryManager({}, mockThemeManager);

      // Mock dispatchEvent to throw an error
      eventSpy.mockImplementation(() => {
        throw new Error('Mock dispatch error');
      });

      // Simulate theme change that will trigger an error
      themeChangeCallback(mockDarkTheme);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[HistoryManager] Error dispatching theme-aware history change:'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('History Functionality with Theme Integration', () => {
    beforeEach(() => {
      historyManager = new HistoryManager({}, mockThemeManager);
      eventSpy.mockClear(); // Clear initialization events
    });

    it('should maintain history functionality with theme manager', () => {
      // Test basic history operations
      historyManager.saveState('<p>First state</p>');
      historyManager.saveState('<p>Second state</p>');

      expect(historyManager.canUndo()).toBe(true);
      expect(historyManager.undoCount()).toBe(1);

      const undoResult = historyManager.undo();
      expect(undoResult).toBe('<p>First state</p>');
      expect(historyManager.canRedo()).toBe(true);

      const redoResult = historyManager.redo();
      expect(redoResult).toBe('<p>Second state</p>');
    });

    it('should include theme in all history events', () => {
      historyManager.saveState('<p>Test</p>');
      historyManager.undo();
      historyManager.redo();
      historyManager.clearHistory('<p>New start</p>');

      // All events should include theme information
      const historyChangeEvents = eventSpy.mock.calls.filter(
        (call: any) => (call[0] as CustomEvent).type === 'history-change'
      );

      historyChangeEvents.forEach((call: any) => {
        const event = call[0] as CustomEvent;
        expect(event.detail.theme).toEqual(mockLightTheme);
      });
    });

    it('should handle SSR environment gracefully', () => {
      // Mock SSR environment (no window)
      const originalWindow = global.window;
      // @ts-expect-error - Intentionally setting to undefined for SSR test
      global.window = undefined;

      // Should not throw error
      expect(() => {
        historyManager.saveState('<p>SSR test</p>');
      }).not.toThrow();

      // Restore window
      global.window = originalWindow;
    });
  });
});