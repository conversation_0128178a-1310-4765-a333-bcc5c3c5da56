/**
 * Manages a linear undo/redo history for HTML snapshots (string).
 *
 * Robust against duplicate snapshots, branch divergence, and server‑side rendering.
 * Emits a native `history-change` event so UI controls can stay in sync.
 * Performs defensive validation and error checking for all operations.
 */
import type { IHistoryManager } from '../types';
import type { ThemeDefinition } from '../themes/theme-types';

// Interface for theme manager dependency (optional)
interface IThemeManager {
  getCurrentTheme(): ThemeDefinition | null;
  watch(callback: (theme: ThemeDefinition) => void): () => void;
}

/**
 * Custom options for history state management
 */
export interface HistoryManagerOptions {
  /** Maximum number of history states to keep in memory */
  maxStates?: number;
  /** Custom comparison function to determine if two states should be considered equal */
  compare?: (prev: string, next: string) => boolean;
  /** Initial state to populate the history with */
  initialState?: string;
  /** Maximum size in bytes for a single state before compression is applied */
  maxStateSize?: number;
}

/**
 * Event detail for the history change event
 */
export interface HistoryChangeEventDetail {
  /** Whether undo operation is available */
  canUndo: boolean;
  /** Whether redo operation is available */
  canRedo: boolean;
  /** Count of available undo states */
  undoCount: number;
  /** Count of available redo states */
  redoCount: number;
  /** Current theme for UI components (optional) */
  theme?: ThemeDefinition | null;
}

/**
 * Manages editor state history for undo/redo functionality
 *
 * Single Responsibility: Manages history state only
 * Open/Closed: Extendable through options parameter
 * Liskov Substitution: Implements the IHistoryManager interface
 * Interface Segregation: Only exposes necessary methods
 * Dependency Inversion: Uses abstractions, not concrete implementations
 */
export class HistoryManager implements IHistoryManager {
  /** Maximum snapshots to keep in `undoStack` (sliding window) */
  private readonly maxSnapshots: number;

  /** Older → newer snapshots the user can step **backwards** through. */
  private readonly undoStack: string[] = [];

  /** Newer → older snapshots available for **redo** */
  private readonly redoStack: string[] = [];

  /** Custom equality predicate so callers can ignore insignificant diffs. */
  private readonly isEqual: (prev: string, next: string) => boolean;

  /** Maximum size in bytes for a state before applying compression */
  private readonly maxStateSize: number;

  /**
   * Flag to prevent recursive broadcast events
   * @private
   */
  private isBroadcasting = false;

  /**
   * Performance metrics for state operations
   * @private
   */
  private metrics = {
    lastSaveTime: 0,
    compressionApplied: 0,
    stateBytes: 0
  };

  // Theme integration
  private themeManager?: IThemeManager;
  private themeUnsubscribe?: () => void;
  private currentTheme: ThemeDefinition | null = null;

  /**
   * Creates a new history manager
   * @param options - Configuration for history behavior
   * @param themeManager - Optional theme manager for theme integration
   */
  constructor(options: HistoryManagerOptions = {}, themeManager?: IThemeManager) {
    // Validate and apply options with safe defaults
    this.maxSnapshots = this._validateMaxStates(options.maxStates);
    this.maxStateSize = options.maxStateSize ?? 1024 * 1024; // Default 1MB

    // Default: compare after normalizing whitespace at either end
    this.isEqual = options.compare ?? this._defaultCompare;

    // Initialize with initial state if provided
    if (options.initialState !== undefined) {
      try {
        this._validateState(options.initialState);
        this.undoStack.push(options.initialState);
      } catch (error) {
        console.error('[HistoryManager] Invalid initial state:', error);
        // Create an empty initial state as fallback
        this.undoStack.push('');
      }
    }

    // Set up theme integration if theme manager is provided
    this.themeManager = themeManager;
    if (this.themeManager) {
      this.currentTheme = this.themeManager.getCurrentTheme();
      this.themeUnsubscribe = this.themeManager.watch(this._handleThemeChange.bind(this));
    }
  }

  /**
   * Capture a new snapshot from the editor. Clears the redo branch because
   * a divergent history has begun.
   * @param currentState - The current editor content to save
   * @throws Error if currentState is not a string
   */
  public saveState(currentState: string): void {
    try {
      // Validate input
      this._validateState(currentState);

      // Performance measurement
      const start = performance.now();

      // Skip if identical to previous state
      const last = this.undoStack.at(-1);
      if (last !== undefined && this.isEqual(last, currentState)) {
        return; // Ignore identical consecutive states
      }

      // Track state size metrics
      this.metrics.stateBytes = currentState.length * 2; // Rough estimate: 2 bytes per char

      // Check if state is too large and compress if needed
      let stateToSave = currentState;
      if (this.metrics.stateBytes > this.maxStateSize) {
        stateToSave = this._compressState(currentState);
        this.metrics.compressionApplied++;
      }

      // Save the new state
      this.undoStack.push(stateToSave);
      this.redoStack.length = 0; // 🔄 Discard redo branch

      // Apply sliding window if needed
      if (this.undoStack.length > this.maxSnapshots) {
        this.undoStack.shift(); // Maintain sliding window
      }

      // Track performance
      this.metrics.lastSaveTime = performance.now() - start;

      // Notify listeners about the history change
      this.broadcastHistoryChange();
    } catch (error) {
      console.error('[HistoryManager] Error saving state:', error);
      throw new Error(`[HistoryManager] Failed to save state: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Step one snapshot backwards through history
   * @returns The previous state or null if no previous state exists
   */
  public undo(): string | null {
    try {
      if (!this.canUndo()) {
        return null;
      }

      const popped = this.undoStack.pop();
      if (popped === undefined) { // Safety check
        return null;
      }

      this.redoStack.push(popped);

      const snapshotToRestore = this.undoStack.at(-1);
      if (snapshotToRestore === undefined) { // Safety check
        // This shouldn't happen due to canUndo() check, but just in case
        return null;
      }

      this.broadcastHistoryChange();
      return snapshotToRestore;
    } catch (error) {
      console.error('[HistoryManager] Error during undo operation:', error);
      return null;
    }
  }

  /**
   * Step one snapshot forwards through history
   * @returns The next state or null if no next state exists
   */
  public redo(): string | null {
    try {
      if (!this.canRedo()) {
        return null;
      }

      const snapshotToRestore = this.redoStack.pop();
      if (snapshotToRestore === undefined) { // Safety check
        return null;
      }

      this.undoStack.push(snapshotToRestore);

      this.broadcastHistoryChange();
      return snapshotToRestore;
    } catch (error) {
      console.error('[HistoryManager] Error during redo operation:', error);
      return null;
    }
  }

  /**
   * Checks if undo operation is available
   * @returns True if undo is available, false otherwise
   */
  public canUndo(): boolean {
    return this.undoStack.length > 1;
  }

  /**
   * Checks if redo operation is available
   * @returns True if redo is available, false otherwise
   */
  public canRedo(): boolean {
    return this.redoStack.length > 0;
  }

  /**
   * Get the number of available undo steps
   * @returns Number of undo steps available
   */
  public undoCount(): number {
    // We subtract 1 because the current state is included in undoStack
    return Math.max(0, this.undoStack.length - 1);
  }

  /**
   * Get the number of available redo steps
   * @returns Number of redo steps available
   */
  public redoCount(): number {
    return this.redoStack.length;
  }

  /**
   * Clear all history (both undo and redo stacks)
   * @param initialState - Optional new initial state
   * @returns True if history was cleared successfully
   */
  public clearHistory(initialState = ''): boolean {
    try {
      this._validateState(initialState);
      this.undoStack.length = 0;
      this.redoStack.length = 0;
      this.undoStack.push(initialState);
      this.broadcastHistoryChange();
      return true;
    } catch (error) {
      console.error('[HistoryManager] Error clearing history:', error);
      return false;
    }
  }

  /**
   * Get performance metrics about history operations
   * @returns Object containing performance metrics
   */
  public getMetrics(): Record<string, number> {
    return {
      undoStackSize: this.undoStack.length,
      redoStackSize: this.redoStack.length,
      lastSaveTimeMs: this.metrics.lastSaveTime,
      compressionCount: this.metrics.compressionApplied,
      estimatedMemoryBytes: this._estimateMemoryUsage()
    };
  }

  /* ------------------------------------------------------------------ */
  /*                             Utilities                               */
  /* ------------------------------------------------------------------ */

  /**
   * Default state comparison function
   * @private
   */
  private _defaultCompare(a: string, b: string): boolean {
    return a.trim() === b.trim();
  }

  /**
   * Validate the max states count
   * @param value - Proposed max states value
   * @returns Valid max states count
   * @private
   */
  private _validateMaxStates(value?: number): number {
    if (value === undefined) return 50; // Default
    if (typeof value !== 'number') return 50;
    if (value < 2) return 2; // Minimum of 2 states needed (current + previous)
    if (value > 1000) return 1000; // Reasonable upper limit
    return Math.floor(value); // Ensure integer
  }

  /**
   * Validate that a state is a valid string
   * @param state - The state to validate
   * @throws Error if state is invalid
   * @private
   */
  private _validateState(state: unknown): asserts state is string {
    if (typeof state !== 'string') {
      throw new Error('[HistoryManager] State must be a string');
    }
  }

  /**
   * Simple compression for large states to reduce memory usage
   * This is a placeholder - in a real implementation, use a proper compression algorithm
   * @param state - Large state to compress
   * @returns Compressed state
   * @private
   */
  private _compressState(state: string): string {
    // This is where you could implement LZ compression or similar
    // For now we'll just return the original state
    console.warn('[HistoryManager] Large state detected, compression recommended');
    return state;
  }

  /**
   * Estimate memory usage of history stacks
   * @returns Estimated bytes used by history
   * @private
   */
  private _estimateMemoryUsage(): number {
    // Rough estimate: 2 bytes per character + object overhead
    const undoBytes = this.undoStack.reduce((sum, state) => sum + (state.length * 2) + 40, 0);
    const redoBytes = this.redoStack.reduce((sum, state) => sum + (state.length * 2) + 40, 0);
    return undoBytes + redoBytes;
  }

  /**
   * Emit a native event so external UI controls can update reactively.
   * @private
   */
  private broadcastHistoryChange(): void {
    // Prevent recursive broadcasts
    if (this.isBroadcasting) return;

    try {
      this.isBroadcasting = true;

      // Guard against SSR – no `window` object.
      if (typeof window === 'undefined') return;

      // Create event detail with all relevant history state including theme
      const detail: HistoryChangeEventDetail = {
        canUndo: this.canUndo(),
        canRedo: this.canRedo(),
        undoCount: this.undoCount(),
        redoCount: this.redoCount(),
        theme: this.currentTheme // Include current theme for UI components
      };

      // Dispatch event with history state information
      window.dispatchEvent(
        new CustomEvent('history-change', { detail })
      );
    } catch (error) {
      console.error('[HistoryManager] Error broadcasting history change:', error);
    } finally {
      this.isBroadcasting = false;
    }
  }

  /**
   * Get the current theme (for theme integration)
   */
  getCurrentTheme(): ThemeDefinition | null {
    return this.currentTheme;
  }

  /**
   * Handle theme changes and update history event dispatching
   * @param theme - New theme definition
   * @private
   */
  private _handleThemeChange(theme: ThemeDefinition): void {
    try {
      this.currentTheme = theme;

      // Dispatch a theme-aware history change event to update UI components
      this._dispatchThemeAwareHistoryChange(theme);
    } catch (error) {
      console.error('[HistoryManager] Error handling theme change:', error);
    }
  }

  /**
   * Dispatch a theme-aware history change event
   * @param theme - Theme that changed
   * @private
   */
  private _dispatchThemeAwareHistoryChange(theme: ThemeDefinition): void {
    try {
      // Guard against SSR
      if (typeof window === 'undefined') return;

      // Dispatch a specific theme change event for history UI components
      window.dispatchEvent(new CustomEvent('history-theme-change', {
        detail: {
          theme,
          canUndo: this.canUndo(),
          canRedo: this.canRedo(),
          undoCount: this.undoCount(),
          redoCount: this.redoCount()
        }
      }));
    } catch (error) {
      console.error('[HistoryManager] Error dispatching theme-aware history change:', error);
    }
  }

  /**
   * Clean up theme integration resources
   */
  destroy(): void {
    if (this.themeUnsubscribe) {
      this.themeUnsubscribe();
      this.themeUnsubscribe = undefined;
    }
    this.themeManager = undefined;
    this.currentTheme = null;
  }
}
