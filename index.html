<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rich Text Editor</title>
    <style>
      /* Minimal style to ensure html/body take full height before JS/CSS loads if needed */
      html, body { height: 100%; margin: 0; padding: 0; }
    </style>
  </head>
  <body class="font-sans antialiased">
    <div class="editor-container" role="application">
      <label id="editor-label" class="sr-only">Rich text editor</label>
      <header id="toolbar" role="toolbar" aria-label="Formatting tools"></header>
      <div id="editor" 
           role="textbox" 
           aria-multiline="true"
           aria-labelledby="editor-label"
           contenteditable="true"
           tabindex="0"></div>
    </div>
    <div class="theme-controls">
      <label for="theme-switcher">Theme:</label>
      <select id="theme-switcher">
        <option value="light">Light</option>
        <option value="dark">Dark</option>
      </select>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
