{"name": "@rich-text/core", "version": "1.0.0", "description": "A modern, extensible rich text editor built with TypeScript", "type": "module", "private": false, "license": "MIT", "author": {"name": "Rich Text Editor Team"}, "repository": {"type": "git", "url": "https://github.com/rich-text/editor"}, "keywords": ["editor", "rich-text", "wysiwyg", "contenteditable", "typescript"], "main": "./dist/editor.umd.js", "module": "./dist/editor.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/editor.mjs", "require": "./dist/editor.umd.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"dev": "bunx vite dev", "build": "bunx vite build", "lint": "bunx eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "bunx prettier --check .", "format:fix": "bunx prettier --write .", "typecheck": "bunx tsc --noEmit", "test": "bunx vitest --globals", "test:run": "bunx vitest run", "test:watch": "bunx vitest --watch"}, "devDependencies": {"@axe-core/cli": "^4.10.1", "@testing-library/dom": "^10.4.0", "@types/chart.js": "^2.9.41", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitest/coverage-v8": "^3.1.2", "autoprefixer": "^10.4.21", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "jsdom": "^26.1.0", "prettier": "^3.5.3", "puppeteer": "^24.7.1", "tailwindcss": "^4.1.6", "ts-migrate": "^0.1.35", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.2", "vitest": "^3.1.2"}, "dependencies": {"@tailwindcss/vite": "^4.1.6", "@types/katex": "^0.16.7", "chart.js": "^4.4.9", "katex": "^0.16.22"}}