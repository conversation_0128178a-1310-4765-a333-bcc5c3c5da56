// eslint.config.js
import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';
import eslintConfigPrettier from 'eslint-config-prettier';

export default [
  {
    languageOptions: {
      globals: { ...globals.browser, ...globals.node, ...globals.es2020 },
      parserOptions: { project: './tsconfig.json' }, // Specify project for typed rules if needed
    },
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended, // Use spread for recommended configs
  eslintConfigPrettier, // Add Prettier config last
  {
    rules: {
      'prettier/prettier': 'off', // Turn off prettier rule from eslint-config-prettier if using eslint-plugin-prettier
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
    },
  },
  {
    ignores: [
      'dist/',
      'node_modules/',
      'coverage/',
      '*.config.js',
      '*.config.cjs',
      'vite.config.ts',
    ],
  },
];
