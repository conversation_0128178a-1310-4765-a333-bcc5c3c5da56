<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Find-Replace Plugin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-button {
            background: #28a745;
        }
        .test-button:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <h1>Find-Replace Plugin Debug Tool</h1>
    
    <div class="debug-panel">
        <h3>Test Actions</h3>
        <button onclick="testDirectCommand()">Test Direct Command</button>
        <button onclick="testEventDispatch()">Test Event Dispatch</button>
        <button onclick="testKeyboardShortcut()">Test Keyboard Shortcut (Ctrl+F)</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>
    
    <div class="debug-panel">
        <h3>Debug Output</h3>
        <div id="debug-output" class="debug-output"></div>
    </div>
    
    <div class="debug-panel">
        <h3>Editor Container</h3>
        <div id="editor" contenteditable="true" style="border: 1px solid #ccc; padding: 10px; min-height: 100px; background: white;">
            This is some sample text for testing the find and replace functionality. 
            You can search for words like "sample", "text", or "functionality".
        </div>
    </div>

    <script>
        let debugOutput = document.getElementById('debug-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.textContent += `[${timestamp}] ${message}\n`;
            debugOutput.scrollTop = debugOutput.scrollHeight;
        }
        
        function clearOutput() {
            debugOutput.textContent = '';
        }
        
        // Override console.log to capture debug messages
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            log(args.join(' '));
        };
        
        function testDirectCommand() {
            log('Testing direct command dispatch...');
            const event = new CustomEvent('feather:command', {
                detail: {
                    command: 'find-replace',
                    pluginId: 'find-replace',
                    source: 'debug-test'
                }
            });
            document.dispatchEvent(event);
        }
        
        function testEventDispatch() {
            log('Testing toolbar button click simulation...');
            const button = document.querySelector('[data-command="find-replace"]');
            if (button) {
                log('Found find-replace button, clicking...');
                button.click();
            } else {
                log('ERROR: Find-replace button not found in toolbar!');
                // List all toolbar buttons
                const allButtons = document.querySelectorAll('#toolbar button[data-command]');
                log(`Found ${allButtons.length} toolbar buttons:`);
                allButtons.forEach(btn => {
                    log(`  - ${btn.getAttribute('data-command')} (${btn.textContent || btn.innerHTML})`);
                });
            }
        }
        
        function testKeyboardShortcut() {
            log('Testing keyboard shortcut (Ctrl+F)...');
            const event = new KeyboardEvent('keydown', {
                key: 'f',
                ctrlKey: true,
                bubbles: true
            });
            document.dispatchEvent(event);
        }
        
        // Listen for feather:command events
        document.addEventListener('feather:command', (event) => {
            log(`Received feather:command event: ${JSON.stringify(event.detail)}`);
        });
        
        // Wait for page to load and check plugin status
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, checking plugin status...');
                
                // Check if editor is ready
                const editor = document.getElementById('editor');
                log(`Editor element found: ${!!editor}`);
                
                // Check if toolbar exists
                const toolbar = document.getElementById('toolbar');
                log(`Toolbar element found: ${!!toolbar}`);
                
                if (toolbar) {
                    const buttons = toolbar.querySelectorAll('button[data-command]');
                    log(`Toolbar buttons found: ${buttons.length}`);
                    buttons.forEach(btn => {
                        const command = btn.getAttribute('data-command');
                        log(`  - Button: ${command} (${btn.textContent || btn.innerHTML})`);
                    });
                }
                
                // Check for find-replace specific elements
                const findReplaceButton = document.querySelector('[data-command="find-replace"]');
                log(`Find-replace button found: ${!!findReplaceButton}`);
                
                // Check if any dialogs are already open
                const dialogs = document.querySelectorAll('[role="dialog"]');
                log(`Open dialogs: ${dialogs.length}`);
                
            }, 1000);
        });
    </script>
</body>
</html>
