{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationDir": "./dist", "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist", "./*.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}